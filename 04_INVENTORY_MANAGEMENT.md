# 04 - INVENTORY MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Inventory Management mengelola semua aspek persediaan dalam operasi peternakan ayam petelur, termasuk feed, medicine, equipment, supplies, dan egg inventory. Modul ini menyediakan real-time stock tracking, automated reorder points, batch management, dan comprehensive reporting.

## 🎯 TUJUAN

- Real-time inventory tracking dan monitoring
- Automated reorder point management
- Batch dan expiry date tracking
- Multi-warehouse inventory support
- Stock movement dan transfer management
- Inventory valuation dan costing
- Integration dengan purchasing dan sales
- Comprehensive inventory reporting

## ⏱️ ESTIMASI WAKTU

**Total**: 18-22 jam
- Backend implementation: 12-14 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Inventory Specialist** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Item management
items
item_categories
item_units
item_suppliers

-- Warehouse management
warehouses
warehouse_locations
warehouse_zones

-- Stock management
stock_items
stock_movements
stock_adjustments
stock_transfers

-- Batch management
item_batches
batch_tracking
expiry_management

-- Reorder management
reorder_points
reorder_suggestions
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Inventory Management module
php artisan module:make InventoryManagement

# Create module components
php artisan module:make-controller InventoryManagement ItemController --api
php artisan module:make-controller InventoryManagement WarehouseController --api
php artisan module:make-controller InventoryManagement StockController --api
php artisan module:make-controller InventoryManagement StockMovementController --api
php artisan module:make-model InventoryManagement Item
php artisan module:make-model InventoryManagement Warehouse
php artisan module:make-model InventoryManagement StockItem
php artisan module:make-model InventoryManagement StockMovement
php artisan module:make-request InventoryManagement ItemStoreRequest
php artisan module:make-request InventoryManagement StockAdjustmentRequest
php artisan module:make-resource InventoryManagement ItemResource
php artisan module:make-resource InventoryManagement StockResource
php artisan module:make-policy InventoryManagement InventoryPolicy
php artisan module:make-seeder InventoryManagement InventorySeeder
```

### **1.2 Item Model**

```php
<?php
// Modules/InventoryManagement/Entities/Item.php

namespace Modules\InventoryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Item extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'item_code',
        'item_name',
        'description',
        'category_id',
        'subcategory',
        'item_type',
        'unit_of_measure',
        'alternative_unit',
        'conversion_factor',
        'brand',
        'manufacturer',
        'specifications',
        'minimum_stock_level',
        'maximum_stock_level',
        'reorder_point',
        'reorder_quantity',
        'lead_time_days',
        'shelf_life_days',
        'storage_requirements',
        'is_batch_tracked',
        'is_expiry_tracked',
        'is_serial_tracked',
        'standard_cost',
        'average_cost',
        'last_purchase_cost',
        'selling_price',
        'is_active',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'specifications' => 'array',
        'storage_requirements' => 'array',
        'is_batch_tracked' => 'boolean',
        'is_expiry_tracked' => 'boolean',
        'is_serial_tracked' => 'boolean',
        'is_active' => 'boolean',
        'minimum_stock_level' => 'decimal:3',
        'maximum_stock_level' => 'decimal:3',
        'reorder_point' => 'decimal:3',
        'reorder_quantity' => 'decimal:3',
        'conversion_factor' => 'decimal:6',
        'standard_cost' => 'decimal:2',
        'average_cost' => 'decimal:2',
        'last_purchase_cost' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'lead_time_days' => 'integer',
        'shelf_life_days' => 'integer',
        'deleted_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['item_name', 'standard_cost', 'reorder_point', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function category()
    {
        return $this->belongsTo(ItemCategory::class, 'category_id');
    }

    public function stockItems()
    {
        return $this->hasMany(StockItem::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function batches()
    {
        return $this->hasMany(ItemBatch::class);
    }

    public function suppliers()
    {
        return $this->belongsToMany(\Modules\Purchasing\Entities\Vendor::class, 'item_suppliers')
            ->withPivot(['supplier_item_code', 'lead_time_days', 'minimum_order_quantity', 'last_purchase_price'])
            ->withTimestamps();
    }

    public function reorderPoints()
    {
        return $this->hasMany(ReorderPoint::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('item_type', $type);
    }

    public function scopeLowStock($query)
    {
        return $query->whereHas('stockItems', function ($q) {
            $q->whereRaw('quantity_on_hand <= reorder_point');
        });
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('is_expiry_tracked', true)
            ->whereHas('batches', function ($q) use ($days) {
                $q->where('expiry_date', '<=', now()->addDays($days))
                  ->where('quantity_remaining', '>', 0);
            });
    }

    // Accessors
    public function getTotalStockAttribute(): float
    {
        return $this->stockItems->sum('quantity_on_hand');
    }

    public function getTotalValueAttribute(): float
    {
        return $this->stockItems->sum(function ($stock) {
            return $stock->quantity_on_hand * $stock->unit_cost;
        });
    }

    public function getIsLowStockAttribute(): bool
    {
        return $this->total_stock <= $this->reorder_point;
    }

    public function getStockStatusAttribute(): string
    {
        if ($this->total_stock <= 0) return 'out_of_stock';
        if ($this->total_stock <= $this->reorder_point) return 'low_stock';
        if ($this->total_stock >= $this->maximum_stock_level) return 'overstock';
        return 'normal';
    }

    public function getAverageMonthlyCostAttribute(): float
    {
        $movements = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('movement_date', '>=', now()->subMonths(3))
            ->get();

        return $movements->avg('unit_cost') ?? $this->average_cost;
    }

    // Methods
    public function updateAverageCost(float $newCost, float $quantity): void
    {
        $currentValue = $this->total_stock * $this->average_cost;
        $newValue = $quantity * $newCost;
        $totalQuantity = $this->total_stock + $quantity;

        if ($totalQuantity > 0) {
            $newAverageCost = ($currentValue + $newValue) / $totalQuantity;
            $this->update(['average_cost' => $newAverageCost]);
        }
    }

    public function getStockByWarehouse(): array
    {
        return $this->stockItems()
            ->with('warehouse')
            ->get()
            ->groupBy('warehouse.name')
            ->map(function ($stocks) {
                return [
                    'quantity' => $stocks->sum('quantity_on_hand'),
                    'value' => $stocks->sum(function ($stock) {
                        return $stock->quantity_on_hand * $stock->unit_cost;
                    }),
                ];
            })
            ->toArray();
    }

    public function getExpiringBatches($days = 30): \Illuminate\Database\Eloquent\Collection
    {
        if (!$this->is_expiry_tracked) {
            return collect();
        }

        return $this->batches()
            ->where('expiry_date', '<=', now()->addDays($days))
            ->where('quantity_remaining', '>', 0)
            ->orderBy('expiry_date')
            ->get();
    }

    public function calculateReorderSuggestion(): array
    {
        $averageMonthlyUsage = $this->calculateAverageMonthlyUsage();
        $leadTimeUsage = ($averageMonthlyUsage / 30) * $this->lead_time_days;
        $safetyStock = $leadTimeUsage * 0.2; // 20% safety stock
        
        $suggestedReorderPoint = $leadTimeUsage + $safetyStock;
        $suggestedOrderQuantity = max(
            $this->reorder_quantity,
            $averageMonthlyUsage - $this->total_stock
        );

        return [
            'current_stock' => $this->total_stock,
            'average_monthly_usage' => $averageMonthlyUsage,
            'lead_time_usage' => $leadTimeUsage,
            'safety_stock' => $safetyStock,
            'suggested_reorder_point' => $suggestedReorderPoint,
            'suggested_order_quantity' => $suggestedOrderQuantity,
            'needs_reorder' => $this->total_stock <= $suggestedReorderPoint,
        ];
    }

    private function calculateAverageMonthlyUsage(): float
    {
        $movements = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('movement_date', '>=', now()->subMonths(6))
            ->get();

        if ($movements->isEmpty()) {
            return 0;
        }

        $totalUsage = $movements->sum('quantity');
        $months = $movements->first()->movement_date->diffInMonths(now());
        
        return $months > 0 ? $totalUsage / $months : $totalUsage;
    }

    public static function generateItemCode($categoryCode = 'ITM'): string
    {
        $year = now()->year;
        $prefix = "{$categoryCode}{$year}";
        
        $lastItem = static::where('item_code', 'like', $prefix . '%')
            ->orderBy('item_code', 'desc')
            ->first();
        
        if ($lastItem) {
            $lastNumber = (int) substr($lastItem->item_code, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.3 Stock Item Model**

```php
<?php
// Modules/InventoryManagement/Entities/StockItem.php

namespace Modules\InventoryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class StockItem extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'item_id',
        'warehouse_id',
        'location_code',
        'quantity_on_hand',
        'quantity_reserved',
        'quantity_available',
        'unit_cost',
        'total_value',
        'last_movement_date',
        'last_count_date',
        'cycle_count_due_date',
        'notes',
    ];

    protected $casts = [
        'quantity_on_hand' => 'decimal:3',
        'quantity_reserved' => 'decimal:3',
        'quantity_available' => 'decimal:3',
        'unit_cost' => 'decimal:2',
        'total_value' => 'decimal:2',
        'last_movement_date' => 'datetime',
        'last_count_date' => 'datetime',
        'cycle_count_due_date' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['quantity_on_hand', 'unit_cost', 'total_value'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function movements()
    {
        return $this->hasMany(StockMovement::class, 'stock_item_id');
    }

    // Accessors
    public function getQuantityAvailableAttribute(): float
    {
        return $this->quantity_on_hand - $this->quantity_reserved;
    }

    public function getTotalValueAttribute(): float
    {
        return $this->quantity_on_hand * $this->unit_cost;
    }

    public function getIsCycleCountDueAttribute(): bool
    {
        return $this->cycle_count_due_date && $this->cycle_count_due_date <= now();
    }

    public function getTurnoverRateAttribute(): float
    {
        $movements = $this->movements()
            ->where('movement_type', 'out')
            ->where('movement_date', '>=', now()->subYear())
            ->sum('quantity');

        return $this->quantity_on_hand > 0 ? $movements / $this->quantity_on_hand : 0;
    }

    // Methods
    public function adjustQuantity(float $quantity, string $reason, ?string $reference = null): StockMovement
    {
        $movementType = $quantity > 0 ? 'in' : 'out';
        $absQuantity = abs($quantity);

        // Create stock movement
        $movement = $this->movements()->create([
            'uuid' => \Str::uuid(),
            'item_id' => $this->item_id,
            'warehouse_id' => $this->warehouse_id,
            'movement_type' => $movementType,
            'movement_date' => now(),
            'quantity' => $absQuantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $absQuantity * $this->unit_cost,
            'reason' => $reason,
            'reference_type' => 'adjustment',
            'reference_id' => $reference,
            'created_by' => auth()->id(),
        ]);

        // Update stock quantities
        $this->increment('quantity_on_hand', $quantity);
        $this->update([
            'total_value' => $this->quantity_on_hand * $this->unit_cost,
            'last_movement_date' => now(),
        ]);

        return $movement;
    }

    public function reserveQuantity(float $quantity): bool
    {
        if ($quantity > $this->quantity_available) {
            return false;
        }

        $this->increment('quantity_reserved', $quantity);
        return true;
    }

    public function releaseReservation(float $quantity): bool
    {
        if ($quantity > $this->quantity_reserved) {
            return false;
        }

        $this->decrement('quantity_reserved', $quantity);
        return true;
    }

    public function transferTo(Warehouse $targetWarehouse, float $quantity, string $reason): ?StockMovement
    {
        if ($quantity > $this->quantity_available) {
            return null;
        }

        // Create outbound movement
        $outMovement = $this->movements()->create([
            'uuid' => \Str::uuid(),
            'item_id' => $this->item_id,
            'warehouse_id' => $this->warehouse_id,
            'movement_type' => 'transfer_out',
            'movement_date' => now(),
            'quantity' => $quantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $quantity * $this->unit_cost,
            'reason' => $reason,
            'reference_type' => 'transfer',
            'target_warehouse_id' => $targetWarehouse->id,
            'created_by' => auth()->id(),
        ]);

        // Update source stock
        $this->decrement('quantity_on_hand', $quantity);
        $this->update([
            'total_value' => $this->quantity_on_hand * $this->unit_cost,
            'last_movement_date' => now(),
        ]);

        // Create or update target stock
        $targetStock = StockItem::firstOrCreate([
            'item_id' => $this->item_id,
            'warehouse_id' => $targetWarehouse->id,
        ], [
            'uuid' => \Str::uuid(),
            'quantity_on_hand' => 0,
            'quantity_reserved' => 0,
            'unit_cost' => $this->unit_cost,
        ]);

        // Create inbound movement
        $targetStock->movements()->create([
            'uuid' => \Str::uuid(),
            'item_id' => $this->item_id,
            'warehouse_id' => $targetWarehouse->id,
            'movement_type' => 'transfer_in',
            'movement_date' => now(),
            'quantity' => $quantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $quantity * $this->unit_cost,
            'reason' => $reason,
            'reference_type' => 'transfer',
            'source_warehouse_id' => $this->warehouse_id,
            'created_by' => auth()->id(),
        ]);

        // Update target stock
        $targetStock->increment('quantity_on_hand', $quantity);
        $targetStock->update([
            'total_value' => $targetStock->quantity_on_hand * $targetStock->unit_cost,
            'last_movement_date' => now(),
        ]);

        return $outMovement;
    }

    public function performCycleCount(float $countedQuantity, ?string $notes = null): array
    {
        $variance = $countedQuantity - $this->quantity_on_hand;
        
        if ($variance != 0) {
            $this->adjustQuantity($variance, 'Cycle count adjustment', $notes);
        }

        $this->update([
            'last_count_date' => now(),
            'cycle_count_due_date' => now()->addDays(90), // Next count in 90 days
        ]);

        return [
            'book_quantity' => $this->quantity_on_hand - $variance,
            'counted_quantity' => $countedQuantity,
            'variance' => $variance,
            'variance_percentage' => $this->quantity_on_hand > 0 ? ($variance / $this->quantity_on_hand) * 100 : 0,
        ];
    }
}
```

### **1.4 Inventory Service**

```php
<?php
// Modules/InventoryManagement/Services/InventoryService.php

namespace Modules\InventoryManagement\Services;

use Modules\InventoryManagement\Entities\Item;
use Modules\InventoryManagement\Entities\StockItem;
use Modules\InventoryManagement\Entities\StockMovement;
use Modules\InventoryManagement\Entities\Warehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class InventoryService
{
    public function createItem(array $data): Item
    {
        return DB::transaction(function () use ($data) {
            $itemData = array_merge($data, [
                'uuid' => Str::uuid(),
                'item_code' => $data['item_code'] ?? Item::generateItemCode(),
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            $item = Item::create($itemData);

            // Create initial stock records for all warehouses if specified
            if (isset($data['initial_stock'])) {
                foreach ($data['initial_stock'] as $warehouseStock) {
                    $this->createInitialStock($item, $warehouseStock);
                }
            }

            return $item;
        });
    }

    private function createInitialStock(Item $item, array $stockData): StockItem
    {
        $stock = StockItem::create([
            'uuid' => Str::uuid(),
            'item_id' => $item->id,
            'warehouse_id' => $stockData['warehouse_id'],
            'quantity_on_hand' => $stockData['quantity'],
            'quantity_reserved' => 0,
            'unit_cost' => $stockData['unit_cost'],
            'location_code' => $stockData['location_code'] ?? null,
        ]);

        // Create initial stock movement
        StockMovement::create([
            'uuid' => Str::uuid(),
            'stock_item_id' => $stock->id,
            'item_id' => $item->id,
            'warehouse_id' => $stock->warehouse_id,
            'movement_type' => 'in',
            'movement_date' => now(),
            'quantity' => $stockData['quantity'],
            'unit_cost' => $stockData['unit_cost'],
            'total_cost' => $stockData['quantity'] * $stockData['unit_cost'],
            'reason' => 'Initial stock',
            'reference_type' => 'initial',
            'created_by' => auth()->id(),
        ]);

        return $stock;
    }

    public function receiveStock(array $data): StockMovement
    {
        return DB::transaction(function () use ($data) {
            $stock = StockItem::firstOrCreate([
                'item_id' => $data['item_id'],
                'warehouse_id' => $data['warehouse_id'],
            ], [
                'uuid' => Str::uuid(),
                'quantity_on_hand' => 0,
                'quantity_reserved' => 0,
                'unit_cost' => $data['unit_cost'],
            ]);

            // Update average cost
            $item = $stock->item;
            $item->updateAverageCost($data['unit_cost'], $data['quantity']);

            // Create stock movement
            $movement = StockMovement::create([
                'uuid' => Str::uuid(),
                'stock_item_id' => $stock->id,
                'item_id' => $data['item_id'],
                'warehouse_id' => $data['warehouse_id'],
                'movement_type' => 'in',
                'movement_date' => $data['movement_date'] ?? now(),
                'quantity' => $data['quantity'],
                'unit_cost' => $data['unit_cost'],
                'total_cost' => $data['quantity'] * $data['unit_cost'],
                'reason' => $data['reason'] ?? 'Stock receipt',
                'reference_type' => $data['reference_type'] ?? null,
                'reference_id' => $data['reference_id'] ?? null,
                'batch_number' => $data['batch_number'] ?? null,
                'expiry_date' => $data['expiry_date'] ?? null,
                'created_by' => auth()->id(),
            ]);

            // Update stock quantities
            $stock->increment('quantity_on_hand', $data['quantity']);
            $stock->update([
                'unit_cost' => $item->average_cost,
                'total_value' => $stock->quantity_on_hand * $item->average_cost,
                'last_movement_date' => now(),
            ]);

            return $movement;
        });
    }

    public function issueStock(array $data): StockMovement
    {
        return DB::transaction(function () use ($data) {
            $stock = StockItem::where('item_id', $data['item_id'])
                ->where('warehouse_id', $data['warehouse_id'])
                ->first();

            if (!$stock || $stock->quantity_available < $data['quantity']) {
                throw new \Exception('Insufficient stock available');
            }

            // Create stock movement
            $movement = StockMovement::create([
                'uuid' => Str::uuid(),
                'stock_item_id' => $stock->id,
                'item_id' => $data['item_id'],
                'warehouse_id' => $data['warehouse_id'],
                'movement_type' => 'out',
                'movement_date' => $data['movement_date'] ?? now(),
                'quantity' => $data['quantity'],
                'unit_cost' => $stock->unit_cost,
                'total_cost' => $data['quantity'] * $stock->unit_cost,
                'reason' => $data['reason'] ?? 'Stock issue',
                'reference_type' => $data['reference_type'] ?? null,
                'reference_id' => $data['reference_id'] ?? null,
                'created_by' => auth()->id(),
            ]);

            // Update stock quantities
            $stock->decrement('quantity_on_hand', $data['quantity']);
            $stock->update([
                'total_value' => $stock->quantity_on_hand * $stock->unit_cost,
                'last_movement_date' => now(),
            ]);

            return $movement;
        });
    }

    public function transferStock(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $sourceStock = StockItem::where('item_id', $data['item_id'])
                ->where('warehouse_id', $data['source_warehouse_id'])
                ->first();

            if (!$sourceStock || $sourceStock->quantity_available < $data['quantity']) {
                throw new \Exception('Insufficient stock available for transfer');
            }

            $targetWarehouse = Warehouse::findOrFail($data['target_warehouse_id']);
            
            $movement = $sourceStock->transferTo(
                $targetWarehouse,
                $data['quantity'],
                $data['reason'] ?? 'Stock transfer'
            );

            return [
                'transfer_movement' => $movement,
                'source_stock' => $sourceStock->fresh(),
                'target_stock' => StockItem::where('item_id', $data['item_id'])
                    ->where('warehouse_id', $data['target_warehouse_id'])
                    ->first(),
            ];
        });
    }

    public function adjustStock(array $data): StockMovement
    {
        return DB::transaction(function () use ($data) {
            $stock = StockItem::where('item_id', $data['item_id'])
                ->where('warehouse_id', $data['warehouse_id'])
                ->first();

            if (!$stock) {
                throw new \Exception('Stock item not found');
            }

            $adjustmentQuantity = $data['new_quantity'] - $stock->quantity_on_hand;
            
            return $stock->adjustQuantity(
                $adjustmentQuantity,
                $data['reason'] ?? 'Stock adjustment',
                $data['reference']
            );
        });
    }

    public function getInventoryValuation(array $filters = []): array
    {
        $query = StockItem::with(['item', 'warehouse']);

        if (isset($filters['warehouse_id'])) {
            $query->where('warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['category_id'])) {
            $query->whereHas('item', function ($q) use ($filters) {
                $q->where('category_id', $filters['category_id']);
            });
        }

        $stocks = $query->get();

        return [
            'total_items' => $stocks->count(),
            'total_quantity' => $stocks->sum('quantity_on_hand'),
            'total_value' => $stocks->sum('total_value'),
            'by_warehouse' => $stocks->groupBy('warehouse.name')->map(function ($warehouseStocks) {
                return [
                    'items_count' => $warehouseStocks->count(),
                    'total_quantity' => $warehouseStocks->sum('quantity_on_hand'),
                    'total_value' => $warehouseStocks->sum('total_value'),
                ];
            }),
            'by_category' => $stocks->groupBy('item.category.name')->map(function ($categoryStocks) {
                return [
                    'items_count' => $categoryStocks->count(),
                    'total_quantity' => $categoryStocks->sum('quantity_on_hand'),
                    'total_value' => $categoryStocks->sum('total_value'),
                ];
            }),
        ];
    }

    public function getLowStockItems(): \Illuminate\Database\Eloquent\Collection
    {
        return Item::with(['stockItems.warehouse'])
            ->lowStock()
            ->get()
            ->map(function ($item) {
                return [
                    'item' => $item,
                    'total_stock' => $item->total_stock,
                    'reorder_point' => $item->reorder_point,
                    'reorder_suggestion' => $item->calculateReorderSuggestion(),
                ];
            });
    }

    public function getExpiringItems($days = 30): \Illuminate\Database\Eloquent\Collection
    {
        return Item::expiringSoon($days)
            ->with(['batches' => function ($query) use ($days) {
                $query->where('expiry_date', '<=', now()->addDays($days))
                      ->where('quantity_remaining', '>', 0)
                      ->orderBy('expiry_date');
            }])
            ->get();
    }

    public function generateReorderReport(): array
    {
        $lowStockItems = $this->getLowStockItems();
        $expiringItems = $this->getExpiringItems();

        return [
            'low_stock_items' => $lowStockItems,
            'expiring_items' => $expiringItems,
            'reorder_suggestions' => $lowStockItems->map(function ($item) {
                return $item['reorder_suggestion'];
            }),
            'summary' => [
                'low_stock_count' => $lowStockItems->count(),
                'expiring_count' => $expiringItems->count(),
                'total_reorder_value' => $lowStockItems->sum(function ($item) {
                    return $item['reorder_suggestion']['suggested_order_quantity'] * $item['item']->last_purchase_cost;
                }),
            ],
        ];
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Inventory Dashboard**

```typescript
// frontend/src/modules/inventory/components/InventoryDashboard.tsx
import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Progress,
  Alert,
  Button,
  Space,
} from 'antd';
import {
  WarningOutlined,
  ExclamationCircleOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { inventoryService } from '../services/inventoryService';

const InventoryDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [inventoryData, setInventoryData] = useState<any>({});
  const [lowStockItems, setLowStockItems] = useState([]);
  const [expiringItems, setExpiringItems] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [valuation, lowStock, expiring] = await Promise.all([
        inventoryService.getInventoryValuation(),
        inventoryService.getLowStockItems(),
        inventoryService.getExpiringItems(),
      ]);

      setInventoryData(valuation.data);
      setLowStockItems(lowStock.data);
      setExpiringItems(expiring.data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const lowStockColumns = [
    {
      title: 'Item',
      dataIndex: ['item', 'item_name'],
      key: 'item_name',
    },
    {
      title: 'Current Stock',
      dataIndex: 'total_stock',
      key: 'total_stock',
      render: (stock: number) => stock?.toFixed(2),
    },
    {
      title: 'Reorder Point',
      dataIndex: 'reorder_point',
      key: 'reorder_point',
      render: (point: number) => point?.toFixed(2),
    },
    {
      title: 'Status',
      key: 'status',
      render: (record: any) => {
        const percentage = (record.total_stock / record.reorder_point) * 100;
        return (
          <Progress
            percent={Math.min(percentage, 100)}
            status={percentage < 50 ? 'exception' : percentage < 100 ? 'active' : 'success'}
            size="small"
          />
        );
      },
    },
    {
      title: 'Suggested Order',
      dataIndex: ['reorder_suggestion', 'suggested_order_quantity'],
      key: 'suggested_order',
      render: (quantity: number) => quantity?.toFixed(2),
    },
  ];

  const expiringColumns = [
    {
      title: 'Item',
      dataIndex: 'item_name',
      key: 'item_name',
    },
    {
      title: 'Batch',
      dataIndex: ['batches', 0, 'batch_number'],
      key: 'batch_number',
    },
    {
      title: 'Expiry Date',
      dataIndex: ['batches', 0, 'expiry_date'],
      key: 'expiry_date',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Days to Expiry',
      dataIndex: ['batches', 0, 'expiry_date'],
      key: 'days_to_expiry',
      render: (date: string) => {
        const days = Math.ceil((new Date(date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        return (
          <Tag color={days <= 7 ? 'red' : days <= 30 ? 'orange' : 'green'}>
            {days} days
          </Tag>
        );
      },
    },
    {
      title: 'Quantity',
      dataIndex: ['batches', 0, 'quantity_remaining'],
      key: 'quantity',
      render: (quantity: number) => quantity?.toFixed(2),
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Items"
              value={inventoryData.total_items}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Value"
              value={inventoryData.total_value}
              prefix={<DollarOutlined />}
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Low Stock Items"
              value={lowStockItems.length}
              prefix={<WarningOutlined />}
              valueStyle={{ color: lowStockItems.length > 0 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Expiring Soon"
              value={expiringItems.length}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: expiringItems.length > 0 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      {lowStockItems.length > 0 && (
        <Alert
          message="Low Stock Alert"
          description={`${lowStockItems.length} items are below reorder point and need restocking.`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" type="primary">
              Generate Purchase Orders
            </Button>
          }
        />
      )}

      {expiringItems.length > 0 && (
        <Alert
          message="Expiring Items Alert"
          description={`${expiringItems.length} items are expiring within 30 days.`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" type="primary">
              View Expiring Items
            </Button>
          }
        />
      )}

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card
            title="Low Stock Items"
            extra={
              <Button type="link" onClick={() => {}}>
                View All
              </Button>
            }
          >
            <Table
              columns={lowStockColumns}
              dataSource={lowStockItems.slice(0, 5)}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title="Expiring Items"
            extra={
              <Button type="link" onClick={() => {}}>
                View All
              </Button>
            }
          >
            <Table
              columns={expiringColumns}
              dataSource={expiringItems.slice(0, 5)}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default InventoryDashboard;
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Item management CRUD working
- [ ] Stock tracking accurate
- [ ] Stock movements recorded
- [ ] Reorder point calculations
- [ ] Batch tracking functional
- [ ] Warehouse management working

### **Frontend**
- [ ] Inventory dashboard functional
- [ ] Stock management interface
- [ ] Low stock alerts working
- [ ] Expiry tracking display
- [ ] Stock movement recording
- [ ] Responsive design

### **Integration**
- [ ] Purchase integration working
- [ ] Sales integration working
- [ ] Cost calculations accurate
- [ ] Reporting functional
- [ ] User permissions working

## 📞 NEXT STEPS

Setelah Inventory Management module selesai:

1. **Test stock operations** end-to-end
2. **Verify calculations** accuracy
3. **Test reorder suggestions**
4. **Validate batch tracking**
5. **Commit module** ke repository
6. **Lanjut ke** `05_ACCOUNTING_SYSTEM.md`

---

**IMPORTANT**: Inventory Management adalah operational backbone. Pastikan stock tracking accurate, cost calculations correct, dan integration dengan purchasing/sales working seamlessly untuk business continuity.
