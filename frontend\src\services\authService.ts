import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
}

export interface User {
  id: number;
  uuid: string;
  employee_id: string;
  name: string;
  email: string;
  phone?: string;
  avatar_url?: string;
  status: string;
  is_active: boolean;
  is_online: boolean;
  roles: Array<{
    id: number;
    name: string;
    display_name: string;
    description?: string;
  }>;
  permissions: Array<{
    id: number;
    name: string;
    display_name: string;
    description?: string;
  }>;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  message: string;
  user: User;
  token: string;
  token_type: string;
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<{ data: AuthResponse }> {
    const response = await apiClient.post('/auth/login', credentials);
    return response;
  },

  async register(userData: RegisterData): Promise<{ data: AuthResponse }> {
    const response = await apiClient.post('/auth/register', userData);
    return response;
  },

  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
  },

  async me(): Promise<{ data: { user: User } }> {
    const response = await apiClient.get('/auth/me');
    return response;
  },

  async refreshToken(): Promise<{ data: { token: string; token_type: string } }> {
    const response = await apiClient.post('/auth/refresh');
    return response;
  },

  async forgotPassword(email: string): Promise<{ data: { message: string } }> {
    const response = await apiClient.post('/auth/forgot-password', { email });
    return response;
  },

  async resetPassword(data: {
    token: string;
    email: string;
    password: string;
    password_confirmation: string;
  }): Promise<{ data: { message: string } }> {
    const response = await apiClient.post('/auth/reset-password', data);
    return response;
  },
};

export { apiClient };
