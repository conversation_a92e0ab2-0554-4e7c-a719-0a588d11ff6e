<?php

namespace Modules\UserManagement\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use Modules\UserManagement\Http\Requests\LoginRequest;
use Modules\UserManagement\Http\Requests\RegisterRequest;
use Modules\UserManagement\Http\Resources\UserResource;

class AuthController extends Controller
{
    public function login(LoginRequest $request): JsonResponse
    {
        $this->ensureIsNotRateLimited($request);

        $credentials = $request->only('email', 'password');

        if (!Auth::attempt($credentials, $request->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey($request));

            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = Auth::user();
        
        // Check if user is active
        if (!$user->is_active) {
            Auth::logout();
            throw ValidationException::withMessages([
                'email' => ['Your account has been deactivated.'],
            ]);
        }

        // Update last login
        $user->updateLastLogin();

        // Create token
        $token = $user->createToken('auth-token', $this->getTokenAbilities($user))->plainTextToken;

        RateLimiter::clear($this->throttleKey($request));

        return response()->json([
            'message' => 'Login successful',
            'user' => new UserResource($user->load(['roles', 'permissions'])),
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $userData = $request->validated();
        $userData['password'] = Hash::make($userData['password']);
        $userData['status'] = 'active';

        $user = User::create($userData);

        // Assign default role
        $user->assignRole('farm_worker');

        // Create token
        $token = $user->createToken('auth-token', $this->getTokenAbilities($user))->plainTextToken;

        return response()->json([
            'message' => 'Registration successful',
            'user' => new UserResource($user->load(['roles', 'permissions'])),
            'token' => $token,
            'token_type' => 'Bearer',
        ], 201);
    }

    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logout successful',
        ]);
    }

    public function me(Request $request): JsonResponse
    {
        $user = $request->user()->load(['roles', 'permissions']);

        return response()->json([
            'user' => new UserResource($user),
        ]);
    }

    public function refreshToken(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Revoke current token
        $request->user()->currentAccessToken()->delete();
        
        // Create new token
        $token = $user->createToken('auth-token', $this->getTokenAbilities($user))->plainTextToken;

        return response()->json([
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    private function ensureIsNotRateLimited(Request $request): void
    {
        if (RateLimiter::tooManyAttempts($this->throttleKey($request), 5)) {
            $seconds = RateLimiter::availableIn($this->throttleKey($request));

            throw ValidationException::withMessages([
                'email' => ["Too many login attempts. Please try again in {$seconds} seconds."],
            ]);
        }
    }

    private function throttleKey(Request $request): string
    {
        return strtolower($request->input('email')) . '|' . $request->ip();
    }

    private function getTokenAbilities(User $user): array
    {
        $abilities = ['*']; // Default abilities

        // Add role-specific abilities
        if ($user->hasRole('super_admin')) {
            $abilities = ['*'];
        } elseif ($user->hasRole('farm_manager')) {
            $abilities = ['farm:*', 'house:*', 'flock:*', 'production:*'];
        } elseif ($user->hasRole('farm_worker')) {
            $abilities = ['production:read', 'production:create', 'flock:read'];
        }

        return $abilities;
    }
}
