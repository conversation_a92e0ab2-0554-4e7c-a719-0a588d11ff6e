# 10 - FEED MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Feed Management mengelola seluruh aspek manajemen pakan dari feed formulation, procurement, storage, hingga distribution ke flocks. Modul ini terintegrasi dengan inventory untuk stock management, purchasing untuk procurement, dan production untuk feed consumption tracking.

## 🎯 TUJUAN

- Feed formulation management dengan nutritional requirements
- Feed procurement dan vendor management
- Feed inventory dengan batch tracking dan expiry management
- Daily feed distribution dan consumption recording
- Feed conversion ratio (FCR) analysis
- Cost per kg calculation dan feed cost optimization
- Integration dengan production untuk performance correlation

## ⏱️ ESTIMASI WAKTU

**Total**: 20-24 jam
- Backend implementation: 14-16 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Nutrition Specialist** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Feed formulation
feed_formulations
feed_formulation_ingredients
feed_ingredients
nutritional_requirements

-- Feed management
feed_batches
feed_inventory
feed_consumption_records
feed_distribution_records

-- Supporting tables
flocks (dari Poultry Management)
vendors (dari Purchasing)
items (dari Inventory)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Feed Management module
php artisan module:make FeedManagement

# Create module components
php artisan module:make-controller FeedManagement FeedFormulationController --api
php artisan module:make-controller FeedManagement FeedInventoryController --api
php artisan module:make-controller FeedManagement FeedConsumptionController --api
php artisan module:make-controller FeedManagement FeedDistributionController --api
php artisan module:make-controller FeedManagement FeedReportController --api
php artisan module:make-model FeedManagement FeedFormulation
php artisan module:make-model FeedManagement FeedBatch
php artisan module:make-model FeedManagement FeedInventory
php artisan module:make-model FeedManagement FeedConsumptionRecord
php artisan module:make-request FeedManagement FeedFormulationStoreRequest
php artisan module:make-request FeedManagement FeedConsumptionStoreRequest
php artisan module:make-resource FeedManagement FeedFormulationResource
php artisan module:make-resource FeedManagement FeedInventoryResource
php artisan module:make-policy FeedManagement FeedManagementPolicy
php artisan module:make-seeder FeedManagement FeedManagementSeeder
```

### **1.2 Feed Formulation Model**

```php
<?php
// Modules/FeedManagement/Entities/FeedFormulation.php

namespace Modules\FeedManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class FeedFormulation extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'formulation_code',
        'formulation_name',
        'description',
        'target_species',
        'life_stage',
        'feed_type',
        'protein_percentage',
        'energy_kcal_kg',
        'fat_percentage',
        'fiber_percentage',
        'ash_percentage',
        'moisture_percentage',
        'calcium_percentage',
        'phosphorus_percentage',
        'lysine_percentage',
        'methionine_percentage',
        'cost_per_kg',
        'batch_size_kg',
        'mixing_instructions',
        'quality_standards',
        'shelf_life_days',
        'storage_requirements',
        'notes',
        'status',
        'approved_by',
        'approved_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'protein_percentage' => 'decimal:2',
        'energy_kcal_kg' => 'decimal:2',
        'fat_percentage' => 'decimal:2',
        'fiber_percentage' => 'decimal:2',
        'ash_percentage' => 'decimal:2',
        'moisture_percentage' => 'decimal:2',
        'calcium_percentage' => 'decimal:2',
        'phosphorus_percentage' => 'decimal:2',
        'lysine_percentage' => 'decimal:2',
        'methionine_percentage' => 'decimal:2',
        'cost_per_kg' => 'decimal:2',
        'batch_size_kg' => 'decimal:2',
        'shelf_life_days' => 'integer',
        'quality_standards' => 'array',
        'storage_requirements' => 'array',
        'approved_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['formulation_name', 'status', 'cost_per_kg', 'protein_percentage'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function ingredients()
    {
        return $this->hasMany(FeedFormulationIngredient::class, 'formulation_id');
    }

    public function batches()
    {
        return $this->hasMany(FeedBatch::class, 'formulation_id');
    }

    public function approvedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeByLifeStage($query, $lifeStage)
    {
        return $query->where('life_stage', $lifeStage);
    }

    public function scopeByFeedType($query, $feedType)
    {
        return $query->where('feed_type', $feedType);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('formulation_name', 'like', "%{$search}%")
              ->orWhere('formulation_code', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    // Accessors
    public function getIsApprovedAttribute(): bool
    {
        return $this->status === 'approved';
    }

    public function getCanBeApprovedAttribute(): bool
    {
        return $this->status === 'pending' && $this->ingredients->count() > 0;
    }

    public function getTotalIngredientPercentageAttribute(): float
    {
        return $this->ingredients->sum('percentage');
    }

    public function getIsBalancedAttribute(): bool
    {
        return abs($this->total_ingredient_percentage - 100) < 0.01;
    }

    // Methods
    public function calculateNutritionalValues(): void
    {
        $totalProtein = 0;
        $totalEnergy = 0;
        $totalFat = 0;
        $totalFiber = 0;
        $totalCalcium = 0;
        $totalPhosphorus = 0;
        $totalCost = 0;

        foreach ($this->ingredients as $ingredient) {
            $percentage = $ingredient->percentage / 100;
            
            $totalProtein += $ingredient->feedIngredient->protein_percentage * $percentage;
            $totalEnergy += $ingredient->feedIngredient->energy_kcal_kg * $percentage;
            $totalFat += $ingredient->feedIngredient->fat_percentage * $percentage;
            $totalFiber += $ingredient->feedIngredient->fiber_percentage * $percentage;
            $totalCalcium += $ingredient->feedIngredient->calcium_percentage * $percentage;
            $totalPhosphorus += $ingredient->feedIngredient->phosphorus_percentage * $percentage;
            $totalCost += $ingredient->feedIngredient->cost_per_kg * $percentage;
        }

        $this->update([
            'protein_percentage' => $totalProtein,
            'energy_kcal_kg' => $totalEnergy,
            'fat_percentage' => $totalFat,
            'fiber_percentage' => $totalFiber,
            'calcium_percentage' => $totalCalcium,
            'phosphorus_percentage' => $totalPhosphorus,
            'cost_per_kg' => $totalCost,
        ]);
    }

    public function approve(): bool
    {
        if (!$this->can_be_approved) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => auth()->id(),
            'approved_at' => now(),
        ]);

        return true;
    }

    public function getIngredientList(): array
    {
        return $this->ingredients->map(function ($ingredient) {
            return [
                'ingredient_name' => $ingredient->feedIngredient->name,
                'percentage' => $ingredient->percentage,
                'quantity_kg' => ($ingredient->percentage / 100) * $this->batch_size_kg,
                'cost_per_kg' => $ingredient->feedIngredient->cost_per_kg,
                'total_cost' => (($ingredient->percentage / 100) * $this->batch_size_kg) * $ingredient->feedIngredient->cost_per_kg,
            ];
        })->toArray();
    }

    public static function generateFormulationCode(): string
    {
        $year = now()->year;
        $prefix = "FORM{$year}";
        
        $lastFormulation = static::where('formulation_code', 'like', $prefix . '%')
            ->orderBy('formulation_code', 'desc')
            ->first();
        
        if ($lastFormulation) {
            $lastNumber = (int) substr($lastFormulation->formulation_code, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.3 Feed Consumption Record Model**

```php
<?php
// Modules/FeedManagement/Entities/FeedConsumptionRecord.php

namespace Modules\FeedManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class FeedConsumptionRecord extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'consumption_date',
        'flock_id',
        'house_id',
        'feed_batch_id',
        'formulation_id',
        'quantity_kg',
        'feeding_time',
        'feed_type',
        'bird_count',
        'consumption_per_bird',
        'feed_conversion_ratio',
        'leftover_kg',
        'wastage_kg',
        'feed_quality_score',
        'weather_condition',
        'temperature',
        'humidity',
        'notes',
        'recorded_by',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'consumption_date' => 'date',
        'feeding_time' => 'time',
        'quantity_kg' => 'decimal:2',
        'bird_count' => 'integer',
        'consumption_per_bird' => 'decimal:3',
        'feed_conversion_ratio' => 'decimal:3',
        'leftover_kg' => 'decimal:2',
        'wastage_kg' => 'decimal:2',
        'feed_quality_score' => 'decimal:1',
        'temperature' => 'decimal:1',
        'humidity' => 'decimal:1',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['quantity_kg', 'feed_conversion_ratio', 'wastage_kg'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function flock()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\Flock::class);
    }

    public function house()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\House::class);
    }

    public function feedBatch()
    {
        return $this->belongsTo(FeedBatch::class, 'feed_batch_id');
    }

    public function formulation()
    {
        return $this->belongsTo(FeedFormulation::class, 'formulation_id');
    }

    public function recordedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'recorded_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('consumption_date', [$startDate, $endDate]);
    }

    public function scopeByFlock($query, $flockId)
    {
        return $query->where('flock_id', $flockId);
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeByFormulation($query, $formulationId)
    {
        return $query->where('formulation_id', $formulationId);
    }

    public function scopeToday($query)
    {
        return $query->where('consumption_date', now()->toDateString());
    }

    // Accessors
    public function getEffectiveConsumptionAttribute(): float
    {
        return $this->quantity_kg - $this->leftover_kg - $this->wastage_kg;
    }

    public function getWastagePercentageAttribute(): float
    {
        return $this->quantity_kg > 0 ? ($this->wastage_kg / $this->quantity_kg) * 100 : 0;
    }

    public function getFeedCostAttribute(): float
    {
        return $this->formulation ? $this->quantity_kg * $this->formulation->cost_per_kg : 0;
    }

    public function getFeedEfficiencyAttribute(): float
    {
        // Feed efficiency = kg feed per kg egg mass
        $eggProduction = \Modules\EggProduction\Entities\EggProductionRecord::where('flock_id', $this->flock_id)
            ->where('record_date', $this->consumption_date)
            ->first();

        if ($eggProduction && $eggProduction->egg_mass > 0) {
            return $this->effective_consumption / $eggProduction->egg_mass;
        }

        return 0;
    }

    // Methods
    public function calculateFCR(): void
    {
        // Get egg production for the same date and flock
        $eggProduction = \Modules\EggProduction\Entities\EggProductionRecord::where('flock_id', $this->flock_id)
            ->where('record_date', $this->consumption_date)
            ->first();

        if ($eggProduction && $eggProduction->eggs_collected > 0) {
            $fcr = $this->effective_consumption / $eggProduction->eggs_collected;
            $this->update(['feed_conversion_ratio' => $fcr]);
        }
    }

    public function updateInventory(): void
    {
        if ($this->feedBatch) {
            $this->feedBatch->decrement('quantity_available', $this->quantity_kg);
            
            // Update feed inventory
            $inventory = FeedInventory::where('batch_id', $this->feed_batch_id)->first();
            if ($inventory) {
                $inventory->decrement('quantity_available', $this->quantity_kg);
                $inventory->increment('quantity_consumed', $this->quantity_kg);
            }
        }
    }

    public static function getDailyConsumption(string $date = null): array
    {
        $date = $date ?: now()->toDateString();
        
        $records = static::with(['flock', 'house', 'formulation'])
            ->where('consumption_date', $date)
            ->get();

        return [
            'date' => $date,
            'total_consumption_kg' => $records->sum('quantity_kg'),
            'total_effective_consumption_kg' => $records->sum('effective_consumption'),
            'total_wastage_kg' => $records->sum('wastage_kg'),
            'total_cost' => $records->sum('feed_cost'),
            'average_fcr' => $records->avg('feed_conversion_ratio'),
            'average_consumption_per_bird' => $records->avg('consumption_per_bird'),
            'average_wastage_percentage' => $records->avg('wastage_percentage'),
            'houses_fed' => $records->count(),
            'flocks_fed' => $records->pluck('flock_id')->unique()->count(),
        ];
    }
}
```

### **1.4 Feed Management Service**

```php
<?php
// Modules/FeedManagement/Services/FeedManagementService.php

namespace Modules\FeedManagement\Services;

use Modules\FeedManagement\Entities\FeedFormulation;
use Modules\FeedManagement\Entities\FeedBatch;
use Modules\FeedManagement\Entities\FeedConsumptionRecord;
use Modules\FeedManagement\Entities\FeedInventory;
use Modules\FeedManagement\Events\FeedFormulationApproved;
use Modules\FeedManagement\Events\FeedBatchCreated;
use Modules\FeedManagement\Events\FeedConsumed;
use Modules\Inventory\Services\StockService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FeedManagementService
{
    protected StockService $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    public function createFeedFormulation(array $data): FeedFormulation
    {
        return DB::transaction(function () use ($data) {
            $formulation = FeedFormulation::create([
                'uuid' => Str::uuid(),
                'formulation_code' => FeedFormulation::generateFormulationCode(),
                'formulation_name' => $data['formulation_name'],
                'description' => $data['description'] ?? null,
                'target_species' => $data['target_species'],
                'life_stage' => $data['life_stage'],
                'feed_type' => $data['feed_type'],
                'batch_size_kg' => $data['batch_size_kg'],
                'mixing_instructions' => $data['mixing_instructions'] ?? null,
                'quality_standards' => $data['quality_standards'] ?? null,
                'shelf_life_days' => $data['shelf_life_days'] ?? 30,
                'storage_requirements' => $data['storage_requirements'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => 'draft',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Add ingredients
            foreach ($data['ingredients'] as $ingredientData) {
                $formulation->ingredients()->create([
                    'uuid' => Str::uuid(),
                    'feed_ingredient_id' => $ingredientData['feed_ingredient_id'],
                    'percentage' => $ingredientData['percentage'],
                    'minimum_percentage' => $ingredientData['minimum_percentage'] ?? null,
                    'maximum_percentage' => $ingredientData['maximum_percentage'] ?? null,
                    'notes' => $ingredientData['notes'] ?? null,
                ]);
            }

            // Calculate nutritional values
            $formulation->calculateNutritionalValues();

            return $formulation;
        });
    }

    public function approveFeedFormulation(FeedFormulation $formulation): bool
    {
        if (!$formulation->is_balanced) {
            throw new \Exception('Formulation ingredients must total 100%');
        }

        $result = $formulation->approve();
        
        if ($result) {
            event(new FeedFormulationApproved($formulation));
        }
        
        return $result;
    }

    public function createFeedBatch(array $data): FeedBatch
    {
        return DB::transaction(function () use ($data) {
            $formulation = FeedFormulation::findOrFail($data['formulation_id']);
            
            $batch = FeedBatch::create([
                'uuid' => Str::uuid(),
                'batch_number' => FeedBatch::generateBatchNumber(),
                'formulation_id' => $data['formulation_id'],
                'production_date' => $data['production_date'],
                'expiry_date' => now()->parse($data['production_date'])->addDays($formulation->shelf_life_days),
                'quantity_produced' => $data['quantity_produced'],
                'quantity_available' => $data['quantity_produced'],
                'unit_cost' => $formulation->cost_per_kg,
                'total_cost' => $data['quantity_produced'] * $formulation->cost_per_kg,
                'quality_score' => $data['quality_score'] ?? null,
                'production_notes' => $data['production_notes'] ?? null,
                'status' => 'available',
                'produced_by' => $data['produced_by'] ?? auth()->id(),
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Create inventory record
            FeedInventory::create([
                'uuid' => Str::uuid(),
                'batch_id' => $batch->id,
                'formulation_id' => $formulation->id,
                'warehouse_id' => $data['warehouse_id'],
                'quantity_available' => $data['quantity_produced'],
                'quantity_reserved' => 0,
                'quantity_consumed' => 0,
                'production_date' => $data['production_date'],
                'expiry_date' => $batch->expiry_date,
                'status' => 'available',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            event(new FeedBatchCreated($batch));

            return $batch;
        });
    }

    public function recordFeedConsumption(array $data): FeedConsumptionRecord
    {
        return DB::transaction(function () use ($data) {
            $record = FeedConsumptionRecord::create([
                'uuid' => Str::uuid(),
                'consumption_date' => $data['consumption_date'],
                'flock_id' => $data['flock_id'],
                'house_id' => $data['house_id'],
                'feed_batch_id' => $data['feed_batch_id'],
                'formulation_id' => $data['formulation_id'],
                'quantity_kg' => $data['quantity_kg'],
                'feeding_time' => $data['feeding_time'] ?? now()->format('H:i:s'),
                'feed_type' => $data['feed_type'],
                'bird_count' => $data['bird_count'],
                'consumption_per_bird' => $data['quantity_kg'] / $data['bird_count'],
                'leftover_kg' => $data['leftover_kg'] ?? 0,
                'wastage_kg' => $data['wastage_kg'] ?? 0,
                'feed_quality_score' => $data['feed_quality_score'] ?? null,
                'weather_condition' => $data['weather_condition'] ?? null,
                'temperature' => $data['temperature'] ?? null,
                'humidity' => $data['humidity'] ?? null,
                'notes' => $data['notes'] ?? null,
                'recorded_by' => $data['recorded_by'] ?? auth()->id(),
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Calculate FCR
            $record->calculateFCR();

            // Update inventory
            $record->updateInventory();

            event(new FeedConsumed($record));

            return $record;
        });
    }

    public function getFeedAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();
        $flockId = $filters['flock_id'] ?? null;
        $formulationId = $filters['formulation_id'] ?? null;

        $query = FeedConsumptionRecord::byDateRange($startDate, $endDate);

        if ($flockId) {
            $query->where('flock_id', $flockId);
        }

        if ($formulationId) {
            $query->where('formulation_id', $formulationId);
        }

        $records = $query->with(['flock', 'house', 'formulation'])->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => [
                'total_consumption_kg' => $records->sum('quantity_kg'),
                'total_effective_consumption_kg' => $records->sum('effective_consumption'),
                'total_wastage_kg' => $records->sum('wastage_kg'),
                'total_cost' => $records->sum('feed_cost'),
                'average_fcr' => $records->avg('feed_conversion_ratio'),
                'average_consumption_per_bird' => $records->avg('consumption_per_bird'),
                'average_wastage_percentage' => $records->avg('wastage_percentage'),
                'average_feed_efficiency' => $records->avg('feed_efficiency'),
            ],
            'daily_consumption' => $this->getDailyConsumptionTrend($records),
            'flock_performance' => $this->getFlockFeedPerformance($records),
            'formulation_usage' => $this->getFormulationUsage($records),
            'cost_analysis' => $this->getFeedCostAnalysis($records),
        ];
    }

    private function getDailyConsumptionTrend($records): array
    {
        return $records->groupBy('consumption_date')
            ->map(function ($dayRecords, $date) {
                return [
                    'date' => $date,
                    'total_consumption' => $dayRecords->sum('quantity_kg'),
                    'effective_consumption' => $dayRecords->sum('effective_consumption'),
                    'wastage' => $dayRecords->sum('wastage_kg'),
                    'cost' => $dayRecords->sum('feed_cost'),
                    'average_fcr' => $dayRecords->avg('feed_conversion_ratio'),
                ];
            })
            ->values()
            ->toArray();
    }

    private function getFlockFeedPerformance($records): array
    {
        return $records->groupBy('flock_id')
            ->map(function ($flockRecords) {
                $flock = $flockRecords->first()->flock;
                return [
                    'flock_number' => $flock->flock_number,
                    'breed' => $flock->breed->name,
                    'age_weeks' => $flock->age_weeks,
                    'total_consumption' => $flockRecords->sum('quantity_kg'),
                    'average_fcr' => $flockRecords->avg('feed_conversion_ratio'),
                    'average_consumption_per_bird' => $flockRecords->avg('consumption_per_bird'),
                    'wastage_percentage' => $flockRecords->avg('wastage_percentage'),
                    'feed_cost' => $flockRecords->sum('feed_cost'),
                ];
            })
            ->values()
            ->toArray();
    }

    private function getFormulationUsage($records): array
    {
        return $records->groupBy('formulation_id')
            ->map(function ($formulationRecords) {
                $formulation = $formulationRecords->first()->formulation;
                return [
                    'formulation_name' => $formulation->formulation_name,
                    'feed_type' => $formulation->feed_type,
                    'life_stage' => $formulation->life_stage,
                    'total_consumption' => $formulationRecords->sum('quantity_kg'),
                    'average_fcr' => $formulationRecords->avg('feed_conversion_ratio'),
                    'total_cost' => $formulationRecords->sum('feed_cost'),
                    'usage_percentage' => 0, // Will be calculated later
                ];
            })
            ->values()
            ->toArray();
    }

    private function getFeedCostAnalysis($records): array
    {
        $totalCost = $records->sum('feed_cost');
        $totalConsumption = $records->sum('quantity_kg');
        
        return [
            'total_cost' => $totalCost,
            'cost_per_kg' => $totalConsumption > 0 ? $totalCost / $totalConsumption : 0,
            'cost_per_bird_per_day' => $this->calculateCostPerBirdPerDay($records),
            'cost_by_formulation' => $this->getCostByFormulation($records),
            'cost_trend' => $this->getCostTrend($records),
        ];
    }

    private function calculateCostPerBirdPerDay($records): float
    {
        $totalCost = $records->sum('feed_cost');
        $totalBirdDays = $records->sum('bird_count');
        
        return $totalBirdDays > 0 ? $totalCost / $totalBirdDays : 0;
    }

    private function getCostByFormulation($records): array
    {
        return $records->groupBy('formulation_id')
            ->map(function ($formulationRecords) {
                $formulation = $formulationRecords->first()->formulation;
                return [
                    'formulation_name' => $formulation->formulation_name,
                    'total_cost' => $formulationRecords->sum('feed_cost'),
                    'total_consumption' => $formulationRecords->sum('quantity_kg'),
                    'cost_per_kg' => $formulation->cost_per_kg,
                ];
            })
            ->values()
            ->toArray();
    }

    private function getCostTrend($records): array
    {
        return $records->groupBy('consumption_date')
            ->map(function ($dayRecords, $date) {
                return [
                    'date' => $date,
                    'total_cost' => $dayRecords->sum('feed_cost'),
                    'consumption_kg' => $dayRecords->sum('quantity_kg'),
                    'cost_per_kg' => $dayRecords->sum('quantity_kg') > 0 
                        ? $dayRecords->sum('feed_cost') / $dayRecords->sum('quantity_kg') 
                        : 0,
                ];
            })
            ->values()
            ->toArray();
    }

    public function getInventoryStatus(): array
    {
        $inventory = FeedInventory::where('status', 'available')
            ->where('quantity_available', '>', 0)
            ->with(['formulation', 'batch'])
            ->get();

        $totalAvailable = $inventory->sum('quantity_available');
        $totalValue = $inventory->sum(function ($item) {
            return $item->quantity_available * $item->batch->unit_cost;
        });

        $expiringStock = $inventory->filter(function ($item) {
            return $item->expiry_date <= now()->addDays(7);
        });

        return [
            'total_available_kg' => $totalAvailable,
            'total_value' => $totalValue,
            'expiring_soon_kg' => $expiringStock->sum('quantity_available'),
            'expiring_soon_value' => $expiringStock->sum(function ($item) {
                return $item->quantity_available * $item->batch->unit_cost;
            }),
            'by_formulation' => $inventory->groupBy('formulation_id')->map(function ($items) {
                $formulation = $items->first()->formulation;
                return [
                    'formulation_name' => $formulation->formulation_name,
                    'quantity_available' => $items->sum('quantity_available'),
                    'value' => $items->sum(function ($item) {
                        return $item->quantity_available * $item->batch->unit_cost;
                    }),
                ];
            }),
            'by_age' => $this->getInventoryByAge($inventory),
        ];
    }

    private function getInventoryByAge($inventory): array
    {
        return [
            'fresh_0_7_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(7);
            })->sum('quantity_available'),
            'good_8_14_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(14) && 
                       $item->production_date < now()->subDays(7);
            })->sum('quantity_available'),
            'aging_15_21_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(21) && 
                       $item->production_date < now()->subDays(14);
            })->sum('quantity_available'),
            'old_22_30_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(30) && 
                       $item->production_date < now()->subDays(21);
            })->sum('quantity_available'),
        ];
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create feed management structure
mkdir -p frontend/src/modules/feed-management/{components,pages,hooks,services,types}
```

### **2.2 Feed Management Service**

```typescript
// frontend/src/modules/feed-management/services/feedManagementService.ts
import { apiClient } from '@/core/api/apiClient';

export interface FeedFormulation {
  id: number;
  uuid: string;
  formulation_code: string;
  formulation_name: string;
  description?: string;
  target_species: string;
  life_stage: 'chick' | 'grower' | 'layer' | 'breeder';
  feed_type: 'starter' | 'grower' | 'layer' | 'finisher' | 'breeder';
  protein_percentage: number;
  energy_kcal_kg: number;
  fat_percentage: number;
  fiber_percentage: number;
  ash_percentage: number;
  moisture_percentage: number;
  calcium_percentage: number;
  phosphorus_percentage: number;
  lysine_percentage: number;
  methionine_percentage: number;
  cost_per_kg: number;
  batch_size_kg: number;
  mixing_instructions?: string;
  quality_standards?: any;
  shelf_life_days: number;
  storage_requirements?: any;
  notes?: string;
  status: 'draft' | 'pending' | 'approved' | 'inactive';
  is_approved: boolean;
  can_be_approved: boolean;
  total_ingredient_percentage: number;
  is_balanced: boolean;
  approved_by?: any;
  approved_at?: string;
  ingredients: FeedFormulationIngredient[];
  created_at: string;
  updated_at: string;
}

export interface FeedFormulationIngredient {
  id: number;
  uuid: string;
  feed_ingredient: FeedIngredient;
  percentage: number;
  minimum_percentage?: number;
  maximum_percentage?: number;
  notes?: string;
}

export interface FeedIngredient {
  id: number;
  name: string;
  protein_percentage: number;
  energy_kcal_kg: number;
  fat_percentage: number;
  fiber_percentage: number;
  calcium_percentage: number;
  phosphorus_percentage: number;
  cost_per_kg: number;
}

export interface FeedConsumptionRecord {
  id: number;
  uuid: string;
  consumption_date: string;
  flock: any;
  house: any;
  feed_batch?: FeedBatch;
  formulation: FeedFormulation;
  quantity_kg: number;
  feeding_time: string;
  feed_type: string;
  bird_count: number;
  consumption_per_bird: number;
  feed_conversion_ratio: number;
  leftover_kg: number;
  wastage_kg: number;
  effective_consumption: number;
  wastage_percentage: number;
  feed_cost: number;
  feed_efficiency: number;
  feed_quality_score?: number;
  weather_condition?: string;
  temperature?: number;
  humidity?: number;
  notes?: string;
  recorded_by?: any;
  created_at: string;
  updated_at: string;
}

export interface FeedBatch {
  id: number;
  uuid: string;
  batch_number: string;
  formulation: FeedFormulation;
  production_date: string;
  expiry_date: string;
  quantity_produced: number;
  quantity_available: number;
  quantity_consumed: number;
  unit_cost: number;
  total_cost: number;
  quality_score?: number;
  production_notes?: string;
  status: 'available' | 'consumed' | 'expired';
  produced_by?: any;
  created_at: string;
  updated_at: string;
}

class FeedManagementService {
  // Feed Formulations
  async getFeedFormulations(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-formulations?${params.toString()}`);
  }

  async getFeedFormulation(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/feed-formulations/${id}${params}`);
  }

  async createFeedFormulation(data: {
    formulation_name: string;
    description?: string;
    target_species: string;
    life_stage: string;
    feed_type: string;
    batch_size_kg: number;
    mixing_instructions?: string;
    quality_standards?: any;
    shelf_life_days?: number;
    storage_requirements?: any;
    notes?: string;
    ingredients: Array<{
      feed_ingredient_id: number;
      percentage: number;
      minimum_percentage?: number;
      maximum_percentage?: number;
      notes?: string;
    }>;
  }) {
    return apiClient.post('/feed-formulations', data);
  }

  async updateFeedFormulation(id: number, data: any) {
    return apiClient.put(`/feed-formulations/${id}`, data);
  }

  async deleteFeedFormulation(id: number) {
    return apiClient.delete(`/feed-formulations/${id}`);
  }

  async approveFeedFormulation(id: number) {
    return apiClient.post(`/feed-formulations/${id}/approve`);
  }

  // Feed Ingredients
  async getFeedIngredients() {
    return apiClient.get('/feed-ingredients');
  }

  // Feed Batches
  async getFeedBatches(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-batches?${params.toString()}`);
  }

  async createFeedBatch(data: {
    formulation_id: number;
    production_date: string;
    quantity_produced: number;
    warehouse_id: number;
    quality_score?: number;
    production_notes?: string;
    produced_by?: number;
  }) {
    return apiClient.post('/feed-batches', data);
  }

  // Feed Consumption
  async getFeedConsumption(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-consumption?${params.toString()}`);
  }

  async recordFeedConsumption(data: {
    consumption_date: string;
    flock_id: number;
    house_id: number;
    feed_batch_id: number;
    formulation_id: number;
    quantity_kg: number;
    feeding_time?: string;
    feed_type: string;
    bird_count: number;
    leftover_kg?: number;
    wastage_kg?: number;
    feed_quality_score?: number;
    weather_condition?: string;
    temperature?: number;
    humidity?: number;
    notes?: string;
    recorded_by?: number;
  }) {
    return apiClient.post('/feed-consumption', data);
  }

  async getDailyConsumption(date?: string) {
    const params = date ? `?date=${date}` : '';
    return apiClient.get(`/feed-consumption/daily${params}`);
  }

  // Feed Inventory
  async getFeedInventory(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-inventory?${params.toString()}`);
  }

  async getInventoryStatus() {
    return apiClient.get('/feed-inventory/status');
  }

  // Analytics & Reports
  async getFeedAnalytics(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-management/analytics?${params.toString()}`);
  }

  async getFeedReport(reportType: string, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-management/reports/${reportType}?${params.toString()}`);
  }

  async getFlockFeedPerformance(flockId: number, period: string = '30d') {
    return apiClient.get(`/feed-management/flock/${flockId}/performance?period=${period}`);
  }

  async getFormulationPerformance(formulationId: number, period: string = '30d') {
    return apiClient.get(`/feed-management/formulation/${formulationId}/performance?period=${period}`);
  }
}

export const feedManagementService = new FeedManagementService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/FeedManagement/Routes/api.php

use Modules\FeedManagement\Http\Controllers\FeedFormulationController;
use Modules\FeedManagement\Http\Controllers\FeedInventoryController;
use Modules\FeedManagement\Http\Controllers\FeedConsumptionController;
use Modules\FeedManagement\Http\Controllers\FeedDistributionController;
use Modules\FeedManagement\Http\Controllers\FeedReportController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Feed Formulations
    Route::apiResource('feed-formulations', FeedFormulationController::class);
    Route::post('feed-formulations/{formulation}/approve', [FeedFormulationController::class, 'approve']);
    Route::get('feed-ingredients', [FeedFormulationController::class, 'ingredients']);
    
    // Feed Batches
    Route::apiResource('feed-batches', FeedInventoryController::class);
    
    // Feed Consumption
    Route::apiResource('feed-consumption', FeedConsumptionController::class);
    Route::get('feed-consumption/daily', [FeedConsumptionController::class, 'dailyConsumption']);
    
    // Feed Inventory
    Route::get('feed-inventory', [FeedInventoryController::class, 'inventory']);
    Route::get('feed-inventory/status', [FeedInventoryController::class, 'status']);
    
    // Feed Analytics & Reports
    Route::prefix('feed-management')->group(function () {
        Route::get('analytics', [FeedReportController::class, 'analytics']);
        Route::get('reports/{type}', [FeedReportController::class, 'report']);
        Route::get('flock/{flock}/performance', [FeedReportController::class, 'flockPerformance']);
        Route::get('formulation/{formulation}/performance', [FeedReportController::class, 'formulationPerformance']);
    });
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Feed formulation CRUD working
- [ ] Nutritional calculation accurate
- [ ] Feed batch management functional
- [ ] Consumption recording working
- [ ] FCR calculation correct
- [ ] Inventory tracking accurate

### **Frontend**
- [ ] Formulation management interface
- [ ] Feed consumption recording
- [ ] Inventory dashboard
- [ ] Analytics visualization
- [ ] Performance tracking
- [ ] Cost analysis reports

### **Integration**
- [ ] Integration dengan poultry management
- [ ] Inventory updates automatic
- [ ] Performance correlation working
- [ ] Cost calculations accurate
- [ ] Expiry tracking functional

## 📞 NEXT STEPS

Setelah Feed Management module selesai:

1. **Test feed formulation** workflow
2. **Verify consumption tracking** accuracy
3. **Test FCR calculations**
4. **Validate cost analysis**
5. **Commit module** ke repository
6. **Lanjut ke** `11_HEALTH_MANAGEMENT.md`

---

**IMPORTANT**: Feed Management adalah cost center terbesar dalam peternakan. Pastikan semua calculations akurat untuk cost optimization dan performance analysis yang tepat.
