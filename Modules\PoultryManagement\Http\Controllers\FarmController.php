<?php

namespace Modules\PoultryManagement\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\PoultryManagement\Entities\Farm;
use Modules\PoultryManagement\Http\Requests\FarmStoreRequest;
use Modules\PoultryManagement\Http\Resources\FarmResource;

class FarmController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of farms.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Farm::with(['manager', 'houses']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('farm_type')) {
            $query->where('farm_type', $request->farm_type);
        }

        if ($request->has('manager_id')) {
            $query->where('manager_id', $request->manager_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('farm_name', 'like', "%{$search}%")
                  ->orWhere('farm_code', 'like', "%{$search}%")
                  ->orWhere('owner_name', 'like', "%{$search}%");
            });
        }

        $farms = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => FarmResource::collection($farms->items()),
            'meta' => [
                'current_page' => $farms->currentPage(),
                'last_page' => $farms->lastPage(),
                'per_page' => $farms->perPage(),
                'total' => $farms->total(),
            ],
        ]);
    }

    /**
     * Store a newly created farm.
     */
    public function store(FarmStoreRequest $request): JsonResponse
    {
        $farmData = $request->validated();
        $farmData['created_by'] = auth()->id();
        $farmData['updated_by'] = auth()->id();

        $farm = Farm::create($farmData);

        return response()->json([
            'message' => 'Farm created successfully',
            'data' => new FarmResource($farm->load(['manager', 'houses'])),
        ], 201);
    }

    /**
     * Display the specified farm.
     */
    public function show(Farm $farm): JsonResponse
    {
        $farm->load(['manager', 'houses.currentFlock', 'flocks.breed']);

        return response()->json([
            'data' => new FarmResource($farm),
        ]);
    }

    /**
     * Update the specified farm.
     */
    public function update(FarmStoreRequest $request, Farm $farm): JsonResponse
    {
        $farmData = $request->validated();
        $farmData['updated_by'] = auth()->id();

        $farm->update($farmData);

        return response()->json([
            'message' => 'Farm updated successfully',
            'data' => new FarmResource($farm->load(['manager', 'houses'])),
        ]);
    }

    /**
     * Remove the specified farm.
     */
    public function destroy(Farm $farm): JsonResponse
    {
        // Check if farm has active flocks
        if ($farm->flocks()->where('status', 'active')->exists()) {
            return response()->json([
                'message' => 'Cannot delete farm with active flocks',
            ], 422);
        }

        $farm->delete();

        return response()->json([
            'message' => 'Farm deleted successfully',
        ]);
    }

    /**
     * Get farm statistics.
     */
    public function statistics(Farm $farm): JsonResponse
    {
        $utilization = $farm->calculateUtilization();
        
        $statistics = [
            'utilization' => $utilization,
            'houses_by_type' => $farm->houses()
                ->selectRaw('house_type, COUNT(*) as count')
                ->groupBy('house_type')
                ->get(),
            'flocks_by_stage' => $farm->flocks()
                ->where('status', 'active')
                ->selectRaw('production_stage, COUNT(*) as count')
                ->groupBy('production_stage')
                ->get(),
            'monthly_population' => $farm->flocks()
                ->selectRaw('MONTH(placement_date) as month, SUM(current_count) as population')
                ->where('placement_date', '>=', now()->subYear())
                ->groupBy('month')
                ->get(),
        ];

        return response()->json([
            'data' => $statistics,
        ]);
    }

    /**
     * Get farms overview for dashboard.
     */
    public function overview(): JsonResponse
    {
        $overview = [
            'total_farms' => Farm::count(),
            'active_farms' => Farm::active()->count(),
            'total_capacity' => Farm::active()->sum('total_capacity'),
            'current_population' => Farm::active()->sum('current_population'),
            'farms_by_type' => Farm::selectRaw('farm_type, COUNT(*) as count')
                ->groupBy('farm_type')
                ->get(),
            'recent_farms' => FarmResource::collection(
                Farm::with(['manager'])
                    ->latest()
                    ->limit(5)
                    ->get()
            ),
        ];

        return response()->json([
            'data' => $overview,
        ]);
    }
}
