# 15 - DEPLOYMENT & PRODUCTION

## 📋 OVERVIEW

File ini berisi panduan lengkap untuk deployment sistem ERP Peternakan Ayam ke production environment. Mencakup setup server, CI/CD pipeline, monitoring, backup, dan maintenance procedures.

## 🎯 TUJUAN

- Setup production environment yang secure dan scalable
- Implementasi CI/CD pipeline untuk automated deployment
- Setup monitoring dan alerting system
- Implementasi backup dan disaster recovery
- Performance optimization untuk production load
- Security hardening dan compliance

## ⏱️ ESTIMASI WAKTU

**Total**: 16-20 jam
- Server setup: 4-6 jam
- CI/CD pipeline: 4-6 jam
- Monitoring setup: 3-4 jam
- Security hardening: 3-4 jam
- Testing & optimization: 2-4 jam

## 👥 TIM YANG TERLIBAT

- **DevOps Engineer** (Lead)
- **Backend Lead**
- **Security Specialist**
- **System Administrator**

## 🏗️ PRODUCTION ARCHITECTURE

### **Infrastructure Overview**
```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUCTION ENVIRONMENT                    │
├─────────────────────────────────────────────────────────────┤
│  Load Balancer (Nginx/HAProxy)                             │
│  ├── SSL Termination                                       │
│  ├── Rate Limiting                                         │
│  └── Health Checks                                         │
├─────────────────────────────────────────────────────────────┤
│  Application Servers (2+ instances)                        │
│  ├── Docker Containers                                     │
│  ├── Auto-scaling                                          │
│  └── Rolling Updates                                       │
├─────────────────────────────────────────────────────────────┤
│  Database Cluster                                          │
│  ├── MySQL Master-Slave                                    │
│  ├── Read Replicas                                         │
│  └── Automated Backups                                     │
├─────────────────────────────────────────────────────────────┤
│  Cache Layer                                               │
│  ├── Redis Cluster                                         │
│  ├── Session Storage                                       │
│  └── Application Cache                                     │
├─────────────────────────────────────────────────────────────┤
│  File Storage                                              │
│  ├── S3/MinIO                                              │
│  ├── CDN Integration                                       │
│  └── Backup Storage                                        │
├─────────────────────────────────────────────────────────────┤
│  Monitoring & Logging                                      │
│  ├── Prometheus + Grafana                                  │
│  ├── ELK Stack                                             │
│  └── Alerting (Slack/Email)                               │
└─────────────────────────────────────────────────────────────┘
```

## 🐳 STEP 1: PRODUCTION DOCKER SETUP

### **1.1 Production Dockerfile**

```dockerfile
# Multi-stage production Dockerfile
FROM node:18-alpine as frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

# PHP Backend stage
FROM php:8.2-fpm-alpine as backend

# Install system dependencies
RUN apk add --no-cache \
    git curl libpng-dev libxml2-dev zip unzip \
    mysql-client nginx supervisor \
    freetype-dev libjpeg-turbo-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd

# Install PHP extensions
RUN docker-php-ext-install \
    pdo pdo_mysql mbstring exif pcntl bcmath \
    opcache intl

# Install Redis extension
RUN apk add --no-cache pcre-dev $PHPIZE_DEPS \
    && pecl install redis \
    && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configure PHP for production
COPY docker/php/php-prod.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# Set working directory
WORKDIR /var/www

# Copy backend files
COPY backend/ .

# Install PHP dependencies
RUN composer install --optimize-autoloader --no-dev --no-scripts \
    && composer dump-autoload --optimize

# Copy frontend build
COPY --from=frontend-builder /app/frontend/dist ./public/dist

# Configure Nginx
COPY docker/nginx/nginx-prod.conf /etc/nginx/nginx.conf
COPY docker/nginx/default-prod.conf /etc/nginx/conf.d/default.conf

# Configure Supervisor
COPY docker/supervisor/supervisord-prod.conf /etc/supervisor/conf.d/supervisord.conf

# Set permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www/storage \
    && chmod -R 755 /var/www/bootstrap/cache

# Health check
COPY docker/healthcheck.sh /usr/local/bin/healthcheck
RUN chmod +x /usr/local/bin/healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck

EXPOSE 80 443
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

### **1.2 Production Docker Compose**

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    image: erp-poultry:${APP_VERSION:-latest}
    container_name: erp-poultry-app
    restart: unless-stopped
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=database
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
    volumes:
      - ./storage:/var/www/storage
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - erp-network
    depends_on:
      - database
      - redis
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  nginx:
    image: nginx:alpine
    container_name: erp-poultry-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx-lb.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - erp-network
    depends_on:
      - app

  database:
    image: mysql:8.0
    container_name: erp-poultry-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my-prod.cnf:/etc/mysql/conf.d/custom.cnf
      - ./backups:/backups
    networks:
      - erp-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  redis:
    image: redis:7-alpine
    container_name: erp-poultry-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redisdata:/data
      - ./docker/redis/redis-prod.conf:/usr/local/etc/redis/redis.conf
    networks:
      - erp-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  queue-worker:
    image: erp-poultry:${APP_VERSION:-latest}
    container_name: erp-poultry-queue
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
    volumes:
      - ./storage:/var/www/storage
    networks:
      - erp-network
    depends_on:
      - database
      - redis

  scheduler:
    image: erp-poultry:${APP_VERSION:-latest}
    container_name: erp-poultry-scheduler
    restart: unless-stopped
    command: sh -c "while true; do php artisan schedule:run; sleep 60; done"
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
    volumes:
      - ./storage:/var/www/storage
    networks:
      - erp-network
    depends_on:
      - database
      - redis

networks:
  erp-network:
    driver: bridge

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local
```

## 🚀 STEP 2: CI/CD PIPELINE

### **2.1 GitHub Actions Workflow**

```yaml
# .github/workflows/deploy-production.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: secret
          MYSQL_DATABASE: erp_poultry_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql, redis
        
    - name: Install Backend Dependencies
      working-directory: ./backend
      run: composer install --prefer-dist --no-progress --no-suggest
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install Frontend Dependencies
      working-directory: ./frontend
      run: npm ci
      
    - name: Build Frontend
      working-directory: ./frontend
      run: npm run build
      
    - name: Copy Environment File
      working-directory: ./backend
      run: cp .env.testing .env
      
    - name: Generate Application Key
      working-directory: ./backend
      run: php artisan key:generate
      
    - name: Run Database Migrations
      working-directory: ./backend
      run: php artisan migrate --force
      
    - name: Run Backend Tests
      working-directory: ./backend
      run: php artisan test --coverage --min=80
      
    - name: Run Frontend Tests
      working-directory: ./frontend
      run: npm run test:ci

  build:
    needs: test
    runs-on: ubuntu-latest
    
    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
      
    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Output image
      id: image
      run: |
        echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}" >> $GITHUB_OUTPUT

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Deploy to Production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        key: ${{ secrets.PROD_SSH_KEY }}
        script: |
          cd /opt/erp-poultry
          
          # Pull latest code
          git pull origin main
          
          # Update environment variables
          echo "APP_VERSION=${{ needs.build.outputs.image }}" >> .env.prod
          
          # Pull new image
          docker pull ${{ needs.build.outputs.image }}
          
          # Run database migrations
          docker-compose -f docker-compose.prod.yml run --rm app php artisan migrate --force
          
          # Clear caches
          docker-compose -f docker-compose.prod.yml run --rm app php artisan config:cache
          docker-compose -f docker-compose.prod.yml run --rm app php artisan route:cache
          docker-compose -f docker-compose.prod.yml run --rm app php artisan view:cache
          
          # Rolling update
          docker-compose -f docker-compose.prod.yml up -d --no-deps app
          
          # Health check
          sleep 30
          curl -f http://localhost/health || exit 1
          
          # Clean up old images
          docker image prune -f

  notify:
    needs: [test, build, deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
```

### **2.2 Deployment Scripts**

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# Configuration
APP_NAME="erp-poultry"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
BACKUP_DIR="/opt/backups"
LOG_FILE="/var/log/deploy.log"

# Functions
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

backup_database() {
    log "Creating database backup..."
    BACKUP_FILE="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose -f $DOCKER_COMPOSE_FILE exec -T database \
        mysqldump -u root -p$DB_ROOT_PASSWORD $DB_DATABASE > $BACKUP_FILE
    log "Database backup created: $BACKUP_FILE"
}

health_check() {
    log "Performing health check..."
    for i in {1..30}; do
        if curl -f http://localhost/health > /dev/null 2>&1; then
            log "Health check passed"
            return 0
        fi
        log "Health check attempt $i failed, retrying..."
        sleep 10
    done
    log "Health check failed after 30 attempts"
    return 1
}

rollback() {
    log "Rolling back to previous version..."
    docker-compose -f $DOCKER_COMPOSE_FILE down
    docker tag $APP_NAME:previous $APP_NAME:latest
    docker-compose -f $DOCKER_COMPOSE_FILE up -d
    log "Rollback completed"
}

# Main deployment process
main() {
    log "Starting deployment of $APP_NAME"
    
    # Backup current database
    backup_database
    
    # Tag current image as previous
    docker tag $APP_NAME:latest $APP_NAME:previous 2>/dev/null || true
    
    # Pull latest code
    log "Pulling latest code..."
    git pull origin main
    
    # Build new image
    log "Building new Docker image..."
    docker-compose -f $DOCKER_COMPOSE_FILE build
    
    # Run database migrations
    log "Running database migrations..."
    docker-compose -f $DOCKER_COMPOSE_FILE run --rm app php artisan migrate --force
    
    # Clear application caches
    log "Clearing application caches..."
    docker-compose -f $DOCKER_COMPOSE_FILE run --rm app php artisan config:cache
    docker-compose -f $DOCKER_COMPOSE_FILE run --rm app php artisan route:cache
    docker-compose -f $DOCKER_COMPOSE_FILE run --rm app php artisan view:cache
    
    # Rolling update
    log "Performing rolling update..."
    docker-compose -f $DOCKER_COMPOSE_FILE up -d --no-deps app
    
    # Health check
    if health_check; then
        log "Deployment successful"
        
        # Clean up old images
        docker image prune -f
        
        # Clean up old backups (keep last 7 days)
        find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete
        
    else
        log "Deployment failed, initiating rollback"
        rollback
        exit 1
    fi
    
    log "Deployment completed successfully"
}

# Run main function
main "$@"
```

## 📊 STEP 3: MONITORING & ALERTING

### **3.1 Prometheus Configuration**

```yaml
# docker/monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'erp-poultry'
    static_configs:
      - targets: ['app:9000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
```

### **3.2 Alert Rules**

```yaml
# docker/monitoring/alert_rules.yml
groups:
- name: erp-poultry-alerts
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds"

  - alert: DatabaseConnectionFailure
    expr: mysql_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connection failure"
      description: "MySQL database is not responding"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage"
      description: "Memory usage is {{ $value | humanizePercentage }}"

  - alert: HighDiskUsage
    expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High disk usage"
      description: "Disk usage is {{ $value | humanizePercentage }}"

  - alert: QueueBacklog
    expr: laravel_queue_size > 1000
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Queue backlog detected"
      description: "Queue has {{ $value }} pending jobs"
```

### **3.3 Grafana Dashboard**

```json
{
  "dashboard": {
    "title": "ERP Poultry Management System",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Database Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "mysql_global_status_queries",
            "legendFormat": "Queries per second"
          },
          {
            "expr": "mysql_global_status_slow_queries",
            "legendFormat": "Slow queries"
          }
        ]
      },
      {
        "title": "System Resources",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg(rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "Memory Usage %"
          }
        ]
      }
    ]
  }
}
```

## 🔒 STEP 4: SECURITY HARDENING

### **4.1 SSL/TLS Configuration**

```nginx
# docker/nginx/ssl.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location /api {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location /auth/login {
        limit_req zone=login burst=5 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### **4.2 Firewall Configuration**

```bash
#!/bin/bash
# scripts/setup-firewall.sh

# Reset iptables
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
iptables -t mangle -F
iptables -t mangle -X

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# Allow SSH (change port as needed)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# Allow HTTP and HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Allow ping
iptables -A INPUT -p icmp --icmp-type echo-request -j ACCEPT

# Rate limiting for SSH
iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW -m recent --set
iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW -m recent --update --seconds 60 --hitcount 4 -j DROP

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "Firewall configured successfully"
```

## 💾 STEP 5: BACKUP & DISASTER RECOVERY

### **5.1 Automated Backup Script**

```bash
#!/bin/bash
# scripts/backup.sh

set -e

# Configuration
BACKUP_DIR="/opt/backups"
RETENTION_DAYS=30
DB_CONTAINER="erp-poultry-db"
APP_CONTAINER="erp-poultry-app"
S3_BUCKET="erp-poultry-backups"

# Create backup directory
mkdir -p $BACKUP_DIR

# Generate timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Database backup
echo "Creating database backup..."
docker exec $DB_CONTAINER mysqldump -u root -p$DB_ROOT_PASSWORD $DB_DATABASE | gzip > $BACKUP_DIR/db_$TIMESTAMP.sql.gz

# Application files backup
echo "Creating application files backup..."
docker exec $APP_CONTAINER tar -czf - /var/www/storage > $BACKUP_DIR/storage_$TIMESTAMP.tar.gz

# Configuration backup
echo "Creating configuration backup..."
tar -czf $BACKUP_DIR/config_$TIMESTAMP.tar.gz docker/ .env.prod

# Upload to S3 (if configured)
if [ ! -z "$S3_BUCKET" ]; then
    echo "Uploading backups to S3..."
    aws s3 cp $BACKUP_DIR/db_$TIMESTAMP.sql.gz s3://$S3_BUCKET/database/
    aws s3 cp $BACKUP_DIR/storage_$TIMESTAMP.tar.gz s3://$S3_BUCKET/storage/
    aws s3 cp $BACKUP_DIR/config_$TIMESTAMP.tar.gz s3://$S3_BUCKET/config/
fi

# Clean up old backups
echo "Cleaning up old backups..."
find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup completed successfully"
```

### **5.2 Disaster Recovery Plan**

```bash
#!/bin/bash
# scripts/disaster-recovery.sh

set -e

# Configuration
BACKUP_DIR="/opt/backups"
S3_BUCKET="erp-poultry-backups"

restore_database() {
    local backup_file=$1
    echo "Restoring database from $backup_file..."
    
    # Stop application
    docker-compose -f docker-compose.prod.yml stop app
    
    # Restore database
    gunzip -c $backup_file | docker exec -i erp-poultry-db mysql -u root -p$DB_ROOT_PASSWORD $DB_DATABASE
    
    echo "Database restored successfully"
}

restore_storage() {
    local backup_file=$1
    echo "Restoring storage from $backup_file..."
    
    # Extract storage files
    docker exec erp-poultry-app tar -xzf - -C / < $backup_file
    
    echo "Storage restored successfully"
}

restore_from_s3() {
    local date=$1
    echo "Restoring from S3 backup dated $date..."
    
    # Download backups from S3
    aws s3 cp s3://$S3_BUCKET/database/db_${date}.sql.gz $BACKUP_DIR/
    aws s3 cp s3://$S3_BUCKET/storage/storage_${date}.tar.gz $BACKUP_DIR/
    aws s3 cp s3://$S3_BUCKET/config/config_${date}.tar.gz $BACKUP_DIR/
    
    # Restore
    restore_database $BACKUP_DIR/db_${date}.sql.gz
    restore_storage $BACKUP_DIR/storage_${date}.tar.gz
    
    # Extract configuration
    tar -xzf $BACKUP_DIR/config_${date}.tar.gz
    
    echo "Restoration from S3 completed"
}

# Main recovery function
main() {
    case $1 in
        "database")
            restore_database $2
            ;;
        "storage")
            restore_storage $2
            ;;
        "s3")
            restore_from_s3 $2
            ;;
        *)
            echo "Usage: $0 {database|storage|s3} [backup_file|date]"
            exit 1
            ;;
    esac
    
    # Restart application
    docker-compose -f docker-compose.prod.yml up -d
    
    echo "Disaster recovery completed"
}

main "$@"
```

## ✅ VERIFICATION CHECKLIST

### **Infrastructure**
- [ ] Production servers configured
- [ ] Load balancer setup and tested
- [ ] SSL certificates installed
- [ ] Firewall rules configured
- [ ] Monitoring stack deployed

### **Application**
- [ ] Docker images built and tested
- [ ] Database migrations successful
- [ ] Application health checks passing
- [ ] Performance benchmarks met
- [ ] Security scans passed

### **CI/CD**
- [ ] Pipeline configured and tested
- [ ] Automated tests passing
- [ ] Deployment scripts working
- [ ] Rollback procedures tested
- [ ] Notifications configured

### **Monitoring**
- [ ] Metrics collection working
- [ ] Alerts configured and tested
- [ ] Dashboards accessible
- [ ] Log aggregation working
- [ ] Performance monitoring active

### **Security**
- [ ] SSL/TLS configured properly
- [ ] Security headers implemented
- [ ] Rate limiting active
- [ ] Access controls verified
- [ ] Vulnerability scans passed

### **Backup & Recovery**
- [ ] Automated backups working
- [ ] Backup verification successful
- [ ] Recovery procedures tested
- [ ] Off-site storage configured
- [ ] RTO/RPO requirements met

## 📞 NEXT STEPS

Setelah production deployment selesai:

1. **Monitor system performance** untuk 48 jam pertama
2. **Verify backup procedures** berjalan dengan baik
3. **Test disaster recovery** procedures
4. **Train operations team** untuk maintenance
5. **Document runbooks** untuk troubleshooting
6. **Schedule regular security audits**
7. **Plan capacity scaling** berdasarkan usage

---

**IMPORTANT**: Production deployment adalah tahap kritis. Pastikan semua checklist terverifikasi dan tim operations siap untuk monitoring dan maintenance 24/7.
