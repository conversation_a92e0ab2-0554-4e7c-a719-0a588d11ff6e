#!/bin/bash

# ERP Poultry Management System Installation Script
# This script sets up the development environment for the ERP system

echo "🐔 ERP Poultry Management System - Installation Script"
echo "======================================================"

# Check if running on Windows (Git Bash/WSL)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    echo "✅ Detected Windows environment"
    IS_WINDOWS=true
else
    echo "✅ Detected Unix-like environment"
    IS_WINDOWS=false
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo ""
echo "🔍 Checking prerequisites..."

# Check PHP
if command_exists php; then
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    echo "✅ PHP $PHP_VERSION found"
    if [[ $(echo "$PHP_VERSION >= 8.1" | bc -l) -eq 1 ]]; then
        echo "✅ PHP version is compatible"
    else
        echo "❌ PHP 8.1 or higher is required"
        exit 1
    fi
else
    echo "❌ PHP not found. Please install PHP 8.1 or higher"
    exit 1
fi

# Check Composer
if command_exists composer; then
    echo "✅ Composer found"
else
    echo "❌ Composer not found. Please install Composer"
    exit 1
fi

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    echo "✅ Node.js v$NODE_VERSION found"
    if [[ $NODE_VERSION -ge 18 ]]; then
        echo "✅ Node.js version is compatible"
    else
        echo "❌ Node.js 18 or higher is required"
        exit 1
    fi
else
    echo "❌ Node.js not found. Please install Node.js 18 or higher"
    exit 1
fi

# Check npm
if command_exists npm; then
    echo "✅ npm found"
else
    echo "❌ npm not found. Please install npm"
    exit 1
fi

# Check MySQL
if command_exists mysql; then
    echo "✅ MySQL found"
else
    echo "⚠️  MySQL not found. Please ensure MySQL is installed and running"
fi

# Check Redis (optional)
if command_exists redis-cli; then
    echo "✅ Redis found"
else
    echo "⚠️  Redis not found. Redis is recommended for caching and sessions"
fi

echo ""
echo "🚀 Starting installation..."

# Step 1: Install PHP dependencies
echo ""
echo "📦 Installing PHP dependencies..."
if composer install --no-dev --optimize-autoloader; then
    echo "✅ PHP dependencies installed successfully"
else
    echo "❌ Failed to install PHP dependencies"
    exit 1
fi

# Step 2: Setup environment file
echo ""
echo "⚙️  Setting up environment configuration..."
if [ ! -f .env ]; then
    if cp .env.example .env; then
        echo "✅ Environment file created"
    else
        echo "❌ Failed to create environment file"
        exit 1
    fi
else
    echo "✅ Environment file already exists"
fi

# Step 3: Generate application key
echo ""
echo "🔑 Generating application key..."
if php artisan key:generate; then
    echo "✅ Application key generated"
else
    echo "❌ Failed to generate application key"
    exit 1
fi

# Step 4: Database setup
echo ""
echo "🗄️  Setting up database..."
read -p "Enter database name (default: erp_poultry): " DB_NAME
DB_NAME=${DB_NAME:-erp_poultry}

read -p "Enter database username (default: root): " DB_USER
DB_USER=${DB_USER:-root}

read -s -p "Enter database password: " DB_PASS
echo ""

# Update .env file with database credentials
if $IS_WINDOWS; then
    sed -i "s/DB_DATABASE=erp_poultry/DB_DATABASE=$DB_NAME/" .env
    sed -i "s/DB_USERNAME=root/DB_USERNAME=$DB_USER/" .env
    sed -i "s/DB_PASSWORD=/DB_PASSWORD=$DB_PASS/" .env
else
    sed -i.bak "s/DB_DATABASE=erp_poultry/DB_DATABASE=$DB_NAME/" .env
    sed -i.bak "s/DB_USERNAME=root/DB_USERNAME=$DB_USER/" .env
    sed -i.bak "s/DB_PASSWORD=/DB_PASSWORD=$DB_PASS/" .env
fi

echo "✅ Database configuration updated"

# Step 5: Create database if it doesn't exist
echo ""
echo "🏗️  Creating database..."
mysql -u "$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Database created successfully"
else
    echo "⚠️  Database creation failed or database already exists"
fi

# Step 6: Run migrations
echo ""
echo "🔄 Running database migrations..."
if php artisan migrate --force; then
    echo "✅ Database migrations completed"
else
    echo "❌ Database migrations failed"
    exit 1
fi

# Step 7: Install Spatie Permission
echo ""
echo "🔐 Setting up permissions system..."
if php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"; then
    echo "✅ Permission system configured"
else
    echo "❌ Failed to configure permission system"
    exit 1
fi

# Step 8: Seed database
echo ""
echo "🌱 Seeding database with initial data..."
if php artisan db:seed; then
    echo "✅ Database seeded successfully"
else
    echo "❌ Database seeding failed"
    exit 1
fi

# Step 9: Install frontend dependencies
echo ""
echo "📦 Installing frontend dependencies..."
cd frontend
if npm install; then
    echo "✅ Frontend dependencies installed successfully"
else
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi
cd ..

# Step 10: Create storage links
echo ""
echo "🔗 Creating storage links..."
if php artisan storage:link; then
    echo "✅ Storage links created"
else
    echo "❌ Failed to create storage links"
fi

# Step 11: Set permissions (Unix-like systems only)
if ! $IS_WINDOWS; then
    echo ""
    echo "🔒 Setting file permissions..."
    chmod -R 755 storage bootstrap/cache
    echo "✅ File permissions set"
fi

# Step 12: Clear caches
echo ""
echo "🧹 Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
echo "✅ Caches cleared"

# Installation complete
echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Start the Laravel development server:"
echo "   php artisan serve"
echo ""
echo "2. Start the React development server:"
echo "   cd frontend && npm start"
echo ""
echo "3. Access the application:"
echo "   Backend API: http://localhost:8000"
echo "   Frontend: http://localhost:3000"
echo ""
echo "4. Default login credentials:"
echo "   Super Admin: <EMAIL> / password123"
echo "   Farm Manager: <EMAIL> / password123"
echo "   Farm Worker: <EMAIL> / password123"
echo ""
echo "🔧 Optional: Use Docker for development:"
echo "   docker-compose up -d"
echo ""
echo "📚 Documentation available in the Docs/ directory"
echo ""
echo "Happy farming! 🐔🥚"
