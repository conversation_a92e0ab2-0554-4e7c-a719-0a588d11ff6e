<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\PoultryManagement\Http\Controllers\FarmController;
use Modules\PoultryManagement\Http\Controllers\HouseController;
use Modules\PoultryManagement\Http\Controllers\FlockController;
use Modules\PoultryManagement\Http\Controllers\BreedController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->group(function () {
    // Farm management routes
    Route::apiResource('farms', FarmController::class);
    Route::get('farms/{farm}/statistics', [FarmController::class, 'statistics']);
    Route::get('farms-overview', [FarmController::class, 'overview']);
    
    // House management routes
    Route::apiResource('houses', HouseController::class);
    Route::get('farms/{farm}/houses', [HouseController::class, 'byFarm']);
    Route::post('houses/{house}/assign-flock', [HouseController::class, 'assignFlock']);
    Route::post('houses/{house}/remove-flock', [HouseController::class, 'removeFlock']);
    Route::get('houses/{house}/environmental-data', [HouseController::class, 'environmentalData']);
    
    // Flock management routes
    Route::apiResource('flocks', FlockController::class);
    Route::get('houses/{house}/flocks', [FlockController::class, 'byHouse']);
    Route::post('flocks/{flock}/update-population', [FlockController::class, 'updatePopulation']);
    Route::post('flocks/{flock}/transfer', [FlockController::class, 'transfer']);
    Route::get('flocks/{flock}/performance', [FlockController::class, 'performance']);
    Route::get('flocks/{flock}/timeline', [FlockController::class, 'timeline']);
    
    // Breed management routes
    Route::apiResource('breeds', BreedController::class);
    Route::get('breeds/{breed}/performance-comparison', [BreedController::class, 'performanceComparison']);
    Route::post('breeds/{breed}/activate', [BreedController::class, 'activate']);
    Route::post('breeds/{breed}/deactivate', [BreedController::class, 'deactivate']);
    
    // Dashboard and analytics routes
    Route::get('poultry/dashboard', [FarmController::class, 'dashboard']);
    Route::get('poultry/analytics/production', [FlockController::class, 'productionAnalytics']);
    Route::get('poultry/analytics/capacity', [HouseController::class, 'capacityAnalytics']);
    Route::get('poultry/analytics/breeds', [BreedController::class, 'breedAnalytics']);
});
