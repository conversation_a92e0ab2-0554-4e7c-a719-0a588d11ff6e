# 04 - INVENTORY MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Inventory Management mengelola semua aspek inventory termasuk items, categories, warehouses, stock levels, dan stock movements. Modul ini menggunakan sistem multi-warehouse dengan real-time stock tracking dan support untuk batch/serial number tracking.

## 🎯 TUJUAN

- Manajemen master data items dan categories
- Multi-warehouse inventory management
- Real-time stock level tracking
- Stock movement recording (in, out, transfer, adjustment)
- Batch dan serial number tracking
- Inventory valuation dengan multiple costing methods
- Low stock alerts dan reorder point management

## ⏱️ ESTIMASI WAKTU

**Total**: 20-24 jam
- Backend implementation: 14-16 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Backend Lead** (Review)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Master data
item_categories
items
warehouses
units

-- Stock management
stock_levels
stock_movements

-- Supporting tables
audit_trails (untuk tracking changes)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Inventory module
php artisan module:make Inventory

# Create module components
php artisan module:make-controller Inventory ItemController --api
php artisan module:make-controller Inventory CategoryController --api
php artisan module:make-controller Inventory WarehouseController --api
php artisan module:make-controller Inventory StockController --api
php artisan module:make-model Inventory Item
php artisan module:make-model Inventory ItemCategory
php artisan module:make-model Inventory Warehouse
php artisan module:make-model Inventory StockLevel
php artisan module:make-model Inventory StockMovement
php artisan module:make-request Inventory ItemStoreRequest
php artisan module:make-request Inventory ItemUpdateRequest
php artisan module:make-resource Inventory ItemResource
php artisan module:make-resource Inventory StockLevelResource
php artisan module:make-policy Inventory ItemPolicy
php artisan module:make-seeder Inventory InventorySeeder
```

### **1.2 Item Model**

```php
<?php
// Modules/Inventory/Entities/Item.php

namespace Modules\Inventory\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Item extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'code',
        'barcode',
        'name',
        'description',
        'category_id',
        'brand',
        'model',
        'type',
        'unit_id',
        'purchase_unit_id',
        'sale_unit_id',
        'weight',
        'volume',
        'dimensions',
        'image',
        'images',
        'is_trackable',
        'is_serialized',
        'is_batch_tracked',
        'min_stock',
        'max_stock',
        'reorder_point',
        'lead_time_days',
        'shelf_life_days',
        'cost_method',
        'standard_cost',
        'last_cost',
        'average_cost',
        'list_price',
        'sale_price',
        'tax_category',
        'notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'dimensions' => 'array',
        'images' => 'array',
        'is_trackable' => 'boolean',
        'is_serialized' => 'boolean',
        'is_batch_tracked' => 'boolean',
        'weight' => 'decimal:3',
        'volume' => 'decimal:3',
        'min_stock' => 'decimal:3',
        'max_stock' => 'decimal:3',
        'reorder_point' => 'decimal:3',
        'standard_cost' => 'decimal:2',
        'last_cost' => 'decimal:2',
        'average_cost' => 'decimal:2',
        'list_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['code', 'name', 'status', 'standard_cost', 'sale_price'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function category()
    {
        return $this->belongsTo(ItemCategory::class, 'category_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function purchaseUnit()
    {
        return $this->belongsTo(Unit::class, 'purchase_unit_id');
    }

    public function saleUnit()
    {
        return $this->belongsTo(Unit::class, 'sale_unit_id');
    }

    public function stockLevels()
    {
        return $this->hasMany(StockLevel::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%")
              ->orWhere('barcode', 'like', "%{$search}%");
        });
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeLowStock($query)
    {
        return $query->whereHas('stockLevels', function ($q) {
            $q->whereRaw('quantity_on_hand <= min_stock');
        });
    }

    // Accessors
    public function getImageUrlAttribute(): ?string
    {
        if ($this->image) {
            return asset('storage/items/' . $this->image);
        }
        return null;
    }

    public function getTotalStockAttribute(): float
    {
        return $this->stockLevels->sum('quantity_on_hand');
    }

    public function getAvailableStockAttribute(): float
    {
        return $this->stockLevels->sum('quantity_available');
    }

    // Methods
    public function getStockInWarehouse(int $warehouseId): float
    {
        $stockLevel = $this->stockLevels()->where('warehouse_id', $warehouseId)->first();
        return $stockLevel ? $stockLevel->quantity_on_hand : 0;
    }

    public function updateAverageCost(): void
    {
        $totalValue = $this->stockMovements()
            ->where('movement_type', 'in')
            ->sum(\DB::raw('quantity * unit_cost'));
        
        $totalQuantity = $this->stockMovements()
            ->where('movement_type', 'in')
            ->sum('quantity');

        if ($totalQuantity > 0) {
            $this->update(['average_cost' => $totalValue / $totalQuantity]);
        }
    }
}
```

### **1.3 Stock Movement Model**

```php
<?php
// Modules/Inventory/Entities/StockMovement.php

namespace Modules\Inventory\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StockMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'item_id',
        'warehouse_id',
        'movement_type',
        'transaction_type',
        'reference_type',
        'reference_id',
        'reference_number',
        'batch_number',
        'serial_number',
        'quantity',
        'unit_id',
        'unit_cost',
        'reason',
        'notes',
        'movement_date',
        'created_by',
    ];

    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_cost' => 'decimal:2',
        'movement_date' => 'date',
    ];

    // Relationships
    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function reference()
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    public function scopeByMovementType($query, $type)
    {
        return $query->where('movement_type', $type);
    }

    public function scopeByTransactionType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    // Accessors
    public function getTotalCostAttribute(): float
    {
        return $this->quantity * $this->unit_cost;
    }
}
```

### **1.4 Stock Service**

```php
<?php
// Modules/Inventory/Services/StockService.php

namespace Modules\Inventory\Services;

use Modules\Inventory\Entities\Item;
use Modules\Inventory\Entities\Warehouse;
use Modules\Inventory\Entities\StockLevel;
use Modules\Inventory\Entities\StockMovement;
use Modules\Inventory\Events\StockMoved;
use Modules\Inventory\Events\LowStockAlert;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class StockService
{
    public function moveStock(
        Item $item,
        Warehouse $warehouse,
        string $movementType,
        float $quantity,
        string $transactionType,
        array $options = []
    ): StockMovement {
        return DB::transaction(function () use ($item, $warehouse, $movementType, $quantity, $transactionType, $options) {
            // Create stock movement
            $movement = StockMovement::create([
                'uuid' => Str::uuid(),
                'item_id' => $item->id,
                'warehouse_id' => $warehouse->id,
                'movement_type' => $movementType,
                'transaction_type' => $transactionType,
                'reference_type' => $options['reference_type'] ?? null,
                'reference_id' => $options['reference_id'] ?? null,
                'reference_number' => $options['reference_number'] ?? null,
                'batch_number' => $options['batch_number'] ?? null,
                'serial_number' => $options['serial_number'] ?? null,
                'quantity' => $quantity,
                'unit_id' => $item->unit_id,
                'unit_cost' => $options['unit_cost'] ?? 0,
                'reason' => $options['reason'] ?? null,
                'notes' => $options['notes'] ?? null,
                'movement_date' => $options['movement_date'] ?? now()->toDateString(),
                'created_by' => auth()->id(),
            ]);

            // Update stock level
            $this->updateStockLevel($item, $warehouse, $movementType, $quantity);

            // Update item average cost if it's an inbound movement
            if ($movementType === 'in' && isset($options['unit_cost'])) {
                $item->updateAverageCost();
            }

            // Check for low stock alert
            $this->checkLowStockAlert($item, $warehouse);

            // Fire events
            event(new StockMoved($movement));

            return $movement;
        });
    }

    public function transferStock(
        Item $item,
        Warehouse $fromWarehouse,
        Warehouse $toWarehouse,
        float $quantity,
        array $options = []
    ): array {
        return DB::transaction(function () use ($item, $fromWarehouse, $toWarehouse, $quantity, $options) {
            // Check available stock
            $availableStock = $this->getAvailableStock($item, $fromWarehouse);
            if ($availableStock < $quantity) {
                throw new \Exception("Insufficient stock. Available: {$availableStock}, Required: {$quantity}");
            }

            // Create outbound movement
            $outboundMovement = $this->moveStock(
                $item,
                $fromWarehouse,
                'out',
                $quantity,
                'transfer',
                array_merge($options, ['reason' => 'Transfer out'])
            );

            // Create inbound movement
            $inboundMovement = $this->moveStock(
                $item,
                $toWarehouse,
                'in',
                $quantity,
                'transfer',
                array_merge($options, [
                    'reason' => 'Transfer in',
                    'reference_type' => StockMovement::class,
                    'reference_id' => $outboundMovement->id
                ])
            );

            return [$outboundMovement, $inboundMovement];
        });
    }

    public function adjustStock(
        Item $item,
        Warehouse $warehouse,
        float $newQuantity,
        string $reason,
        array $options = []
    ): StockMovement {
        $currentStock = $this->getCurrentStock($item, $warehouse);
        $adjustmentQuantity = $newQuantity - $currentStock;
        $movementType = $adjustmentQuantity >= 0 ? 'in' : 'out';

        return $this->moveStock(
            $item,
            $warehouse,
            $movementType,
            abs($adjustmentQuantity),
            'adjustment',
            array_merge($options, ['reason' => $reason])
        );
    }

    private function updateStockLevel(Item $item, Warehouse $warehouse, string $movementType, float $quantity): void
    {
        $stockLevel = StockLevel::firstOrCreate(
            ['item_id' => $item->id, 'warehouse_id' => $warehouse->id],
            ['quantity_on_hand' => 0, 'quantity_reserved' => 0]
        );

        $adjustment = $movementType === 'in' ? $quantity : -$quantity;
        $stockLevel->increment('quantity_on_hand', $adjustment);
        $stockLevel->update(['last_movement_at' => now()]);
    }

    private function getCurrentStock(Item $item, Warehouse $warehouse): float
    {
        $stockLevel = StockLevel::where('item_id', $item->id)
            ->where('warehouse_id', $warehouse->id)
            ->first();

        return $stockLevel ? $stockLevel->quantity_on_hand : 0;
    }

    private function getAvailableStock(Item $item, Warehouse $warehouse): float
    {
        $stockLevel = StockLevel::where('item_id', $item->id)
            ->where('warehouse_id', $warehouse->id)
            ->first();

        return $stockLevel ? $stockLevel->quantity_available : 0;
    }

    private function checkLowStockAlert(Item $item, Warehouse $warehouse): void
    {
        $stockLevel = StockLevel::where('item_id', $item->id)
            ->where('warehouse_id', $warehouse->id)
            ->first();

        if ($stockLevel && $item->min_stock > 0 && $stockLevel->quantity_on_hand <= $item->min_stock) {
            event(new LowStockAlert($item, $warehouse, $stockLevel->quantity_on_hand));
        }
    }

    public function getStockSummary(Item $item): array
    {
        $stockLevels = $item->stockLevels()->with('warehouse')->get();
        
        return [
            'total_on_hand' => $stockLevels->sum('quantity_on_hand'),
            'total_reserved' => $stockLevels->sum('quantity_reserved'),
            'total_available' => $stockLevels->sum('quantity_available'),
            'warehouses' => $stockLevels->map(function ($level) {
                return [
                    'warehouse' => $level->warehouse->name,
                    'on_hand' => $level->quantity_on_hand,
                    'reserved' => $level->quantity_reserved,
                    'available' => $level->quantity_available,
                ];
            }),
        ];
    }

    public function getMovementHistory(Item $item, array $filters = []): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = $item->stockMovements()->with(['warehouse', 'unit', 'createdBy']);

        if (isset($filters['warehouse_id'])) {
            $query->where('warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['movement_type'])) {
            $query->where('movement_type', $filters['movement_type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('movement_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('movement_date', '<=', $filters['date_to']);
        }

        return $query->orderBy('movement_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 15);
    }
}
```

### **1.5 Item Controller**

```php
<?php
// Modules/Inventory/Http/Controllers/ItemController.php

namespace Modules\Inventory\Http\Controllers;

use App\Core\API\Controllers\BaseApiController;
use Modules\Inventory\Entities\Item;
use Modules\Inventory\Http\Requests\ItemStoreRequest;
use Modules\Inventory\Http\Requests\ItemUpdateRequest;
use Modules\Inventory\Http\Resources\ItemResource;
use Modules\Inventory\Services\StockService;
use Modules\Inventory\Events\ItemCreated;
use Modules\Inventory\Events\ItemUpdated;
use Modules\Inventory\Events\ItemDeleted;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ItemController extends BaseApiController
{
    protected StockService $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    protected array $allowedIncludes = ['category', 'unit', 'stockLevels', 'stockLevels.warehouse'];
    protected array $allowedFilters = ['search', 'category_id', 'type', 'status', 'low_stock'];
    protected array $allowedSorts = ['name', 'code', 'created_at', 'standard_cost', 'sale_price'];

    protected function getModelClass(): string
    {
        return Item::class;
    }

    protected function getResourceClass(): string
    {
        return ItemResource::class;
    }

    protected function getStoreRules(): array
    {
        return [
            'code' => 'required|string|max:50|unique:items',
            'barcode' => 'nullable|string|max:100|unique:items',
            'name' => 'required|string|max:200',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:item_categories,id',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'type' => 'required|in:product,service,raw_material,finished_good,consumable',
            'unit_id' => 'required|exists:units,id',
            'purchase_unit_id' => 'nullable|exists:units,id',
            'sale_unit_id' => 'nullable|exists:units,id',
            'weight' => 'nullable|numeric|min:0',
            'volume' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',
            'is_trackable' => 'boolean',
            'is_serialized' => 'boolean',
            'is_batch_tracked' => 'boolean',
            'min_stock' => 'nullable|numeric|min:0',
            'max_stock' => 'nullable|numeric|min:0',
            'reorder_point' => 'nullable|numeric|min:0',
            'lead_time_days' => 'nullable|integer|min:0',
            'shelf_life_days' => 'nullable|integer|min:0',
            'cost_method' => 'required|in:fifo,lifo,average,standard',
            'standard_cost' => 'nullable|numeric|min:0',
            'list_price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'tax_category' => 'nullable|string|max:50',
            'notes' => 'nullable|string',
            'image' => 'nullable|image|max:2048',
        ];
    }

    protected function getUpdateRules($item): array
    {
        return [
            'code' => "required|string|max:50|unique:items,code,{$item->id}",
            'barcode' => "nullable|string|max:100|unique:items,barcode,{$item->id}",
            'name' => 'required|string|max:200',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:item_categories,id',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'type' => 'required|in:product,service,raw_material,finished_good,consumable',
            'unit_id' => 'required|exists:units,id',
            'purchase_unit_id' => 'nullable|exists:units,id',
            'sale_unit_id' => 'nullable|exists:units,id',
            'weight' => 'nullable|numeric|min:0',
            'volume' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',
            'is_trackable' => 'boolean',
            'is_serialized' => 'boolean',
            'is_batch_tracked' => 'boolean',
            'min_stock' => 'nullable|numeric|min:0',
            'max_stock' => 'nullable|numeric|min:0',
            'reorder_point' => 'nullable|numeric|min:0',
            'lead_time_days' => 'nullable|integer|min:0',
            'shelf_life_days' => 'nullable|integer|min:0',
            'cost_method' => 'required|in:fifo,lifo,average,standard',
            'standard_cost' => 'nullable|numeric|min:0',
            'list_price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'tax_category' => 'nullable|string|max:50',
            'notes' => 'nullable|string',
            'status' => 'in:active,inactive,discontinued',
            'image' => 'nullable|image|max:2048',
        ];
    }

    protected function performStore(array $data): Item
    {
        if (isset($data['image'])) {
            $image = $data['image'];
            $filename = Str::uuid() . '.' . $image->getClientOriginalExtension();
            $image->storeAs('items', $filename, 'public');
            $data['image'] = $filename;
        }

        $item = Item::create(array_merge($data, [
            'uuid' => Str::uuid(),
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]));

        event(new ItemCreated($item));
        
        return $item->load('category', 'unit');
    }

    protected function performUpdate($item, array $data): Item
    {
        if (isset($data['image'])) {
            $image = $data['image'];
            $filename = Str::uuid() . '.' . $image->getClientOriginalExtension();
            $image->storeAs('items', $filename, 'public');
            $data['image'] = $filename;
        }

        $item->update(array_merge($data, [
            'updated_by' => auth()->id(),
        ]));

        event(new ItemUpdated($item));
        
        return $item->load('category', 'unit');
    }

    protected function performDestroy($item): void
    {
        $item->delete();
        event(new ItemDeleted($item));
    }

    public function stockSummary(Item $item)
    {
        $this->authorize('view', $item);
        
        $summary = $this->stockService->getStockSummary($item);
        
        return $this->successResponse([
            'data' => $summary
        ]);
    }

    public function movementHistory(Request $request, Item $item)
    {
        $this->authorize('view', $item);
        
        $filters = $request->only(['warehouse_id', 'movement_type', 'date_from', 'date_to', 'per_page']);
        $movements = $this->stockService->getMovementHistory($item, $filters);
        
        return $this->successResponse([
            'data' => $movements->items(),
            'meta' => $this->getPaginationMeta($movements)
        ]);
    }

    public function lowStock(Request $request)
    {
        $this->authorize('viewAny', Item::class);
        
        $items = Item::with(['category', 'unit', 'stockLevels.warehouse'])
            ->lowStock()
            ->paginate($request->get('per_page', 15));
        
        return $this->successResponse([
            'data' => ItemResource::collection($items->items()),
            'meta' => $this->getPaginationMeta($items)
        ]);
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create inventory management structure
mkdir -p frontend/src/modules/inventory/{components,pages,hooks,services,types}
```

### **2.2 Inventory Service**

```typescript
// frontend/src/modules/inventory/services/inventoryService.ts
import { apiClient } from '@/core/api/apiClient';

export interface Item {
  id: number;
  uuid: string;
  code: string;
  barcode?: string;
  name: string;
  description?: string;
  category?: ItemCategory;
  brand?: string;
  model?: string;
  type: 'product' | 'service' | 'raw_material' | 'finished_good' | 'consumable';
  unit: Unit;
  purchase_unit?: Unit;
  sale_unit?: Unit;
  weight?: number;
  volume?: number;
  dimensions?: any;
  image?: string;
  image_url?: string;
  is_trackable: boolean;
  is_serialized: boolean;
  is_batch_tracked: boolean;
  min_stock?: number;
  max_stock?: number;
  reorder_point?: number;
  lead_time_days?: number;
  shelf_life_days?: number;
  cost_method: 'fifo' | 'lifo' | 'average' | 'standard';
  standard_cost?: number;
  last_cost?: number;
  average_cost?: number;
  list_price?: number;
  sale_price?: number;
  tax_category?: string;
  notes?: string;
  status: 'active' | 'inactive' | 'discontinued';
  total_stock?: number;
  available_stock?: number;
  stock_levels?: StockLevel[];
  created_at: string;
  updated_at: string;
}

export interface ItemCategory {
  id: number;
  code: string;
  name: string;
  description?: string;
  parent_id?: number;
  level: number;
  image?: string;
}

export interface Unit {
  id: number;
  code: string;
  name: string;
  symbol?: string;
  type: string;
}

export interface Warehouse {
  id: number;
  uuid: string;
  code: string;
  name: string;
  description?: string;
  type: string;
  address?: string;
  is_active: boolean;
}

export interface StockLevel {
  id: number;
  item_id: number;
  warehouse_id: number;
  warehouse: Warehouse;
  quantity_on_hand: number;
  quantity_reserved: number;
  quantity_available: number;
  quantity_on_order: number;
  last_movement_at?: string;
}

export interface StockMovement {
  id: number;
  uuid: string;
  item: Item;
  warehouse: Warehouse;
  movement_type: 'in' | 'out' | 'transfer' | 'adjustment';
  transaction_type: string;
  reference_type?: string;
  reference_id?: number;
  reference_number?: string;
  batch_number?: string;
  serial_number?: string;
  quantity: number;
  unit: Unit;
  unit_cost: number;
  total_cost: number;
  reason?: string;
  notes?: string;
  movement_date: string;
  created_by?: any;
  created_at: string;
}

export interface ItemFilters {
  search?: string;
  category_id?: string;
  type?: string;
  status?: string;
  low_stock?: boolean;
  page?: number;
  per_page?: number;
  sort?: string;
  include?: string[];
}

class InventoryService {
  // Items
  async getItems(filters: ItemFilters = {}) {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        if (key === 'include' && Array.isArray(value)) {
          params.append(key, value.join(','));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    return apiClient.get(`/items?${params.toString()}`);
  }

  async getItem(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/items/${id}${params}`);
  }

  async createItem(data: FormData) {
    return apiClient.post('/items', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async updateItem(id: number, data: FormData) {
    return apiClient.post(`/items/${id}`, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-HTTP-Method-Override': 'PUT',
      },
    });
  }

  async deleteItem(id: number) {
    return apiClient.delete(`/items/${id}`);
  }

  async getStockSummary(itemId: number) {
    return apiClient.get(`/items/${itemId}/stock-summary`);
  }

  async getMovementHistory(itemId: number, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/items/${itemId}/movement-history?${params.toString()}`);
  }

  async getLowStockItems(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/items/low-stock?${params.toString()}`);
  }

  // Categories
  async getCategories() {
    return apiClient.get('/categories');
  }

  // Warehouses
  async getWarehouses() {
    return apiClient.get('/warehouses');
  }

  // Units
  async getUnits() {
    return apiClient.get('/units');
  }

  // Stock operations
  async moveStock(data: {
    item_id: number;
    warehouse_id: number;
    movement_type: 'in' | 'out';
    quantity: number;
    transaction_type: string;
    unit_cost?: number;
    reason?: string;
    notes?: string;
    batch_number?: string;
    serial_number?: string;
  }) {
    return apiClient.post('/stock/move', data);
  }

  async transferStock(data: {
    item_id: number;
    from_warehouse_id: number;
    to_warehouse_id: number;
    quantity: number;
    reason?: string;
    notes?: string;
  }) {
    return apiClient.post('/stock/transfer', data);
  }

  async adjustStock(data: {
    item_id: number;
    warehouse_id: number;
    new_quantity: number;
    reason: string;
    notes?: string;
  }) {
    return apiClient.post('/stock/adjust', data);
  }
}

export const inventoryService = new InventoryService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/Inventory/Routes/api.php

use Modules\Inventory\Http\Controllers\ItemController;
use Modules\Inventory\Http\Controllers\CategoryController;
use Modules\Inventory\Http\Controllers\WarehouseController;
use Modules\Inventory\Http\Controllers\StockController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Items
    Route::apiResource('items', ItemController::class);
    Route::get('items/{item}/stock-summary', [ItemController::class, 'stockSummary']);
    Route::get('items/{item}/movement-history', [ItemController::class, 'movementHistory']);
    Route::get('items/low-stock', [ItemController::class, 'lowStock']);
    
    // Categories
    Route::apiResource('categories', CategoryController::class);
    
    // Warehouses
    Route::apiResource('warehouses', WarehouseController::class);
    
    // Stock operations
    Route::prefix('stock')->group(function () {
        Route::post('move', [StockController::class, 'move']);
        Route::post('transfer', [StockController::class, 'transfer']);
        Route::post('adjust', [StockController::class, 'adjust']);
        Route::get('levels', [StockController::class, 'levels']);
        Route::get('movements', [StockController::class, 'movements']);
    });
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Item CRUD operations working
- [ ] Stock movement recording functional
- [ ] Stock level updates automatic
- [ ] Multi-warehouse support working
- [ ] Low stock alerts functional
- [ ] Inventory valuation accurate

### **Frontend**
- [ ] Item management interface working
- [ ] Stock operations interface functional
- [ ] Real-time stock display working
- [ ] Search and filtering working
- [ ] Image upload working

### **Integration**
- [ ] Frontend-backend integration working
- [ ] Stock movements reflected in UI
- [ ] Permission-based access working
- [ ] Error handling working

## 📞 NEXT STEPS

Setelah Inventory Management module selesai:

1. **Test inventory operations** end-to-end
2. **Verify stock calculations** accuracy
3. **Test multi-warehouse functionality**
4. **Commit module** ke repository
5. **Lanjut ke** `05_ACCOUNTING_MODULE.md` untuk implementasi accounting module

---

**IMPORTANT**: Inventory module adalah core business module yang akan digunakan oleh banyak modul lainnya. Pastikan semua functionality bekerja dengan akurat sebelum melanjutkan.
