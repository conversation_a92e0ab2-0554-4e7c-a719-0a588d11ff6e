<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'manage_user_roles',
            
            // Farm Management
            'view_farms',
            'create_farms',
            'edit_farms',
            'delete_farms',
            'manage_farms',
            
            // House Management
            'view_houses',
            'create_houses',
            'edit_houses',
            'delete_houses',
            'manage_houses',
            
            // Flock Management
            'view_flocks',
            'create_flocks',
            'edit_flocks',
            'delete_flocks',
            'manage_flocks',
            'update_flock_population',
            'transfer_flocks',
            
            // Breed Management
            'view_breeds',
            'create_breeds',
            'edit_breeds',
            'delete_breeds',
            'manage_breeds',
            
            // Production Management
            'view_production',
            'create_production_records',
            'edit_production_records',
            'delete_production_records',
            'manage_production',
            
            // Inventory Management
            'view_inventory',
            'create_inventory_items',
            'edit_inventory_items',
            'delete_inventory_items',
            'manage_inventory',
            'adjust_stock',
            'transfer_stock',
            
            // Sales Management
            'view_sales',
            'create_sales',
            'edit_sales',
            'delete_sales',
            'manage_sales',
            'approve_sales',
            
            // Purchasing Management
            'view_purchases',
            'create_purchases',
            'edit_purchases',
            'delete_purchases',
            'manage_purchases',
            'approve_purchases',
            
            // Financial Management
            'view_financials',
            'create_financial_records',
            'edit_financial_records',
            'delete_financial_records',
            'manage_financials',
            'view_reports',
            'generate_reports',
            
            // System Administration
            'manage_system_settings',
            'view_system_logs',
            'manage_backups',
            'manage_integrations',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        
        // Super Admin - Full access
        $superAdmin = Role::create([
            'name' => 'super_admin',
            'display_name' => 'Super Administrator',
            'description' => 'Full system access with all permissions',
        ]);
        $superAdmin->givePermissionTo(Permission::all());

        // Farm Manager - Farm operations management
        $farmManager = Role::create([
            'name' => 'farm_manager',
            'display_name' => 'Farm Manager',
            'description' => 'Manages farm operations, houses, flocks, and production',
        ]);
        $farmManager->givePermissionTo([
            'view_farms', 'edit_farms', 'manage_farms',
            'view_houses', 'create_houses', 'edit_houses', 'manage_houses',
            'view_flocks', 'create_flocks', 'edit_flocks', 'manage_flocks', 'update_flock_population', 'transfer_flocks',
            'view_breeds', 'create_breeds', 'edit_breeds',
            'view_production', 'create_production_records', 'edit_production_records', 'manage_production',
            'view_inventory', 'create_inventory_items', 'edit_inventory_items', 'adjust_stock', 'transfer_stock',
            'view_sales', 'create_sales', 'edit_sales',
            'view_purchases', 'create_purchases', 'edit_purchases',
            'view_financials', 'view_reports',
            'view_users', 'create_users', 'edit_users',
        ]);

        // Production Supervisor - Production and inventory management
        $productionSupervisor = Role::create([
            'name' => 'production_supervisor',
            'display_name' => 'Production Supervisor',
            'description' => 'Supervises production activities and inventory management',
        ]);
        $productionSupervisor->givePermissionTo([
            'view_farms', 'view_houses', 'view_flocks',
            'view_breeds',
            'view_production', 'create_production_records', 'edit_production_records', 'manage_production',
            'view_inventory', 'create_inventory_items', 'edit_inventory_items', 'adjust_stock', 'transfer_stock',
            'view_sales', 'create_sales',
            'view_purchases', 'create_purchases',
            'view_financials', 'view_reports',
        ]);

        // Farm Worker - Basic production operations
        $farmWorker = Role::create([
            'name' => 'farm_worker',
            'display_name' => 'Farm Worker',
            'description' => 'Performs daily farm operations and data entry',
        ]);
        $farmWorker->givePermissionTo([
            'view_farms', 'view_houses', 'view_flocks',
            'view_breeds',
            'view_production', 'create_production_records', 'edit_production_records',
            'view_inventory',
            'view_sales',
            'view_purchases',
        ]);

        // Sales Manager - Sales and customer management
        $salesManager = Role::create([
            'name' => 'sales_manager',
            'display_name' => 'Sales Manager',
            'description' => 'Manages sales operations and customer relationships',
        ]);
        $salesManager->givePermissionTo([
            'view_farms', 'view_houses', 'view_flocks',
            'view_production',
            'view_inventory',
            'view_sales', 'create_sales', 'edit_sales', 'delete_sales', 'manage_sales', 'approve_sales',
            'view_financials', 'view_reports',
        ]);

        // Purchasing Manager - Procurement management
        $purchasingManager = Role::create([
            'name' => 'purchasing_manager',
            'display_name' => 'Purchasing Manager',
            'description' => 'Manages procurement and vendor relationships',
        ]);
        $purchasingManager->givePermissionTo([
            'view_farms', 'view_houses', 'view_flocks',
            'view_inventory', 'create_inventory_items', 'edit_inventory_items',
            'view_purchases', 'create_purchases', 'edit_purchases', 'delete_purchases', 'manage_purchases', 'approve_purchases',
            'view_financials', 'view_reports',
        ]);

        // Accountant - Financial management
        $accountant = Role::create([
            'name' => 'accountant',
            'display_name' => 'Accountant',
            'description' => 'Manages financial records and reporting',
        ]);
        $accountant->givePermissionTo([
            'view_farms', 'view_houses', 'view_flocks',
            'view_production', 'view_inventory',
            'view_sales', 'view_purchases',
            'view_financials', 'create_financial_records', 'edit_financial_records', 'manage_financials',
            'view_reports', 'generate_reports',
        ]);

        // Veterinarian - Health management
        $veterinarian = Role::create([
            'name' => 'veterinarian',
            'display_name' => 'Veterinarian',
            'description' => 'Manages animal health and medical records',
        ]);
        $veterinarian->givePermissionTo([
            'view_farms', 'view_houses', 'view_flocks',
            'view_breeds',
            'view_production',
            'view_inventory',
        ]);

        // Create default super admin user
        $superAdminUser = User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $superAdminUser->assignRole('super_admin');

        // Create sample farm manager
        $farmManagerUser = User::create([
            'name' => 'Farm Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $farmManagerUser->assignRole('farm_manager');

        // Create sample farm worker
        $farmWorkerUser = User::create([
            'name' => 'Farm Worker',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $farmWorkerUser->assignRole('farm_worker');

        $this->command->info('Roles and permissions seeded successfully!');
        $this->command->info('Default users created:');
        $this->command->info('- Super Admin: <EMAIL> / password123');
        $this->command->info('- Farm Manager: <EMAIL> / password123');
        $this->command->info('- Farm Worker: <EMAIL> / password123');
    }
}
