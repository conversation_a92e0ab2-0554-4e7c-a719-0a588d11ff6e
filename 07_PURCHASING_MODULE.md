# 07 - PURCHASING MODULE

## 📋 OVERVIEW

Modul Purchasing mengelola seluruh proses procurement dari purchase requisition hingga vendor payment. Modul ini terintegrasi dengan inventory untuk stock replenishment, accounting untuk automatic journal posting, dan approval workflow untuk purchase authorization.

## 🎯 TUJUAN

- Manajemen vendor dan supplier database
- Purchase requisition dan approval workflow
- Purchase order processing dan tracking
- Goods receipt dan quality control
- Vendor invoice matching dan payment
- Purchase analytics dan cost analysis
- Integration dengan inventory untuk automatic reordering

## ⏱️ ESTIMASI WAKTU

**Total**: 22-26 jam
- Backend implementation: 14-18 jam
- Frontend implementation: 8-10 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Procurement Manager** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Vendor management
vendors
vendor_contacts
vendor_addresses
vendor_price_lists

-- Purchase process
purchase_requisitions
purchase_requisition_items
purchase_orders
purchase_order_items
goods_receipts
goods_receipt_items
vendor_invoices
vendor_invoice_items

-- Supporting tables
approval_workflows
purchase_categories
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Purchasing module
php artisan module:make Purchasing

# Create module components
php artisan module:make-controller Purchasing VendorController --api
php artisan module:make-controller Purchasing PurchaseRequisitionController --api
php artisan module:make-controller Purchasing PurchaseOrderController --api
php artisan module:make-controller Purchasing GoodsReceiptController --api
php artisan module:make-controller Purchasing VendorInvoiceController --api
php artisan module:make-controller Purchasing PurchaseReportController --api
php artisan module:make-model Purchasing Vendor
php artisan module:make-model Purchasing PurchaseRequisition
php artisan module:make-model Purchasing PurchaseOrder
php artisan module:make-model Purchasing GoodsReceipt
php artisan module:make-model Purchasing VendorInvoice
php artisan module:make-request Purchasing VendorStoreRequest
php artisan module:make-request Purchasing PurchaseOrderStoreRequest
php artisan module:make-resource Purchasing VendorResource
php artisan module:make-resource Purchasing PurchaseOrderResource
php artisan module:make-policy Purchasing PurchasingPolicy
php artisan module:make-seeder Purchasing PurchasingSeeder
```

### **1.2 Vendor Model**

```php
<?php
// Modules/Purchasing/Entities/Vendor.php

namespace Modules\Purchasing\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Vendor extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'vendor_code',
        'vendor_name',
        'company_name',
        'vendor_type',
        'tax_id',
        'email',
        'phone',
        'mobile',
        'website',
        'industry',
        'vendor_group',
        'payment_terms_id',
        'currency_id',
        'credit_limit',
        'credit_days',
        'discount_percentage',
        'tax_exempt',
        'lead_time_days',
        'minimum_order_amount',
        'billing_address',
        'shipping_address',
        'bank_details',
        'certification',
        'quality_rating',
        'delivery_rating',
        'price_rating',
        'overall_rating',
        'notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'credit_days' => 'integer',
        'discount_percentage' => 'decimal:2',
        'tax_exempt' => 'boolean',
        'lead_time_days' => 'integer',
        'minimum_order_amount' => 'decimal:2',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'bank_details' => 'array',
        'certification' => 'array',
        'quality_rating' => 'decimal:1',
        'delivery_rating' => 'decimal:1',
        'price_rating' => 'decimal:1',
        'overall_rating' => 'decimal:1',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['vendor_name', 'status', 'overall_rating', 'payment_terms_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function paymentTerms()
    {
        return $this->belongsTo(PaymentTerms::class, 'payment_terms_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function contacts()
    {
        return $this->hasMany(VendorContact::class);
    }

    public function addresses()
    {
        return $this->hasMany(VendorAddress::class);
    }

    public function priceLists()
    {
        return $this->hasMany(VendorPriceList::class);
    }

    public function purchaseOrders()
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    public function invoices()
    {
        return $this->hasMany(VendorInvoice::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('vendor_name', 'like', "%{$search}%")
              ->orWhere('vendor_code', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    public function scopeByType($query, $type)
    {
        return $query->where('vendor_type', $type);
    }

    public function scopeByRating($query, $minRating)
    {
        return $query->where('overall_rating', '>=', $minRating);
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->company_name ?: $this->vendor_name;
    }

    public function getCurrentBalanceAttribute(): float
    {
        return $this->invoices()
            ->where('status', '!=', 'paid')
            ->sum('total_amount');
    }

    public function getAvailableCreditAttribute(): float
    {
        return max(0, $this->credit_limit - $this->current_balance);
    }

    // Methods
    public function updateRatings(): void
    {
        $recentOrders = $this->purchaseOrders()
            ->where('created_at', '>=', now()->subMonths(12))
            ->where('status', 'completed')
            ->get();

        if ($recentOrders->count() > 0) {
            $qualityRating = $recentOrders->avg('quality_rating') ?: 0;
            $deliveryRating = $recentOrders->avg('delivery_rating') ?: 0;
            $priceRating = $recentOrders->avg('price_rating') ?: 0;
            
            $overallRating = ($qualityRating + $deliveryRating + $priceRating) / 3;

            $this->update([
                'quality_rating' => $qualityRating,
                'delivery_rating' => $deliveryRating,
                'price_rating' => $priceRating,
                'overall_rating' => $overallRating,
            ]);
        }
    }

    public function getPurchaseHistory(string $period = '12m'): array
    {
        $startDate = now()->sub($period);
        
        $orders = $this->purchaseOrders()
            ->where('order_date', '>=', $startDate)
            ->where('status', '!=', 'cancelled')
            ->get();

        $invoices = $this->invoices()
            ->where('invoice_date', '>=', $startDate)
            ->where('status', '!=', 'cancelled')
            ->get();

        return [
            'total_orders' => $orders->count(),
            'total_order_value' => $orders->sum('total_amount'),
            'total_invoices' => $invoices->count(),
            'total_invoice_value' => $invoices->sum('total_amount'),
            'average_order_value' => $orders->count() > 0 ? $orders->avg('total_amount') : 0,
            'average_delivery_time' => $orders->avg('actual_delivery_days'),
            'on_time_delivery_rate' => $this->calculateOnTimeDeliveryRate($orders),
            'last_order_date' => $orders->max('order_date'),
        ];
    }

    private function calculateOnTimeDeliveryRate($orders): float
    {
        $completedOrders = $orders->where('status', 'completed');
        
        if ($completedOrders->count() === 0) {
            return 0;
        }

        $onTimeOrders = $completedOrders->filter(function ($order) {
            return $order->actual_delivery_date <= $order->expected_delivery_date;
        });

        return ($onTimeOrders->count() / $completedOrders->count()) * 100;
    }

    public static function generateVendorCode(): string
    {
        $year = now()->year;
        $prefix = "VEND{$year}";
        
        $lastVendor = static::where('vendor_code', 'like', $prefix . '%')
            ->orderBy('vendor_code', 'desc')
            ->first();
        
        if ($lastVendor) {
            $lastNumber = (int) substr($lastVendor->vendor_code, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.3 Purchase Order Model**

```php
<?php
// Modules/Purchasing/Entities/PurchaseOrder.php

namespace Modules\Purchasing\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class PurchaseOrder extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'po_number',
        'vendor_id',
        'requisition_id',
        'order_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'warehouse_id',
        'buyer_id',
        'payment_terms_id',
        'currency_id',
        'exchange_rate',
        'subtotal',
        'discount_amount',
        'tax_amount',
        'shipping_amount',
        'total_amount',
        'delivery_address',
        'terms_conditions',
        'notes',
        'internal_notes',
        'status',
        'approved_by',
        'approved_at',
        'quality_rating',
        'delivery_rating',
        'price_rating',
        'transaction_type',
        'barter_agreement_id',
        'cash_amount',
        'barter_value',
        'barter_settlement_status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'order_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'exchange_rate' => 'decimal:6',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'delivery_address' => 'array',
        'approved_at' => 'datetime',
        'quality_rating' => 'decimal:1',
        'delivery_rating' => 'decimal:1',
        'price_rating' => 'decimal:1',
        'cash_amount' => 'decimal:2',
        'barter_value' => 'decimal:2',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'total_amount', 'expected_delivery_date'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function requisition()
    {
        return $this->belongsTo(PurchaseRequisition::class, 'requisition_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(\Modules\Inventory\Entities\Warehouse::class);
    }

    public function buyer()
    {
        return $this->belongsTo(\App\Models\User::class, 'buyer_id');
    }

    public function paymentTerms()
    {
        return $this->belongsTo(PaymentTerms::class, 'payment_terms_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function items()
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    public function goodsReceipts()
    {
        return $this->hasMany(GoodsReceipt::class);
    }

    public function invoices()
    {
        return $this->hasMany(VendorInvoice::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    public function barterAgreement()
    {
        return $this->belongsTo(\Modules\BarterTrade\Entities\BarterAgreement::class, 'barter_agreement_id');
    }

    public function barterTransactions()
    {
        return $this->morphMany(\Modules\BarterTrade\Entities\BarterTransaction::class, 'reference');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('order_date', [$startDate, $endDate]);
    }

    public function scopeByVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    public function scopeByBuyer($query, $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    // Accessors
    public function getIsApprovedAttribute(): bool
    {
        return $this->status === 'approved';
    }

    public function getCanBeApprovedAttribute(): bool
    {
        return $this->status === 'pending';
    }

    public function getCanBeCancelledAttribute(): bool
    {
        return in_array($this->status, ['pending', 'approved']) && 
               $this->goodsReceipts()->where('status', '!=', 'cancelled')->count() === 0;
    }

    public function getReceivedQuantityAttribute(): float
    {
        return $this->goodsReceipts()
            ->where('status', 'received')
            ->with('items')
            ->get()
            ->flatMap->items
            ->sum('quantity_received');
    }

    public function getInvoicedAmountAttribute(): float
    {
        return $this->invoices()
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');
    }

    public function getOutstandingAmountAttribute(): float
    {
        return $this->total_amount - $this->invoiced_amount;
    }

    public function getIsFullyReceivedAttribute(): bool
    {
        $orderedQuantity = $this->items->sum('quantity');
        return $orderedQuantity > 0 && $this->received_quantity >= $orderedQuantity;
    }

    public function getIsFullyInvoicedAttribute(): bool
    {
        return $this->outstanding_amount <= 0.01;
    }

    public function getActualDeliveryDaysAttribute(): ?int
    {
        if ($this->actual_delivery_date && $this->order_date) {
            return $this->order_date->diffInDays($this->actual_delivery_date);
        }
        return null;
    }

    public function getIsMixedPaymentAttribute(): bool
    {
        return $this->transaction_type === 'mixed';
    }

    public function getIsBarterOnlyAttribute(): bool
    {
        return $this->transaction_type === 'barter';
    }

    public function getBarterPercentageAttribute(): float
    {
        return $this->total_amount > 0 ? ($this->barter_value / $this->total_amount) * 100 : 0;
    }

    public function getCashPercentageAttribute(): float
    {
        return $this->total_amount > 0 ? ($this->cash_amount / $this->total_amount) * 100 : 0;
    }

    public function getBarterSettlementPercentageAttribute(): float
    {
        if ($this->barter_value <= 0) return 100;

        $settledValue = $this->barterTransactions()
            ->where('settlement_status', 'settled')
            ->sum('barter_component');

        return ($settledValue / $this->barter_value) * 100;
    }

    // Methods
    public function calculateTotals(): void
    {
        $subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });

        $discountAmount = $subtotal * ($this->discount_percentage / 100);
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * ($this->tax_percentage / 100);
        $totalAmount = $taxableAmount + $taxAmount + $this->shipping_amount;

        $this->update([
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }

    public function approve(): bool
    {
        if (!$this->can_be_approved) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => auth()->id(),
            'approved_at' => now(),
        ]);

        return true;
    }

    public function cancel(string $reason = null): bool
    {
        if (!$this->can_be_cancelled) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . "\nCancelled: " . ($reason ?: 'No reason provided'),
        ]);

        return true;
    }

    public function complete(): bool
    {
        if (!$this->is_fully_received) {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'actual_delivery_date' => now()->toDateString(),
        ]);

        // Update vendor ratings
        $this->vendor->updateRatings();

        return true;
    }

    public static function generatePONumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "PO{$year}{$month}";
        
        $lastPO = static::where('po_number', 'like', $prefix . '%')
            ->orderBy('po_number', 'desc')
            ->first();
        
        if ($lastPO) {
            $lastNumber = (int) substr($lastPO->po_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.4 Purchasing Service**

```php
<?php
// Modules/Purchasing/Services/PurchasingService.php

namespace Modules\Purchasing\Services;

use Modules\Purchasing\Entities\PurchaseOrder;
use Modules\Purchasing\Entities\GoodsReceipt;
use Modules\Purchasing\Entities\VendorInvoice;
use Modules\Purchasing\Events\PurchaseOrderApproved;
use Modules\Purchasing\Events\GoodsReceived;
use Modules\Purchasing\Events\VendorInvoiceCreated;
use Modules\Accounting\Services\AccountingService;
use Modules\Inventory\Services\StockService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PurchasingService
{
    protected AccountingService $accountingService;
    protected StockService $stockService;

    public function __construct(AccountingService $accountingService, StockService $stockService)
    {
        $this->accountingService = $accountingService;
        $this->stockService = $stockService;
    }

    public function createPurchaseOrder(array $data): PurchaseOrder
    {
        return DB::transaction(function () use ($data) {
            $purchaseOrder = PurchaseOrder::create([
                'uuid' => Str::uuid(),
                'po_number' => PurchaseOrder::generatePONumber(),
                'vendor_id' => $data['vendor_id'],
                'requisition_id' => $data['requisition_id'] ?? null,
                'order_date' => $data['order_date'],
                'expected_delivery_date' => $data['expected_delivery_date'],
                'warehouse_id' => $data['warehouse_id'],
                'buyer_id' => $data['buyer_id'] ?? auth()->id(),
                'payment_terms_id' => $data['payment_terms_id'],
                'currency_id' => $data['currency_id'] ?? 1,
                'exchange_rate' => $data['exchange_rate'] ?? 1.0,
                'shipping_amount' => $data['shipping_amount'] ?? 0,
                'delivery_address' => $data['delivery_address'] ?? null,
                'terms_conditions' => $data['terms_conditions'] ?? null,
                'notes' => $data['notes'] ?? null,
                'internal_notes' => $data['internal_notes'] ?? null,
                'status' => 'pending',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            foreach ($data['items'] as $itemData) {
                $purchaseOrder->items()->create([
                    'uuid' => Str::uuid(),
                    'item_id' => $itemData['item_id'],
                    'description' => $itemData['description'] ?? null,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'discount_percentage' => $itemData['discount_percentage'] ?? 0,
                    'tax_percentage' => $itemData['tax_percentage'] ?? 0,
                    'line_total' => $itemData['quantity'] * $itemData['unit_price'],
                    'expected_date' => $itemData['expected_date'] ?? $purchaseOrder->expected_delivery_date,
                ]);
            }

            $purchaseOrder->calculateTotals();

            return $purchaseOrder;
        });
    }

    public function approvePurchaseOrder(PurchaseOrder $purchaseOrder): bool
    {
        $result = $purchaseOrder->approve();
        
        if ($result) {
            event(new PurchaseOrderApproved($purchaseOrder));
        }
        
        return $result;
    }

    public function createGoodsReceipt(PurchaseOrder $purchaseOrder, array $items): GoodsReceipt
    {
        return DB::transaction(function () use ($purchaseOrder, $items) {
            $goodsReceipt = GoodsReceipt::create([
                'uuid' => Str::uuid(),
                'receipt_number' => GoodsReceipt::generateReceiptNumber(),
                'purchase_order_id' => $purchaseOrder->id,
                'vendor_id' => $purchaseOrder->vendor_id,
                'warehouse_id' => $purchaseOrder->warehouse_id,
                'receipt_date' => now()->toDateString(),
                'received_by' => auth()->id(),
                'status' => 'pending',
                'notes' => null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            foreach ($items as $itemData) {
                $goodsReceipt->items()->create([
                    'uuid' => Str::uuid(),
                    'purchase_order_item_id' => $itemData['purchase_order_item_id'],
                    'item_id' => $itemData['item_id'],
                    'quantity_ordered' => $itemData['quantity_ordered'],
                    'quantity_received' => $itemData['quantity_received'],
                    'unit_price' => $itemData['unit_price'],
                    'batch_number' => $itemData['batch_number'] ?? null,
                    'expiry_date' => $itemData['expiry_date'] ?? null,
                    'quality_status' => $itemData['quality_status'] ?? 'pending',
                    'notes' => $itemData['notes'] ?? null,
                ]);
            }

            return $goodsReceipt;
        });
    }

    public function receiveGoods(GoodsReceipt $goodsReceipt): bool
    {
        return DB::transaction(function () use ($goodsReceipt) {
            // Update receipt status
            $goodsReceipt->update([
                'status' => 'received',
                'received_at' => now(),
            ]);

            // Update inventory
            foreach ($goodsReceipt->items as $item) {
                if ($item->quality_status === 'approved') {
                    $this->stockService->moveStock(
                        $item->item,
                        $goodsReceipt->warehouse,
                        'in',
                        $item->quantity_received,
                        'purchase_receipt',
                        [
                            'reference_type' => GoodsReceipt::class,
                            'reference_id' => $goodsReceipt->id,
                            'reference_number' => $goodsReceipt->receipt_number,
                            'unit_cost' => $item->unit_price,
                            'batch_number' => $item->batch_number,
                            'reason' => 'Goods receipt',
                        ]
                    );
                }
            }

            // Check if PO is fully received
            if ($goodsReceipt->purchaseOrder->is_fully_received) {
                $goodsReceipt->purchaseOrder->complete();
            }

            event(new GoodsReceived($goodsReceipt));

            return true;
        });
    }

    public function createVendorInvoice(PurchaseOrder $purchaseOrder, array $data): VendorInvoice
    {
        return DB::transaction(function () use ($purchaseOrder, $data) {
            $invoice = VendorInvoice::create([
                'uuid' => Str::uuid(),
                'invoice_number' => $data['invoice_number'],
                'vendor_invoice_number' => $data['vendor_invoice_number'],
                'purchase_order_id' => $purchaseOrder->id,
                'vendor_id' => $purchaseOrder->vendor_id,
                'invoice_date' => $data['invoice_date'],
                'due_date' => $data['due_date'],
                'payment_terms_id' => $purchaseOrder->payment_terms_id,
                'currency_id' => $purchaseOrder->currency_id,
                'exchange_rate' => $data['exchange_rate'] ?? $purchaseOrder->exchange_rate,
                'subtotal' => $data['subtotal'],
                'discount_amount' => $data['discount_amount'] ?? 0,
                'tax_amount' => $data['tax_amount'] ?? 0,
                'total_amount' => $data['total_amount'],
                'status' => 'pending',
                'notes' => $data['notes'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            foreach ($data['items'] as $itemData) {
                $invoice->items()->create([
                    'uuid' => Str::uuid(),
                    'purchase_order_item_id' => $itemData['purchase_order_item_id'],
                    'item_id' => $itemData['item_id'],
                    'description' => $itemData['description'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'line_total' => $itemData['quantity'] * $itemData['unit_price'],
                ]);
            }

            // Create accounting entries
            $this->createPurchaseAccountingEntries($invoice);

            event(new VendorInvoiceCreated($invoice));

            return $invoice;
        });
    }

    private function createPurchaseAccountingEntries(VendorInvoice $invoice): void
    {
        $entries = [];

        // Inventory/Expense (Debit)
        foreach ($invoice->items as $item) {
            $accountId = $item->item->type === 'inventory' 
                ? config('purchasing.accounts.inventory')
                : config('purchasing.accounts.expense');

            $entries[] = [
                'account_id' => $accountId,
                'description' => "Purchase from {$invoice->vendor->vendor_name}",
                'debit_amount' => $item->line_total,
                'credit_amount' => 0,
            ];
        }

        // Purchase Tax (Debit)
        if ($invoice->tax_amount > 0) {
            $entries[] = [
                'account_id' => config('purchasing.accounts.purchase_tax'),
                'description' => "Purchase tax",
                'debit_amount' => $invoice->tax_amount,
                'credit_amount' => 0,
            ];
        }

        // Accounts Payable (Credit)
        $entries[] = [
            'account_id' => config('purchasing.accounts.accounts_payable'),
            'description' => "Purchase from {$invoice->vendor->vendor_name}",
            'debit_amount' => 0,
            'credit_amount' => $invoice->total_amount,
        ];

        $this->accountingService->createAutomaticEntry(
            VendorInvoice::class,
            $invoice->id,
            $entries
        );
    }

    public function getPurchaseAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();
        $vendorId = $filters['vendor_id'] ?? null;
        $buyerId = $filters['buyer_id'] ?? null;

        $ordersQuery = PurchaseOrder::byDateRange($startDate, $endDate);
        $invoicesQuery = VendorInvoice::whereBetween('invoice_date', [$startDate, $endDate]);

        if ($vendorId) {
            $ordersQuery->where('vendor_id', $vendorId);
            $invoicesQuery->where('vendor_id', $vendorId);
        }

        if ($buyerId) {
            $ordersQuery->where('buyer_id', $buyerId);
        }

        $orders = $ordersQuery->get();
        $invoices = $invoicesQuery->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'orders' => [
                'count' => $orders->count(),
                'total_value' => $orders->sum('total_amount'),
                'average_value' => $orders->avg('total_amount'),
                'pending_count' => $orders->where('status', 'pending')->count(),
                'approved_count' => $orders->where('status', 'approved')->count(),
                'completed_count' => $orders->where('status', 'completed')->count(),
            ],
            'invoices' => [
                'count' => $invoices->count(),
                'total_value' => $invoices->sum('total_amount'),
                'paid_value' => $invoices->where('status', 'paid')->sum('total_amount'),
                'outstanding_value' => $invoices->where('status', '!=', 'paid')->sum('total_amount'),
            ],
            'top_vendors' => $this->getTopVendors($startDate, $endDate, 5),
            'purchase_by_category' => $this->getPurchaseByCategory($startDate, $endDate),
            'delivery_performance' => $this->getDeliveryPerformance($startDate, $endDate),
        ];
    }

    private function getTopVendors(string $startDate, string $endDate, int $limit): array
    {
        return PurchaseOrder::with('vendor')
            ->byDateRange($startDate, $endDate)
            ->selectRaw('vendor_id, SUM(total_amount) as total_purchases, COUNT(*) as order_count')
            ->groupBy('vendor_id')
            ->orderBy('total_purchases', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($order) {
                return [
                    'vendor_name' => $order->vendor->vendor_name,
                    'total_purchases' => $order->total_purchases,
                    'order_count' => $order->order_count,
                ];
            })
            ->toArray();
    }

    private function getPurchaseByCategory(string $startDate, string $endDate): array
    {
        // Implementation for purchase by category analysis
        return [];
    }

    private function getDeliveryPerformance(string $startDate, string $endDate): array
    {
        $completedOrders = PurchaseOrder::byDateRange($startDate, $endDate)
            ->where('status', 'completed')
            ->whereNotNull('actual_delivery_date')
            ->get();

        $onTimeDeliveries = $completedOrders->filter(function ($order) {
            return $order->actual_delivery_date <= $order->expected_delivery_date;
        });

        return [
            'total_deliveries' => $completedOrders->count(),
            'on_time_deliveries' => $onTimeDeliveries->count(),
            'on_time_percentage' => $completedOrders->count() > 0 
                ? ($onTimeDeliveries->count() / $completedOrders->count()) * 100 
                : 0,
            'average_delivery_days' => $completedOrders->avg('actual_delivery_days'),
        ];
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create purchasing structure
mkdir -p frontend/src/modules/purchasing/{components,pages,hooks,services,types}
```

### **2.2 Purchasing Service**

```typescript
// frontend/src/modules/purchasing/services/purchasingService.ts
import { apiClient } from '@/core/api/apiClient';

export interface Vendor {
  id: number;
  uuid: string;
  vendor_code: string;
  vendor_name: string;
  company_name?: string;
  vendor_type: 'supplier' | 'contractor' | 'service_provider';
  tax_id?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  industry?: string;
  vendor_group?: string;
  payment_terms?: PaymentTerms;
  currency?: Currency;
  credit_limit: number;
  credit_days: number;
  discount_percentage: number;
  tax_exempt: boolean;
  lead_time_days: number;
  minimum_order_amount: number;
  billing_address?: any;
  shipping_address?: any;
  bank_details?: any;
  certification?: any;
  quality_rating: number;
  delivery_rating: number;
  price_rating: number;
  overall_rating: number;
  display_name: string;
  current_balance: number;
  available_credit: number;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
}

export interface PurchaseOrder {
  id: number;
  uuid: string;
  po_number: string;
  vendor: Vendor;
  requisition?: any;
  order_date: string;
  expected_delivery_date: string;
  actual_delivery_date?: string;
  warehouse: any;
  buyer?: any;
  payment_terms: PaymentTerms;
  currency: Currency;
  exchange_rate: number;
  subtotal: number;
  discount_amount: number;
  tax_amount: number;
  shipping_amount: number;
  total_amount: number;
  delivery_address?: any;
  terms_conditions?: string;
  notes?: string;
  internal_notes?: string;
  status: 'pending' | 'approved' | 'received' | 'completed' | 'cancelled';
  is_approved: boolean;
  can_be_approved: boolean;
  can_be_cancelled: boolean;
  received_quantity: number;
  invoiced_amount: number;
  outstanding_amount: number;
  is_fully_received: boolean;
  is_fully_invoiced: boolean;
  actual_delivery_days?: number;
  quality_rating?: number;
  delivery_rating?: number;
  price_rating?: number;
  approved_by?: any;
  approved_at?: string;
  items: PurchaseOrderItem[];
  created_at: string;
  updated_at: string;
}

export interface PurchaseOrderItem {
  id: number;
  uuid: string;
  item: any;
  description?: string;
  quantity: number;
  unit_price: number;
  discount_percentage: number;
  tax_percentage: number;
  line_total: number;
  expected_date?: string;
}

class PurchasingService {
  // Vendors
  async getVendors(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/vendors?${params.toString()}`);
  }

  async getVendor(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/vendors/${id}${params}`);
  }

  async createVendor(data: any) {
    return apiClient.post('/vendors', data);
  }

  async updateVendor(id: number, data: any) {
    return apiClient.put(`/vendors/${id}`, data);
  }

  async deleteVendor(id: number) {
    return apiClient.delete(`/vendors/${id}`);
  }

  async getVendorPurchaseHistory(vendorId: number, period: string = '12m') {
    return apiClient.get(`/vendors/${vendorId}/purchase-history?period=${period}`);
  }

  // Purchase Orders
  async getPurchaseOrders(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/purchase-orders?${params.toString()}`);
  }

  async getPurchaseOrder(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/purchase-orders/${id}${params}`);
  }

  async createPurchaseOrder(data: {
    vendor_id: number;
    order_date: string;
    expected_delivery_date: string;
    warehouse_id: number;
    buyer_id?: number;
    payment_terms_id: number;
    currency_id?: number;
    exchange_rate?: number;
    shipping_amount?: number;
    delivery_address?: any;
    terms_conditions?: string;
    notes?: string;
    internal_notes?: string;
    items: Array<{
      item_id: number;
      description?: string;
      quantity: number;
      unit_price: number;
      discount_percentage?: number;
      tax_percentage?: number;
      expected_date?: string;
    }>;
  }) {
    return apiClient.post('/purchase-orders', data);
  }

  async updatePurchaseOrder(id: number, data: any) {
    return apiClient.put(`/purchase-orders/${id}`, data);
  }

  async deletePurchaseOrder(id: number) {
    return apiClient.delete(`/purchase-orders/${id}`);
  }

  async approvePurchaseOrder(id: number) {
    return apiClient.post(`/purchase-orders/${id}/approve`);
  }

  async cancelPurchaseOrder(id: number, reason?: string) {
    return apiClient.post(`/purchase-orders/${id}/cancel`, { reason });
  }

  // Goods Receipts
  async getGoodsReceipts(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/goods-receipts?${params.toString()}`);
  }

  async createGoodsReceipt(purchaseOrderId: number, items: any[]) {
    return apiClient.post('/goods-receipts', {
      purchase_order_id: purchaseOrderId,
      items
    });
  }

  async receiveGoods(goodsReceiptId: number) {
    return apiClient.post(`/goods-receipts/${goodsReceiptId}/receive`);
  }

  // Vendor Invoices
  async getVendorInvoices(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/vendor-invoices?${params.toString()}`);
  }

  async createVendorInvoice(data: any) {
    return apiClient.post('/vendor-invoices', data);
  }

  async payVendorInvoice(invoiceId: number, paymentData: any) {
    return apiClient.post(`/vendor-invoices/${invoiceId}/pay`, paymentData);
  }

  // Analytics & Reports
  async getPurchaseAnalytics(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/purchasing/analytics?${params.toString()}`);
  }

  async getPurchaseReport(reportType: string, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/purchasing/reports/${reportType}?${params.toString()}`);
  }
}

export const purchasingService = new PurchasingService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/Purchasing/Routes/api.php

use Modules\Purchasing\Http\Controllers\VendorController;
use Modules\Purchasing\Http\Controllers\PurchaseRequisitionController;
use Modules\Purchasing\Http\Controllers\PurchaseOrderController;
use Modules\Purchasing\Http\Controllers\GoodsReceiptController;
use Modules\Purchasing\Http\Controllers\VendorInvoiceController;
use Modules\Purchasing\Http\Controllers\PurchaseReportController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Vendors
    Route::apiResource('vendors', VendorController::class);
    Route::get('vendors/{vendor}/purchase-history', [VendorController::class, 'purchaseHistory']);
    Route::post('vendors/{vendor}/update-rating', [VendorController::class, 'updateRating']);
    
    // Purchase Requisitions
    Route::apiResource('purchase-requisitions', PurchaseRequisitionController::class);
    Route::post('purchase-requisitions/{requisition}/approve', [PurchaseRequisitionController::class, 'approve']);
    Route::post('purchase-requisitions/{requisition}/convert', [PurchaseRequisitionController::class, 'convertToPO']);
    
    // Purchase Orders
    Route::apiResource('purchase-orders', PurchaseOrderController::class);
    Route::post('purchase-orders/{purchaseOrder}/approve', [PurchaseOrderController::class, 'approve']);
    Route::post('purchase-orders/{purchaseOrder}/cancel', [PurchaseOrderController::class, 'cancel']);
    
    // Goods Receipts
    Route::apiResource('goods-receipts', GoodsReceiptController::class);
    Route::post('goods-receipts/{goodsReceipt}/receive', [GoodsReceiptController::class, 'receive']);
    
    // Vendor Invoices
    Route::apiResource('vendor-invoices', VendorInvoiceController::class);
    Route::post('vendor-invoices/{invoice}/pay', [VendorInvoiceController::class, 'recordPayment']);
    
    // Purchase Analytics & Reports
    Route::prefix('purchasing')->group(function () {
        Route::get('analytics', [PurchaseReportController::class, 'analytics']);
        Route::get('reports/{type}', [PurchaseReportController::class, 'report']);
    });
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Vendor CRUD operations working
- [ ] Purchase order workflow complete
- [ ] Goods receipt processing functional
- [ ] Vendor invoice matching working
- [ ] Inventory integration accurate
- [ ] Accounting integration functional

### **Frontend**
- [ ] Vendor management interface
- [ ] Purchase order creation/approval
- [ ] Goods receipt interface
- [ ] Invoice processing
- [ ] Purchase analytics dashboard
- [ ] Vendor performance tracking

### **Integration**
- [ ] Inventory updates automatic
- [ ] Accounting entries correct
- [ ] Approval workflows working
- [ ] Multi-currency support
- [ ] Vendor rating calculations

## 📞 NEXT STEPS

Setelah Purchasing module selesai:

1. **Test purchase workflow** end-to-end
2. **Verify inventory integration** accuracy
3. **Test accounting integration**
4. **Validate vendor performance** calculations
5. **Commit module** ke repository
6. **Lanjut ke** `09_EGG_PRODUCTION.md`

---

**IMPORTANT**: Purchasing module adalah cost control center. Pastikan semua calculations akurat dan integration dengan inventory/accounting bekerja sempurna untuk cost tracking yang tepat.
