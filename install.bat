@echo off
setlocal enabledelayedexpansion

echo 🐔 ERP Poultry Management System - Installation Script
echo ======================================================

REM Check prerequisites
echo.
echo 🔍 Checking prerequisites...

REM Check PHP
php -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP not found. Please install PHP 8.1 or higher
    pause
    exit /b 1
) else (
    echo ✅ PHP found
)

REM Check Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer not found. Please install Composer
    pause
    exit /b 1
) else (
    echo ✅ Composer found
)

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 18 or higher
    pause
    exit /b 1
) else (
    echo ✅ Node.js found
)

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found. Please install npm
    pause
    exit /b 1
) else (
    echo ✅ npm found
)

echo.
echo 🚀 Starting installation...

REM Step 1: Install PHP dependencies
echo.
echo 📦 Installing PHP dependencies...
composer install --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ❌ Failed to install PHP dependencies
    pause
    exit /b 1
) else (
    echo ✅ PHP dependencies installed successfully
)

REM Step 2: Setup environment file
echo.
echo ⚙️ Setting up environment configuration...
if not exist .env (
    copy .env.example .env >nul
    if %errorlevel% neq 0 (
        echo ❌ Failed to create environment file
        pause
        exit /b 1
    ) else (
        echo ✅ Environment file created
    )
) else (
    echo ✅ Environment file already exists
)

REM Step 3: Generate application key
echo.
echo 🔑 Generating application key...
php artisan key:generate
if %errorlevel% neq 0 (
    echo ❌ Failed to generate application key
    pause
    exit /b 1
) else (
    echo ✅ Application key generated
)

REM Step 4: Database setup
echo.
echo 🗄️ Setting up database...
set /p DB_NAME="Enter database name (default: erp_poultry): "
if "%DB_NAME%"=="" set DB_NAME=erp_poultry

set /p DB_USER="Enter database username (default: root): "
if "%DB_USER%"=="" set DB_USER=root

set /p DB_PASS="Enter database password: "

REM Update .env file with database credentials
powershell -Command "(gc .env) -replace 'DB_DATABASE=erp_poultry', 'DB_DATABASE=%DB_NAME%' | Out-File -encoding ASCII .env"
powershell -Command "(gc .env) -replace 'DB_USERNAME=root', 'DB_USERNAME=%DB_USER%' | Out-File -encoding ASCII .env"
powershell -Command "(gc .env) -replace 'DB_PASSWORD=', 'DB_PASSWORD=%DB_PASS%' | Out-File -encoding ASCII .env"

echo ✅ Database configuration updated

REM Step 5: Create database
echo.
echo 🏗️ Creating database...
mysql -u %DB_USER% -p%DB_PASS% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ Database creation failed or database already exists
) else (
    echo ✅ Database created successfully
)

REM Step 6: Run migrations
echo.
echo 🔄 Running database migrations...
php artisan migrate --force
if %errorlevel% neq 0 (
    echo ❌ Database migrations failed
    pause
    exit /b 1
) else (
    echo ✅ Database migrations completed
)

REM Step 7: Install Spatie Permission
echo.
echo 🔐 Setting up permissions system...
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
if %errorlevel% neq 0 (
    echo ❌ Failed to configure permission system
    pause
    exit /b 1
) else (
    echo ✅ Permission system configured
)

REM Step 8: Seed database
echo.
echo 🌱 Seeding database with initial data...
php artisan db:seed
if %errorlevel% neq 0 (
    echo ❌ Database seeding failed
    pause
    exit /b 1
) else (
    echo ✅ Database seeded successfully
)

REM Step 9: Install frontend dependencies
echo.
echo 📦 Installing frontend dependencies...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    cd ..
    pause
    exit /b 1
) else (
    echo ✅ Frontend dependencies installed successfully
    cd ..
)

REM Step 10: Create storage links
echo.
echo 🔗 Creating storage links...
php artisan storage:link
if %errorlevel% neq 0 (
    echo ❌ Failed to create storage links
) else (
    echo ✅ Storage links created
)

REM Step 11: Clear caches
echo.
echo 🧹 Clearing caches...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
echo ✅ Caches cleared

REM Installation complete
echo.
echo 🎉 Installation completed successfully!
echo.
echo 📋 Next steps:
echo 1. Start the Laravel development server:
echo    php artisan serve
echo.
echo 2. Start the React development server:
echo    cd frontend ^&^& npm start
echo.
echo 3. Access the application:
echo    Backend API: http://localhost:8000
echo    Frontend: http://localhost:3000
echo.
echo 4. Default login credentials:
echo    Super Admin: <EMAIL> / password123
echo    Farm Manager: <EMAIL> / password123
echo    Farm Worker: <EMAIL> / password123
echo.
echo 🔧 Optional: Use Docker for development:
echo    docker-compose up -d
echo.
echo 📚 Documentation available in the Docs/ directory
echo.
echo Happy farming! 🐔🥚
echo.
pause
