<?php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

class Farm extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'farm_code',
        'farm_name',
        'farm_type',
        'owner_name',
        'manager_id',
        'address',
        'city',
        'province',
        'postal_code',
        'country',
        'latitude',
        'longitude',
        'total_area_hectares',
        'established_date',
        'license_number',
        'certification',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'established_date' => 'date',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'total_area_hectares' => 'decimal:2',
        'certification' => 'array',
        'deleted_at' => 'datetime',
    ];

    // Boot method to auto-generate UUID and farm code
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($farm) {
            if (empty($farm->uuid)) {
                $farm->uuid = Str::uuid();
            }
            if (empty($farm->farm_code)) {
                $farm->farm_code = $farm->generateFarmCode();
            }
        });
    }

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['farm_name', 'status', 'manager_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function manager()
    {
        return $this->belongsTo(\App\Models\User::class, 'manager_id');
    }

    public function houses()
    {
        return $this->hasMany(House::class);
    }

    public function flocks()
    {
        return $this->hasManyThrough(Flock::class, House::class);
    }

    public function users()
    {
        return $this->belongsToMany(\App\Models\User::class, 'user_farms');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('farm_type', $type);
    }

    public function scopeByManager($query, $managerId)
    {
        return $query->where('manager_id', $managerId);
    }

    // Accessors
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getTotalHousesAttribute(): int
    {
        return $this->houses()->count();
    }

    public function getTotalCapacityAttribute(): int
    {
        return $this->houses()->sum('capacity');
    }

    public function getCurrentPopulationAttribute(): int
    {
        return $this->flocks()->where('status', 'active')->sum('current_count');
    }

    public function getOccupancyRateAttribute(): float
    {
        return $this->total_capacity > 0 ? ($this->current_population / $this->total_capacity) * 100 : 0;
    }

    public function getFullAddressAttribute(): string
    {
        return trim("{$this->address}, {$this->city}, {$this->province} {$this->postal_code}");
    }

    // Methods
    public function addHouse(array $houseData): House
    {
        return $this->houses()->create(array_merge($houseData, [
            'uuid' => Str::uuid(),
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]));
    }

    public function calculateUtilization(): array
    {
        $houses = $this->houses()->with('currentFlock')->get();
        
        return [
            'total_houses' => $houses->count(),
            'occupied_houses' => $houses->where('currentFlock')->count(),
            'total_capacity' => $houses->sum('capacity'),
            'current_population' => $houses->sum('currentFlock.current_count'),
            'utilization_percentage' => $this->occupancy_rate,
        ];
    }

    public function generateFarmCode(): string
    {
        $year = now()->year;
        $prefix = "FARM{$year}";

        $lastFarm = static::where('farm_code', 'like', $prefix . '%')
            ->orderBy('farm_code', 'desc')
            ->first();

        if ($lastFarm) {
            $lastNumber = (int) substr($lastFarm->farm_code, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }

        return $prefix . $newNumber;
    }

    public static function generateFarmCodeStatic(): string
    {
        $year = now()->year;
        $prefix = "FARM{$year}";

        $lastFarm = static::where('farm_code', 'like', $prefix . '%')
            ->orderBy('farm_code', 'desc')
            ->first();

        if ($lastFarm) {
            $lastNumber = (int) substr($lastFarm->farm_code, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }

        return $prefix . $newNumber;
    }
}
