{"name": "erp-poultry/management-system", "type": "project", "description": "Comprehensive ERP system for poultry farming operations", "keywords": ["laravel", "erp", "poultry", "farming", "management"], "license": "MIT", "require": {"php": "^8.1", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "spatie/laravel-permission": "^5.10", "spatie/laravel-activitylog": "^4.7", "nwidart/laravel-modules": "^10.0", "maatwebsite/excel": "^3.1", "barryvdh/laravel-dompdf": "^2.0", "intervention/image": "^2.7", "pusher/pusher-php-server": "^7.2", "predis/predis": "^2.0", "laravel/horizon": "^5.15", "spatie/laravel-backup": "^8.1", "spatie/laravel-medialibrary": "^10.7", "league/flysystem-aws-s3-v3": "^3.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "laravel/telescope": "^4.14", "barryvdh/laravel-debugbar": "^3.8", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0", "squizlabs/php_codesniffer": "^3.7", "friendsofphp/php-cs-fixer": "^3.15", "phpstan/phpstan": "^1.10", "larastan/larastan": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "test": ["vendor/bin/phpunit"], "test-coverage": ["vendor/bin/phpunit --coverage-html coverage"], "pint": ["vendor/bin/pint"], "phpstan": ["vendor/bin/phpstan analyse"], "cs-fix": ["vendor/bin/php-cs-fixer fix"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}