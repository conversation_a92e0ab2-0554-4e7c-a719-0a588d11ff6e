<?php

namespace Modules\PoultryManagement\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FarmResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'farm_code' => $this->farm_code,
            'farm_name' => $this->farm_name,
            'farm_type' => $this->farm_type,
            'owner_name' => $this->owner_name,
            'manager' => $this->whenLoaded('manager', function () {
                return [
                    'id' => $this->manager->id,
                    'name' => $this->manager->name,
                    'email' => $this->manager->email,
                ];
            }),
            'address' => $this->address,
            'city' => $this->city,
            'province' => $this->province,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'full_address' => $this->full_address,
            'coordinates' => [
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ],
            'total_area_hectares' => $this->total_area_hectares,
            'established_date' => $this->established_date?->format('Y-m-d'),
            'license_number' => $this->license_number,
            'certification' => $this->certification,
            'status' => $this->status,
            'is_active' => $this->is_active,
            'statistics' => [
                'total_houses' => $this->total_houses,
                'total_capacity' => $this->total_capacity,
                'current_population' => $this->current_population,
                'occupancy_rate' => round($this->occupancy_rate, 2),
            ],
            'houses' => $this->whenLoaded('houses', function () {
                return $this->houses->map(function ($house) {
                    return [
                        'id' => $house->id,
                        'house_number' => $house->house_number,
                        'house_name' => $house->house_name,
                        'house_type' => $house->house_type,
                        'capacity' => $house->capacity,
                        'status' => $house->status,
                        'current_population' => $house->current_population ?? 0,
                        'occupancy_rate' => $house->occupancy_rate ?? 0,
                    ];
                });
            }),
            'flocks' => $this->whenLoaded('flocks', function () {
                return $this->flocks->map(function ($flock) {
                    return [
                        'id' => $flock->id,
                        'flock_number' => $flock->flock_number,
                        'breed_name' => $flock->breed->breed_name ?? null,
                        'current_count' => $flock->current_count,
                        'production_stage' => $flock->production_stage,
                        'status' => $flock->status,
                        'age_weeks' => $flock->age_weeks ?? 0,
                    ];
                });
            }),
            'notes' => $this->notes,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
