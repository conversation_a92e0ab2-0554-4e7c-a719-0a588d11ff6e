<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('houses', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->unsignedBigInteger('farm_id');
            $table->string('house_number');
            $table->string('house_name');
            $table->enum('house_type', ['layer', 'broiler', 'breeder', 'nursery', 'quarantine'])->default('layer');
            $table->integer('capacity');
            $table->decimal('length_meters', 8, 2)->nullable();
            $table->decimal('width_meters', 8, 2)->nullable();
            $table->decimal('height_meters', 8, 2)->nullable();
            $table->date('construction_date')->nullable();
            $table->date('last_renovation_date')->nullable();
            $table->enum('ventilation_type', ['natural', 'mechanical', 'hybrid'])->default('natural');
            $table->enum('lighting_type', ['natural', 'artificial', 'hybrid'])->default('natural');
            $table->enum('feeding_system', ['manual', 'automatic', 'semi_automatic'])->default('manual');
            $table->enum('watering_system', ['manual', 'nipple', 'bell', 'trough'])->default('nipple');
            $table->string('waste_management')->nullable();
            $table->json('climate_control')->nullable();
            $table->enum('status', ['active', 'inactive', 'maintenance', 'renovation'])->default('active');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->unique(['farm_id', 'house_number']);
            $table->index(['farm_id', 'status']);
            $table->index(['house_type', 'status']);
            $table->foreign('farm_id')->references('id')->on('farms')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('houses');
    }
};
