version: '3.8'

services:
  # Laravel Application
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
      target: development
    container_name: erp-poultry-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    ports:
      - "8000:8000"
    networks:
      - erp-network
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: erp-poultry-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: erp_poultry
      MYSQL_USER: erp_user
      MYSQL_PASSWORD: erp_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - erp-network

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    container_name: erp-poultry-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - erp-network
    command: redis-server --appendonly yes

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: docker/Dockerfile
      target: development
    container_name: erp-poultry-queue
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    networks:
      - erp-network
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - REDIS_HOST=redis
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600

  # Scheduler
  scheduler:
    build:
      context: .
      dockerfile: docker/Dockerfile
      target: development
    container_name: erp-poultry-scheduler
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    networks:
      - erp-network
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - REDIS_HOST=redis
    command: php artisan schedule:work

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: erp-poultry-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - erp-network
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - REACT_APP_WS_URL=ws://localhost:8000
      - CHOKIDAR_USEPOLLING=true

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: erp-poultry-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./storage/app/public:/var/www/html/storage/app/public
    networks:
      - erp-network
    depends_on:
      - app
      - frontend

  # MailHog for Email Testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: erp-poultry-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - erp-network

  # MinIO for S3-compatible Storage
  minio:
    image: minio/minio:latest
    container_name: erp-poultry-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - erp-network
    command: server /data --console-address ":9001"

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  erp-network:
    driver: bridge
