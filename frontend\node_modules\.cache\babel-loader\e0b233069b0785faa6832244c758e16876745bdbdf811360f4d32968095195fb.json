{"ast": null, "code": "// This icon file is generated automatically.\nvar DotNetOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"fill-rule\": \"evenodd\",\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"g\",\n      \"attrs\": {\n        \"fill-opacity\": \".88\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M101.28 662c-10.65 0-19.53-3.3-26.63-9.89-7.1-6.6-10.65-14.7-10.65-24.32 0-9.89 3.65-18 10.96-24.31 7.3-6.32 16.42-9.48 27.35-9.48 11.06 0 20.1 3.2 27.14 9.58 7.03 6.39 10.55 14.46 10.55 24.21 0 10.03-3.58 18.24-10.76 24.63-7.17 6.39-16.49 9.58-27.96 9.58M458 657h-66.97l-121.4-185.35c-7.13-10.84-12.06-19-14.8-24.48h-.82c1.1 10.42 1.65 26.33 1.65 47.72V657H193V362h71.49l116.89 179.6a423.23 423.23 0 0114.79 24.06h.82c-1.1-6.86-1.64-20.37-1.64-40.53V362H458zM702 657H525V362h170.2v54.1H591.49v65.63H688v53.9h-96.52v67.47H702zM960 416.1h-83.95V657h-66.5V416.1H726V362h234z\"\n        }\n      }]\n    }]\n  },\n  \"name\": \"dot-net\",\n  \"theme\": \"outlined\"\n};\nexport default DotNetOutlined;", "map": {"version": 3, "names": ["DotNetOutlined"], "sources": ["C:/laragon/www/neomuria2025_augment/frontend/node_modules/@ant-design/icons-svg/es/asn/DotNetOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar DotNetOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"g\", \"attrs\": { \"fill-opacity\": \".88\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M101.28 662c-10.65 0-19.53-3.3-26.63-9.89-7.1-6.6-10.65-14.7-10.65-24.32 0-9.89 3.65-18 10.96-24.31 7.3-6.32 16.42-9.48 27.35-9.48 11.06 0 20.1 3.2 27.14 9.58 7.03 6.39 10.55 14.46 10.55 24.21 0 10.03-3.58 18.24-10.76 24.63-7.17 6.39-16.49 9.58-27.96 9.58M458 657h-66.97l-121.4-185.35c-7.13-10.84-12.06-19-14.8-24.48h-.82c1.1 10.42 1.65 26.33 1.65 47.72V657H193V362h71.49l116.89 179.6a423.23 423.23 0 0114.79 24.06h.82c-1.1-6.86-1.64-20.37-1.64-40.53V362H458zM702 657H525V362h170.2v54.1H591.49v65.63H688v53.9h-96.52v67.47H702zM960 416.1h-83.95V657h-66.5V416.1H726V362h234z\" } }] }] }, \"name\": \"dot-net\", \"theme\": \"outlined\" };\nexport default DotNetOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,cAAc,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,WAAW,EAAE,SAAS;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,GAAG;MAAE,OAAO,EAAE;QAAE,cAAc,EAAE;MAAM,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE;QAA+jB;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,SAAS;EAAE,OAAO,EAAE;AAAW,CAAC;AACr2B,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}