/*
 Navicat Premium Data Transfer

 Source Server         : LOCALHOST
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42)
 Source Host           : localhost:3306
 Source Schema         : muriawebneo2024

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42)
 File Encoding         : 65001

 Date: 16/07/2025 19:41:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for akun
-- ----------------------------
DROP TABLE IF EXISTS `akun`;
CREATE TABLE `akun`  (
  `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT,
  `nama` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `kode` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `kelompok_akun_id` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `pajak` tinyint(1) NOT NULL DEFAULT 1,
  `saldo_awal` bigint(20) NOT NULL DEFAULT 0,
  `saldo` bigint(20) NOT NULL DEFAULT 0,
  `keterangan` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `kelompok_akun_id`(`kelompok_akun_id`) USING BTREE,
  CONSTRAINT `akun_ibfk_1` FOREIGN KEY (`kelompok_akun_id`) REFERENCES `kelompok_akun` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for armada
-- ----------------------------
DROP TABLE IF EXISTS `armada`;
CREATE TABLE `armada`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kendaraan_id` int(11) NULL DEFAULT NULL,
  `supir_id` int(11) NULL DEFAULT NULL,
  `supplier_id` int(11) NULL DEFAULT NULL,
  `customer_id` int(11) NULL DEFAULT NULL,
  `rute_id` int(11) NULL DEFAULT NULL,
  `wilayah_id` int(11) NULL DEFAULT NULL,
  `mitra_id` int(11) NULL DEFAULT NULL,
  `gudang_id` int(11) NULL DEFAULT NULL,
  `kandang_id` int(11) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assembly_pakan
-- ----------------------------
DROP TABLE IF EXISTS `assembly_pakan`;
CREATE TABLE `assembly_pakan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `id_formulasi` int(11) NULL DEFAULT NULL,
  `id_barang_jadi` int(11) NULL DEFAULT NULL,
  `id_satuan_jadi` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `total_jadi` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_hpp` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assembly_pakan-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `assembly_pakan-backup25122016`;
CREATE TABLE `assembly_pakan-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `id_formulasi` int(11) NULL DEFAULT NULL,
  `id_barang_jadi` int(11) NULL DEFAULT NULL,
  `id_satuan_jadi` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `total_jadi` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_hpp` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assembly_pakan_detail
-- ----------------------------
DROP TABLE IF EXISTS `assembly_pakan_detail`;
CREATE TABLE `assembly_pakan_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_assembly` int(11) NULL DEFAULT NULL,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `is_barangjadi` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `urutan` int(11) NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assembly_pakan_detail-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `assembly_pakan_detail-backup25122016`;
CREATE TABLE `assembly_pakan_detail-backup25122016`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_assembly` int(11) NULL DEFAULT NULL,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `is_barangjadi` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `urutan` int(11) NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bank
-- ----------------------------
DROP TABLE IF EXISTS `bank`;
CREATE TABLE `bank`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bukti_bank` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_trx` enum('D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'Debet: bank masuk, Kredit: bank keluar',
  `akun_bank` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_bank` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `tgl_bank` date NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total_bank` decimal(20, 2) NULL DEFAULT NULL,
  `total_giro` decimal(20, 2) NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bank_detail
-- ----------------------------
DROP TABLE IF EXISTS `bank_detail`;
CREATE TABLE `bank_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_trx_bank` int(11) NULL DEFAULT NULL,
  `bukti_bank` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_debet` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_kredit` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_lawan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_lawan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nominal_detail` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan_detail` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `status_detail` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bank_giro
-- ----------------------------
DROP TABLE IF EXISTS `bank_giro`;
CREATE TABLE `bank_giro`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_giro` int(11) NULL DEFAULT NULL,
  `id_trx_bank` int(11) NULL DEFAULT NULL,
  `bukti_bank` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_tt_giro` enum('T','G') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'T: Transfer, G: Giro',
  `no_tt_giro` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_tt_giro` date NULL DEFAULT NULL,
  `nominal` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for banks
-- ----------------------------
DROP TABLE IF EXISTS `banks`;
CREATE TABLE `banks`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Rekening` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 8 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang
-- ----------------------------
DROP TABLE IF EXISTS `barang`;
CREATE TABLE `barang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Cabang` varchar(4) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Barcode` varchar(13) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Nama` varchar(125) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_golongan` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kemasan` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `Isi2` double NOT NULL DEFAULT 0,
  `Isi3` double NOT NULL DEFAULT 0,
  `Max` double NOT NULL DEFAULT 0,
  `SatuanMax` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Min` double NOT NULL DEFAULT 0,
  `SatuanMin` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HP` double(18, 2) NOT NULL DEFAULT 0.00,
  `StKon` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `id_supplier` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier1` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 445 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang-asli-backup01012017
-- ----------------------------
DROP TABLE IF EXISTS `barang-asli-backup01012017`;
CREATE TABLE `barang-asli-backup01012017`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Cabang` varchar(4) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Barcode` varchar(13) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Nama` varchar(125) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Golongan` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGolongan` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Markup` double NOT NULL DEFAULT 0,
  `Merk` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Ukuran` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kualitas` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Motif` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Warna` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kemasan` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `Isi2` double NOT NULL DEFAULT 0,
  `Isi3` double NOT NULL DEFAULT 0,
  `Satuan1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan2` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan3` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HPL1` double NOT NULL DEFAULT 0,
  `HPL2` double NOT NULL DEFAULT 0,
  `HPL3` double NOT NULL DEFAULT 0,
  `KTBeli` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '-',
  `Beli1` double NOT NULL DEFAULT 0,
  `Beli2` double NOT NULL DEFAULT 0,
  `Beli3` double NOT NULL DEFAULT 0,
  `KTJual` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '-',
  `Jual1` double NOT NULL DEFAULT 0,
  `Jual2` double NOT NULL DEFAULT 0,
  `Jual3` double NOT NULL DEFAULT 0,
  `HB1` double NOT NULL DEFAULT 0,
  `HB2` double NOT NULL DEFAULT 0,
  `HB3` double NOT NULL DEFAULT 0,
  `HJ1R` double NOT NULL DEFAULT 0,
  `HJ2R` double NOT NULL DEFAULT 0,
  `HJ3R` double NOT NULL DEFAULT 0,
  `HJ1U` double NOT NULL DEFAULT 0,
  `HJ2U` double NOT NULL DEFAULT 0,
  `HJ3U` double NOT NULL DEFAULT 0,
  `HJ2P` double NOT NULL DEFAULT 0,
  `HJ3P` double NOT NULL DEFAULT 0,
  `HN1` double NOT NULL DEFAULT 0,
  `HN2` double NOT NULL DEFAULT 0,
  `HN3` double NOT NULL DEFAULT 0,
  `Max` double NOT NULL DEFAULT 0,
  `SatuanMax` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Min` double NOT NULL DEFAULT 0,
  `SatuanMin` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HP` double(18, 2) NOT NULL DEFAULT 0.00,
  `StKon` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `OldHN1` double NOT NULL DEFAULT 0,
  `OldHN2` double NOT NULL DEFAULT 0,
  `OldHN3` double NOT NULL DEFAULT 0,
  `OldHb1` double NOT NULL DEFAULT 0,
  `OldHB2` double NOT NULL DEFAULT 0,
  `OldHb3` double NOT NULL DEFAULT 0,
  `OldHJ1U` double NOT NULL DEFAULT 0,
  `OldHJ2U` double NOT NULL DEFAULT 0,
  `OldHJ3U` double NOT NULL DEFAULT 0,
  `OldHJ1R` double NOT NULL DEFAULT 0,
  `OldHJ2R` double NOT NULL DEFAULT 0,
  `OldHJ3R` double NOT NULL DEFAULT 0,
  `Supplier1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier1` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Supplier2` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier2` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Supplier3` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier3` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Supplier4` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier4` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 445 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang-backup30122016
-- ----------------------------
DROP TABLE IF EXISTS `barang-backup30122016`;
CREATE TABLE `barang-backup30122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Cabang` varchar(4) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Barcode` varchar(13) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Nama` varchar(125) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_golongan` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kemasan` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `Isi2` double NOT NULL DEFAULT 0,
  `Isi3` double NOT NULL DEFAULT 0,
  `StKon` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `id_supplier` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 427 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_asli_backup
-- ----------------------------
DROP TABLE IF EXISTS `barang_asli_backup`;
CREATE TABLE `barang_asli_backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Cabang` varchar(4) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Barcode` varchar(13) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Nama` varchar(125) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Golongan` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGolongan` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Markup` double NOT NULL DEFAULT 0,
  `Merk` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Ukuran` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kualitas` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Motif` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Warna` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kemasan` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `Isi2` double NOT NULL DEFAULT 0,
  `Isi3` double NOT NULL DEFAULT 0,
  `Satuan1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan2` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan3` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HPL1` double NOT NULL DEFAULT 0,
  `HPL2` double NOT NULL DEFAULT 0,
  `HPL3` double NOT NULL DEFAULT 0,
  `KTBeli` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '-',
  `Beli1` double NOT NULL DEFAULT 0,
  `Beli2` double NOT NULL DEFAULT 0,
  `Beli3` double NOT NULL DEFAULT 0,
  `KTJual` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '-',
  `Jual1` double NOT NULL DEFAULT 0,
  `Jual2` double NOT NULL DEFAULT 0,
  `Jual3` double NOT NULL DEFAULT 0,
  `HB1` double NOT NULL DEFAULT 0,
  `HB2` double NOT NULL DEFAULT 0,
  `HB3` double NOT NULL DEFAULT 0,
  `HJ1R` double NOT NULL DEFAULT 0,
  `HJ2R` double NOT NULL DEFAULT 0,
  `HJ3R` double NOT NULL DEFAULT 0,
  `HJ1U` double NOT NULL DEFAULT 0,
  `HJ2U` double NOT NULL DEFAULT 0,
  `HJ3U` double NOT NULL DEFAULT 0,
  `HJ2P` double NOT NULL DEFAULT 0,
  `HJ3P` double NOT NULL DEFAULT 0,
  `HN1` double NOT NULL DEFAULT 0,
  `HN2` double NOT NULL DEFAULT 0,
  `HN3` double NOT NULL DEFAULT 0,
  `Max` double NOT NULL DEFAULT 0,
  `SatuanMax` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Min` double NOT NULL DEFAULT 0,
  `SatuanMin` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HP` double(18, 2) NOT NULL DEFAULT 0.00,
  `StKon` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `OldHN1` double NOT NULL DEFAULT 0,
  `OldHN2` double NOT NULL DEFAULT 0,
  `OldHN3` double NOT NULL DEFAULT 0,
  `OldHb1` double NOT NULL DEFAULT 0,
  `OldHB2` double NOT NULL DEFAULT 0,
  `OldHb3` double NOT NULL DEFAULT 0,
  `OldHJ1U` double NOT NULL DEFAULT 0,
  `OldHJ2U` double NOT NULL DEFAULT 0,
  `OldHJ3U` double NOT NULL DEFAULT 0,
  `OldHJ1R` double NOT NULL DEFAULT 0,
  `OldHJ2R` double NOT NULL DEFAULT 0,
  `OldHJ3R` double NOT NULL DEFAULT 0,
  `Supplier1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier1` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Supplier2` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier2` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Supplier3` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier3` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Supplier4` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmSupplier4` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 394 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_golongan
-- ----------------------------
DROP TABLE IF EXISTS `barang_golongan`;
CREATE TABLE `barang_golongan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `Golongan` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGolongan` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 889 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_golongan-backup01012017
-- ----------------------------
DROP TABLE IF EXISTS `barang_golongan-backup01012017`;
CREATE TABLE `barang_golongan-backup01012017`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `Golongan` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 787 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for barang_harga
-- ----------------------------
DROP TABLE IF EXISTS `barang_harga`;
CREATE TABLE `barang_harga`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `kode_barang` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `HPL1` double NOT NULL DEFAULT 0,
  `HPL2` double NOT NULL DEFAULT 0,
  `HPL3` double NOT NULL DEFAULT 0,
  `hb1` double NOT NULL DEFAULT 0,
  `hb2` double NOT NULL DEFAULT 0,
  `hb3` double NOT NULL DEFAULT 0,
  `HJ1R` double NOT NULL DEFAULT 0,
  `HJ2R` double NOT NULL DEFAULT 0,
  `HJ3R` double NOT NULL DEFAULT 0,
  `HJ1U` double NOT NULL DEFAULT 0,
  `HJ2U` double NOT NULL DEFAULT 0,
  `HJ3U` double NOT NULL DEFAULT 0,
  `HJ2P` double NOT NULL DEFAULT 0,
  `HJ3P` double NOT NULL DEFAULT 0,
  `hn1` double NOT NULL DEFAULT 0,
  `hn2` double NOT NULL DEFAULT 0,
  `hn3` double NOT NULL DEFAULT 0,
  `max` double NOT NULL DEFAULT 0,
  `satuanmax` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `min` double NOT NULL DEFAULT 0,
  `satuanmin` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 889 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_harga4112016
-- ----------------------------
DROP TABLE IF EXISTS `barang_harga4112016`;
CREATE TABLE `barang_harga4112016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `markup` double NOT NULL DEFAULT 0,
  `hb1` double NOT NULL DEFAULT 0,
  `hb2` double NOT NULL DEFAULT 0,
  `hb3` double NOT NULL DEFAULT 0,
  `hj1r` double NOT NULL DEFAULT 0,
  `hj2r` double NOT NULL DEFAULT 0,
  `hj3r` double NOT NULL DEFAULT 0,
  `hj1u` double NOT NULL DEFAULT 0,
  `hj2u` double NOT NULL DEFAULT 0,
  `hj3u` double NOT NULL DEFAULT 0,
  `hj2p` double NOT NULL DEFAULT 0,
  `hj3p` double NOT NULL DEFAULT 0,
  `hn1` double NOT NULL DEFAULT 0,
  `hn2` double NOT NULL DEFAULT 0,
  `hn3` double NOT NULL DEFAULT 0,
  `max` double NOT NULL DEFAULT 0,
  `datetime` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 794 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for barang_harga-backup01012017
-- ----------------------------
DROP TABLE IF EXISTS `barang_harga-backup01012017`;
CREATE TABLE `barang_harga-backup01012017`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `markup` double NOT NULL DEFAULT 0,
  `hb1` double NOT NULL DEFAULT 0,
  `hb2` double NOT NULL DEFAULT 0,
  `hb3` double NOT NULL DEFAULT 0,
  `hj1r` double NOT NULL DEFAULT 0,
  `hj2r` double NOT NULL DEFAULT 0,
  `hj3r` double NOT NULL DEFAULT 0,
  `hj1u` double NOT NULL DEFAULT 0,
  `hj2u` double NOT NULL DEFAULT 0,
  `hj3u` double NOT NULL DEFAULT 0,
  `hj2p` double NOT NULL DEFAULT 0,
  `hj3p` double NOT NULL DEFAULT 0,
  `hn1` double NOT NULL DEFAULT 0,
  `hn2` double NOT NULL DEFAULT 0,
  `hn3` double NOT NULL DEFAULT 0,
  `max` double NOT NULL DEFAULT 0,
  `datetime` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 794 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for barang_harga_asli_backup
-- ----------------------------
DROP TABLE IF EXISTS `barang_harga_asli_backup`;
CREATE TABLE `barang_harga_asli_backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `Markup` double NOT NULL DEFAULT 0,
  `HPL1` double NOT NULL DEFAULT 0,
  `HPL2` double NOT NULL DEFAULT 0,
  `HPL3` double NOT NULL DEFAULT 0,
  `KTBeli` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '-',
  `Beli1` double NOT NULL DEFAULT 0,
  `Beli2` double NOT NULL DEFAULT 0,
  `Beli3` double NOT NULL DEFAULT 0,
  `KTJual` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '-',
  `Jual1` double NOT NULL DEFAULT 0,
  `Jual2` double NOT NULL DEFAULT 0,
  `Jual3` double NOT NULL DEFAULT 0,
  `HB1` double NOT NULL DEFAULT 0,
  `HB2` double NOT NULL DEFAULT 0,
  `HB3` double NOT NULL DEFAULT 0,
  `HJ1R` double NOT NULL DEFAULT 0,
  `HJ2R` double NOT NULL DEFAULT 0,
  `HJ3R` double NOT NULL DEFAULT 0,
  `HJ1U` double NOT NULL DEFAULT 0,
  `HJ2U` double NOT NULL DEFAULT 0,
  `HJ3U` double NOT NULL DEFAULT 0,
  `HJ2P` double NOT NULL DEFAULT 0,
  `HJ3P` double NOT NULL DEFAULT 0,
  `HN1` double NOT NULL DEFAULT 0,
  `HN2` double NOT NULL DEFAULT 0,
  `HN3` double NOT NULL DEFAULT 0,
  `Max` double NOT NULL DEFAULT 0,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 787 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for barang_harga_lama
-- ----------------------------
DROP TABLE IF EXISTS `barang_harga_lama`;
CREATE TABLE `barang_harga_lama`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `kode_barang` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `hpl1` double NOT NULL DEFAULT 0,
  `hpl2` double NOT NULL DEFAULT 0,
  `hpl3` double NOT NULL DEFAULT 0,
  `hb1` double NOT NULL DEFAULT 0,
  `hb2` double NOT NULL DEFAULT 0,
  `hb3` double NOT NULL DEFAULT 0,
  `hj1r` double NOT NULL DEFAULT 0,
  `hj2r` double NOT NULL DEFAULT 0,
  `hj3r` double NOT NULL DEFAULT 0,
  `hj1u` double NOT NULL DEFAULT 0,
  `hj2u` double NOT NULL DEFAULT 0,
  `hj3u` double NOT NULL DEFAULT 0,
  `hj2p` double NOT NULL DEFAULT 0,
  `hj3p` double NOT NULL DEFAULT 0,
  `hn1` double NOT NULL DEFAULT 0,
  `hn2` double NOT NULL DEFAULT 0,
  `hn3` double NOT NULL DEFAULT 0,
  `user` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 889 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_harga_lama-backup01012017
-- ----------------------------
DROP TABLE IF EXISTS `barang_harga_lama-backup01012017`;
CREATE TABLE `barang_harga_lama-backup01012017`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `kode_barang` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `hn1` double NOT NULL DEFAULT 0,
  `hn2` double NOT NULL DEFAULT 0,
  `hn3` double NOT NULL DEFAULT 0,
  `hb1` double NOT NULL DEFAULT 0,
  `hb2` double NOT NULL DEFAULT 0,
  `hb3` double NOT NULL DEFAULT 0,
  `hj1u` double NOT NULL DEFAULT 0,
  `hj2u` double NOT NULL DEFAULT 0,
  `hj3u` double NOT NULL DEFAULT 0,
  `hj1r` double NOT NULL DEFAULT 0,
  `hj2r` double NOT NULL DEFAULT 0,
  `hj3r` double NOT NULL DEFAULT 0,
  `user` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 787 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_satuan
-- ----------------------------
DROP TABLE IF EXISTS `barang_satuan`;
CREATE TABLE `barang_satuan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `kode` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Kemasan` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `Isi2` double NOT NULL DEFAULT 0,
  `Isi3` double NOT NULL DEFAULT 0,
  `Satuan1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan2` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan3` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Max` double NOT NULL DEFAULT 0,
  `SatuanMax` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Min` double NOT NULL DEFAULT 0,
  `SatuanMin` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HP` double(18, 2) NOT NULL DEFAULT 0.00,
  `StKon` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 889 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for barang_satuan-backup01012017
-- ----------------------------
DROP TABLE IF EXISTS `barang_satuan-backup01012017`;
CREATE TABLE `barang_satuan-backup01012017`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `kode` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Isi2` double NOT NULL DEFAULT 0,
  `Isi3` double NOT NULL DEFAULT 0,
  `Satuan1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan2` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Satuan3` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Max` double NOT NULL DEFAULT 0,
  `SatuanMax` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Min` double NOT NULL DEFAULT 0,
  `SatuanMin` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `StKon` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 801 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for beli
-- ----------------------------
DROP TABLE IF EXISTS `beli`;
CREATE TABLE `beli`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NOT NULL,
  `Kode` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `NmBarang` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Harga` double NULL DEFAULT 0,
  `Discount` double NULL DEFAULT 0,
  `Discount2` double NULL DEFAULT 0,
  `Promo` double NULL DEFAULT 0,
  `Jumlah` double NULL DEFAULT 0,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `Faktur`(`Faktur`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 30607 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for beli_baru
-- ----------------------------
DROP TABLE IF EXISTS `beli_baru`;
CREATE TABLE `beli_baru`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Kode` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmBarang` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Harga` double NOT NULL DEFAULT 0,
  `Discount` double NOT NULL DEFAULT 0,
  `Discount2` double NOT NULL DEFAULT 0,
  `Promo` double NOT NULL DEFAULT 0,
  `Jumlah` double NOT NULL DEFAULT 0,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `Faktur`(`Faktur`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bon
-- ----------------------------
DROP TABLE IF EXISTS `bon`;
CREATE TABLE `bon`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmKaryawan` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `FakturBiaya` varchar(14) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Jumlah` double NOT NULL DEFAULT 0,
  `Total` double NOT NULL DEFAULT 0,
  `Bayar` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `Kas` double NOT NULL DEFAULT 0,
  `Jenis` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'T',
  `Bank` double NOT NULL DEFAULT 0,
  `NamaBank` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAccBank` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Ref` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `JthTmp` date NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  `StKembali` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `TglKembali` date NULL DEFAULT NULL,
  `UserKembali` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cNoJrn` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lVoid` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `lPosted` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`Faktur`) USING BTREE,
  INDEX `NewIndex`(`Faktur`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bukti
-- ----------------------------
DROP TABLE IF EXISTS `bukti`;
CREATE TABLE `bukti`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_bukti` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `parent` int(11) NULL DEFAULT NULL,
  `module` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `load_url` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_active` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for campur
-- ----------------------------
DROP TABLE IF EXISTS `campur`;
CREATE TABLE `campur`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Gudang` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `StCampur` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `Harga` double NOT NULL DEFAULT 0,
  `Total` double NOT NULL DEFAULT 0,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `NmBarang` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  INDEX `NewIndex`(`Faktur`, `Kode`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ci_sessions
-- ----------------------------
DROP TABLE IF EXISTS `ci_sessions`;
CREATE TABLE `ci_sessions`  (
  `session_id` varchar(40) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `ip_address` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `user_agent` varchar(120) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `last_activity` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `user_data` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`session_id`) USING BTREE,
  INDEX `last_activity_idx`(`last_activity`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer
-- ----------------------------
DROP TABLE IF EXISTS `customer`;
CREATE TABLE `customer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(8) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Nama` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Wilayah` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Area` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Contact` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Telepon` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Fax` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `JthTempo` double NOT NULL DEFAULT 0,
  `Diskon` double NOT NULL DEFAULT 0,
  `Plafond` double NOT NULL DEFAULT 0,
  `Subsidi` double NOT NULL DEFAULT 0,
  `NPWP` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  `Hutang` double NULL DEFAULT 0,
  `Bayar` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `Bank` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `AnRekening` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Golongan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `LastPrint` date NULL DEFAULT NULL,
  `StAktif` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`, `Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 634 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer-backup30122016
-- ----------------------------
DROP TABLE IF EXISTS `customer-backup30122016`;
CREATE TABLE `customer-backup30122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(8) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Nama` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Wilayah` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Area` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Contact` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Telepon` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Fax` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `JthTempo` double NOT NULL DEFAULT 0,
  `Diskon` double NOT NULL DEFAULT 0,
  `Plafond` double NOT NULL DEFAULT 0,
  `Subsidi` double NOT NULL DEFAULT 0,
  `NPWP` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  `Hutang` double NULL DEFAULT 0,
  `Bayar` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `Bank` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `AnRekening` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Golongan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `LastPrint` date NULL DEFAULT NULL,
  `StAktif` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 573 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for delivery_order
-- ----------------------------
DROP TABLE IF EXISTS `delivery_order`;
CREATE TABLE `delivery_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_po` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tanggal_kirim` date NULL DEFAULT NULL,
  `kirim_via` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alamat_kirim` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alamat_tagihan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `biaya_id` int(11) NULL DEFAULT NULL,
  `biaya_kirim` decimal(20, 2) NULL DEFAULT NULL,
  `armada_id` int(11) NULL DEFAULT NULL,
  `is_approved` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for delivery_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `delivery_order_detail`;
CREATE TABLE `delivery_order_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'faktur po, retur, request internal',
  `faktur_po` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'faktur po customer eksternal',
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL COMMENT 'jumlah kirim',
  `jumlah_retur` int(11) NULL DEFAULT NULL COMMENT 'jumlah retur jika ada barang retur',
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for earn
-- ----------------------------
DROP TABLE IF EXISTS `earn`;
CREATE TABLE `earn`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Kode` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(40) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Ket` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Jumlah` double NULL DEFAULT 0,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `NewIndex`(`Faktur`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 277 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for f
-- ----------------------------
DROP TABLE IF EXISTS `f`;
CREATE TABLE `f`  (
  `id` tinyint(3) UNSIGNED NOT NULL,
  `nama` char(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for formulasi
-- ----------------------------
DROP TABLE IF EXISTS `formulasi`;
CREATE TABLE `formulasi`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `nama` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_layer` int(11) NULL DEFAULT NULL,
  `id_strain` int(11) NULL DEFAULT NULL,
  `id_grade` int(11) NULL DEFAULT NULL,
  `jml_hasil_prediksi` decimal(10, 2) NULL DEFAULT NULL,
  `jml_hasil_jadi` decimal(10, 2) NULL DEFAULT NULL,
  `satuan_jadi` int(11) NULL DEFAULT NULL,
  `umur` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for formulasi_detail
-- ----------------------------
DROP TABLE IF EXISTS `formulasi_detail`;
CREATE TABLE `formulasi_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_formulasi` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `prosentase` double NULL DEFAULT NULL,
  `jml_form_jadi` decimal(10, 2) NULL DEFAULT NULL,
  `jml_fakta_jadi` decimal(10, 2) NULL DEFAULT NULL,
  `satuan_jadi` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for golonganaktiva
-- ----------------------------
DROP TABLE IF EXISTS `golonganaktiva`;
CREATE TABLE `golonganaktiva`  (
  `Kode` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Kode`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for golonganbiaya
-- ----------------------------
DROP TABLE IF EXISTS `golonganbiaya`;
CREATE TABLE `golonganbiaya`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'P',
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 104 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for golongancustomer
-- ----------------------------
DROP TABLE IF EXISTS `golongancustomer`;
CREATE TABLE `golongancustomer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` char(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `CaraJual` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'U',
  `User` char(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` char(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for groups
-- ----------------------------
DROP TABLE IF EXISTS `groups`;
CREATE TABLE `groups`  (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for grup_akun
-- ----------------------------
DROP TABLE IF EXISTS `grup_akun`;
CREATE TABLE `grup_akun`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `parent` int(11) NULL DEFAULT NULL,
  `kode_akun` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `parent_akun` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gudang
-- ----------------------------
DROP TABLE IF EXISTS `gudang`;
CREATE TABLE `gudang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kd_gudang` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nama` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `kode_mitra` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `id_wilayah` int(11) NULL DEFAULT NULL,
  `status` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `datetime` datetime NULL DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for harga_barang
-- ----------------------------
DROP TABLE IF EXISTS `harga_barang`;
CREATE TABLE `harga_barang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `jenis_harga` enum('beli','jual') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'beli',
  `harga_lama` decimal(20, 2) NULL DEFAULT NULL,
  `harga_baru` decimal(20, 2) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `tgl_aktif` datetime NULL DEFAULT NULL,
  `tgl_nonaktif` datetime NULL DEFAULT NULL,
  `is_active` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `is_delete` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `is_syarat` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for harga_satuan
-- ----------------------------
DROP TABLE IF EXISTS `harga_satuan`;
CREATE TABLE `harga_satuan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_satu_barang` int(11) NULL DEFAULT NULL,
  `id_harga` int(11) NULL DEFAULT NULL,
  `isactive` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hpkaryawan
-- ----------------------------
DROP TABLE IF EXISTS `hpkaryawan`;
CREATE TABLE `hpkaryawan`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmKaryawan` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Jumlah` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `NoAcc` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Bank` double NOT NULL DEFAULT 0,
  `JenisBayar` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'T',
  `KodeBank` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAccBank` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoBukti` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Jthtmp` date NULL DEFAULT NULL,
  `Total` double NOT NULL DEFAULT 0,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cNoJrn` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lVoid` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `lPosted` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`Faktur`) USING BTREE,
  UNIQUE INDEX `Faktur`(`Faktur`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for invoice_po
-- ----------------------------
DROP TABLE IF EXISTS `invoice_po`;
CREATE TABLE `invoice_po`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_invoice` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_po` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `tgl_invoice` date NULL DEFAULT NULL,
  `pembayaran` enum('Hutang','Tunai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jatuh_tempo` date NULL DEFAULT NULL,
  `status` enum('Lunas','Belum Lunas') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for isilayer
-- ----------------------------
DROP TABLE IF EXISTS `isilayer`;
CREATE TABLE `isilayer`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Customer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmCustomer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kandang` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmKandang` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Layer` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmLayer` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Gudang` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGudang` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Keterangan` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Nmbarang` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `QtyReal` double NOT NULL DEFAULT 0,
  `Umur` int(11) NULL DEFAULT 0,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `lPosted` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `lVoid` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  PRIMARY KEY (`Faktur`) USING BTREE,
  UNIQUE INDEX `Faktur`(`Faktur`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jabatan
-- ----------------------------
DROP TABLE IF EXISTS `jabatan`;
CREATE TABLE `jabatan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 5 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jenis_barang
-- ----------------------------
DROP TABLE IF EXISTS `jenis_barang`;
CREATE TABLE `jenis_barang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `parent` int(11) NULL DEFAULT NULL,
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 10 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jenis_barang_lama
-- ----------------------------
DROP TABLE IF EXISTS `jenis_barang_lama`;
CREATE TABLE `jenis_barang_lama`  (
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Kode`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jenis_pembayaran
-- ----------------------------
DROP TABLE IF EXISTS `jenis_pembayaran`;
CREATE TABLE `jenis_pembayaran`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `jenis_pembayaran` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jenis_pembelian
-- ----------------------------
DROP TABLE IF EXISTS `jenis_pembelian`;
CREATE TABLE `jenis_pembelian`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `jenis_beli` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jenis_recording
-- ----------------------------
DROP TABLE IF EXISTS `jenis_recording`;
CREATE TABLE `jenis_recording`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_rekening` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jenis_transaksi
-- ----------------------------
DROP TABLE IF EXISTS `jenis_transaksi`;
CREATE TABLE `jenis_transaksi`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `jenis_transaksi` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jurnal
-- ----------------------------
DROP TABLE IF EXISTS `jurnal`;
CREATE TABLE `jurnal`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `no_jurnal` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `no_bukti` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jurnal_group` int(11) NULL DEFAULT NULL,
  `tgl` date NULL DEFAULT NULL,
  `ket` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_jurnal` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total_debet` decimal(20, 2) NULL DEFAULT NULL,
  `total_kredit` decimal(20, 2) NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `from_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_posted` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_posted` datetime NULL DEFAULT NULL,
  `is_cancel` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_cancel` datetime NULL DEFAULT NULL,
  `alasan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jurnal-backup
-- ----------------------------
DROP TABLE IF EXISTS `jurnal-backup`;
CREATE TABLE `jurnal-backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `no_jurnal` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `no_bukti` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jurnal_group` int(11) NULL DEFAULT NULL,
  `tgl` date NULL DEFAULT NULL,
  `ket` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_jurnal` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total_debet` decimal(20, 2) NULL DEFAULT NULL,
  `total_kredit` decimal(20, 2) NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `from_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_posted` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_posted` datetime NULL DEFAULT NULL,
  `is_cancel` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_cancel` datetime NULL DEFAULT NULL,
  `alasan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jurnal_detail
-- ----------------------------
DROP TABLE IF EXISTS `jurnal_detail`;
CREATE TABLE `jurnal_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_jurnal` int(11) NULL DEFAULT NULL,
  `no_jurnal` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_detail` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_detail` enum('D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ket_detail` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nilai` decimal(20, 2) NULL DEFAULT NULL,
  `no_urut` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jurnal_detail-backup
-- ----------------------------
DROP TABLE IF EXISTS `jurnal_detail-backup`;
CREATE TABLE `jurnal_detail-backup`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_jurnal` int(11) NULL DEFAULT NULL,
  `no_jurnal` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_detail` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_detail` enum('D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ket_detail` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nilai` decimal(20, 2) NULL DEFAULT NULL,
  `no_urut` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jurnalx_detail
-- ----------------------------
DROP TABLE IF EXISTS `jurnalx_detail`;
CREATE TABLE `jurnalx_detail`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `jurnal_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `item` tinyint(4) NOT NULL DEFAULT 0,
  `akun_id` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
  `debit_kredit` tinyint(1) NOT NULL DEFAULT 1,
  `nilai` bigint(20) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `jurnal_id`(`jurnal_id`) USING BTREE,
  INDEX `akun_id`(`akun_id`) USING BTREE,
  CONSTRAINT `jurnalx_detail_ibfk_1` FOREIGN KEY (`jurnal_id`) REFERENCES `jurnalx` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `jurnalx_detail_ibfk_2` FOREIGN KEY (`akun_id`) REFERENCES `akun` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kandang
-- ----------------------------
DROP TABLE IF EXISTS `kandang`;
CREATE TABLE `kandang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Gudang` varchar(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGudang` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Mitra` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmMitra` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `StKandang` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `Faktur` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Barang` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Umur` double NOT NULL DEFAULT 0,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Kode`, `id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kandang-backup
-- ----------------------------
DROP TABLE IF EXISTS `kandang-backup`;
CREATE TABLE `kandang-backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Gudang` varchar(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGudang` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Mitra` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmMitra` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `StKandang` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `Faktur` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Barang` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Umur` double NOT NULL DEFAULT 0,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 21 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kandang-backup30122016
-- ----------------------------
DROP TABLE IF EXISTS `kandang-backup30122016`;
CREATE TABLE `kandang-backup30122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Gudang` varchar(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGudang` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Mitra` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmMitra` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `StKandang` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `Faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_kosong` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Barang` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `qty_real` int(11) NULL DEFAULT NULL,
  `qty_kosong` int(11) NULL DEFAULT NULL,
  `qty_afkir` int(11) NULL DEFAULT NULL,
  `qty_tambah` int(11) NULL DEFAULT NULL,
  `qty_mati` int(11) NULL DEFAULT NULL,
  `total_butir` int(11) NULL DEFAULT NULL,
  `total_kg` decimal(10, 2) NULL DEFAULT NULL,
  `total_pakan` decimal(10, 2) NULL DEFAULT NULL,
  `Satuan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Umur` double NOT NULL DEFAULT 0,
  `umur_real` int(11) NULL DEFAULT NULL,
  `umur_kosong` int(11) NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 21 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kandang-backupx
-- ----------------------------
DROP TABLE IF EXISTS `kandang-backupx`;
CREATE TABLE `kandang-backupx`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Gudang` varchar(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmGudang` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Mitra` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmMitra` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `StKandang` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `Faktur` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Barang` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Umur` double NOT NULL DEFAULT 0,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 21 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kandang_baru
-- ----------------------------
DROP TABLE IF EXISTS `kandang_baru`;
CREATE TABLE `kandang_baru`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nama` varchar(150) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `kapasitas_min` decimal(10, 0) NULL DEFAULT NULL,
  `kapasitas_max` decimal(10, 0) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartuhutang
-- ----------------------------
DROP TABLE IF EXISTS `kartuhutang`;
CREATE TABLE `kartuhutang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tgltempo` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_hutang` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(20, 2) NULL DEFAULT NULL,
  `id_customer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `mutasidebet` decimal(20, 2) NULL DEFAULT NULL,
  `mutasikredit` decimal(20, 2) NULL DEFAULT NULL,
  `saldodebet` decimal(20, 2) NULL DEFAULT NULL,
  `saldokredit` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartuhutang-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `kartuhutang-backup25122016`;
CREATE TABLE `kartuhutang-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tgltempo` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_hutang` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(20, 2) NULL DEFAULT NULL,
  `id_customer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `mutasidebet` decimal(20, 2) NULL DEFAULT NULL,
  `mutasikredit` decimal(20, 2) NULL DEFAULT NULL,
  `saldodebet` decimal(20, 2) NULL DEFAULT NULL,
  `saldokredit` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartupiutang
-- ----------------------------
DROP TABLE IF EXISTS `kartupiutang`;
CREATE TABLE `kartupiutang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tgltempo` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_hutang` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(20, 2) NULL DEFAULT NULL,
  `id_customer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `mutasidebet` decimal(20, 2) NULL DEFAULT NULL,
  `mutasikredit` decimal(20, 2) NULL DEFAULT NULL,
  `saldodebet` decimal(20, 2) NULL DEFAULT NULL,
  `saldokredit` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartupiutang-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `kartupiutang-backup25122016`;
CREATE TABLE `kartupiutang-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tgltempo` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_hutang` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(20, 2) NULL DEFAULT NULL,
  `id_customer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `mutasidebet` decimal(20, 2) NULL DEFAULT NULL,
  `mutasikredit` decimal(20, 2) NULL DEFAULT NULL,
  `saldodebet` decimal(20, 2) NULL DEFAULT NULL,
  `saldokredit` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartustock
-- ----------------------------
DROP TABLE IF EXISTS `kartustock`;
CREATE TABLE `kartustock`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Gudang` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmBarang` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `QTY` double NOT NULL DEFAULT 0,
  `Satuan` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Debet` double NOT NULL DEFAULT 0,
  `Kredit` double NOT NULL DEFAULT 0,
  `Harga` double NOT NULL DEFAULT 0,
  `DiscFaktur` double NOT NULL DEFAULT 0,
  `DiscFaktur2` double NOT NULL DEFAULT 0,
  `DiscFaktur3` double NOT NULL DEFAULT 0,
  `Disc1` double NOT NULL DEFAULT 0,
  `Disc2` double NOT NULL DEFAULT 0,
  `HP` double NOT NULL DEFAULT 0,
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `Status`(`Faktur`) USING BTREE,
  INDEX `Gudang`(`Gudang`, `Kode`, `Tgl`) USING BTREE,
  INDEX `Kode`(`Kode`, `Tgl`) USING BTREE,
  INDEX `Posting`(`Gudang`, `Kode`) USING BTREE,
  INDEX `StatusKode`(`Kode`, `Tgl`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartustok
-- ----------------------------
DROP TABLE IF EXISTS `kartustok`;
CREATE TABLE `kartustok`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_kartustok` enum('Telur','Ayam','Pakan','Obat','Vaksin','Disinvektan','Onderdil','Sapronak','Oli','Barang') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe` enum('S','D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `debet` decimal(20, 2) NULL DEFAULT NULL,
  `kredit` decimal(20, 2) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `hpp` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartustok-backup
-- ----------------------------
DROP TABLE IF EXISTS `kartustok-backup`;
CREATE TABLE `kartustok-backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_kartustok` enum('Telur','Ayam','Pakan','Obat','Vaksin','Disinvektan','Onderdil','Sapronak','Oli','Barang') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe` enum('S','D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `debet` decimal(10, 2) NULL DEFAULT NULL,
  `kredit` decimal(10, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartustok-backup20092016
-- ----------------------------
DROP TABLE IF EXISTS `kartustok-backup20092016`;
CREATE TABLE `kartustok-backup20092016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_kartustok` enum('Telur','Ayam','Pakan','Obat','Vaksin','Disinvektan','Onderdil','Sapronak','Oli','Barang') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe` enum('S','D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `debet` decimal(10, 2) NULL DEFAULT NULL,
  `kredit` decimal(10, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kartustok-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `kartustok-backup25122016`;
CREATE TABLE `kartustok-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_kartustok` enum('Telur','Ayam','Pakan','Obat','Vaksin','Disinvektan','Onderdil','Sapronak','Oli','Barang') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe` enum('S','D','K') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `debet` decimal(20, 2) NULL DEFAULT NULL,
  `kredit` decimal(20, 2) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `hpp` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for karyawan
-- ----------------------------
DROP TABLE IF EXISTS `karyawan`;
CREATE TABLE `karyawan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Nama` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `TempatLahir` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `TglLahir` date NULL DEFAULT NULL,
  `JK` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'L',
  `Telepon` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Jabatan` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmJabatan` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `TglMasuk` date NULL DEFAULT NULL,
  `Gapok` double NOT NULL DEFAULT 0,
  `Lembur` double NOT NULL DEFAULT 0,
  `TunjanganKeluarga` double NOT NULL DEFAULT 0,
  `TunjanganJabatan` double NOT NULL DEFAULT 0,
  `Transport` double NOT NULL DEFAULT 0,
  `Makan` double NOT NULL DEFAULT 0,
  `Lain` double NOT NULL DEFAULT 0,
  `Bonus` double NOT NULL DEFAULT 0,
  `Hutang` double NOT NULL DEFAULT 0,
  `NoAcc` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 140 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kas_keluar
-- ----------------------------
DROP TABLE IF EXISTS `kas_keluar`;
CREATE TABLE `kas_keluar`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_kas` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_kas` date NULL DEFAULT NULL,
  `akun_kas` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nominal` decimal(20, 2) NULL DEFAULT NULL,
  `is_jurnal` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kas_keluar_detail
-- ----------------------------
DROP TABLE IF EXISTS `kas_keluar_detail`;
CREATE TABLE `kas_keluar_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_kas_keluar` int(11) NULL DEFAULT NULL,
  `faktur_kas` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_lawan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `no_perkiraan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nominal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `status_kas` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kas_masuk
-- ----------------------------
DROP TABLE IF EXISTS `kas_masuk`;
CREATE TABLE `kas_masuk`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_kas` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tgl_kas` date NULL DEFAULT NULL,
  `akun_kas` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nominal` decimal(20, 2) NULL DEFAULT NULL,
  `is_jurnal` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kas_masuk_detail
-- ----------------------------
DROP TABLE IF EXISTS `kas_masuk_detail`;
CREATE TABLE `kas_masuk_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_kas_masuk` int(11) NULL DEFAULT NULL,
  `faktur_kas` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_lawan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `no_perkiraan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nominal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `status_kas` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kelompok_akun
-- ----------------------------
DROP TABLE IF EXISTS `kelompok_akun`;
CREATE TABLE `kelompok_akun`  (
  `id` tinyint(3) UNSIGNED NOT NULL,
  `nama` char(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kendaraan
-- ----------------------------
DROP TABLE IF EXISTS `kendaraan`;
CREATE TABLE `kendaraan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nopol` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nama` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_biaya` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_aktiva` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_penyusutan` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `daya_angkut` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_wilayah` int(11) NULL DEFAULT NULL,
  `jenis` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `kir_awal` date NULL DEFAULT NULL,
  `kir_akhir` date NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for klien
-- ----------------------------
DROP TABLE IF EXISTS `klien`;
CREATE TABLE `klien`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `nama` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `npwp` char(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `alamat` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `telpon_1` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `telpon_2` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `email` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `fax` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `website` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `keterangan` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for laporan_performance
-- ----------------------------
DROP TABLE IF EXISTS `laporan_performance`;
CREATE TABLE `laporan_performance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mitra` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `kandang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `gudang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tglawal` date NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `usia` int(11) NULL DEFAULT NULL,
  `ayam_mati` int(11) NULL DEFAULT NULL,
  `ayam_afkir` int(11) NULL DEFAULT NULL,
  `ayam_tambah` int(11) NULL DEFAULT NULL,
  `ayam_hidup` int(11) NULL DEFAULT NULL,
  `pakan` decimal(6, 2) NULL DEFAULT NULL,
  `pakan_gr_ekr_hr` decimal(4, 2) NULL DEFAULT NULL,
  `telur_butir` int(11) NULL DEFAULT NULL,
  `telur_kg` decimal(4, 2) NULL DEFAULT NULL,
  `telur_grambutir` decimal(4, 2) NULL DEFAULT NULL,
  `hd` decimal(4, 2) NULL DEFAULT NULL,
  `eggmass` decimal(4, 2) NULL DEFAULT NULL,
  `fcr` decimal(4, 2) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 168 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for log_activity
-- ----------------------------
DROP TABLE IF EXISTS `log_activity`;
CREATE TABLE `log_activity`  (
  `id` int(11) NOT NULL,
  `activity` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for logger
-- ----------------------------
DROP TABLE IF EXISTS `logger`;
CREATE TABLE `logger`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ipaddress` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `agent` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `module_id` int(11) NULL DEFAULT NULL,
  `start` datetime NULL DEFAULT NULL,
  `end` datetime NULL DEFAULT NULL,
  `activity` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `userdata` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for menu
-- ----------------------------
DROP TABLE IF EXISTS `menu`;
CREATE TABLE `menu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `parent` int(11) NULL DEFAULT NULL,
  `module` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `data-remote` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `data-target` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `levelid` int(11) NULL DEFAULT NULL,
  `groupid` int(11) NULL DEFAULT NULL,
  `orders` tinyint(4) NULL DEFAULT NULL,
  `is_ajax_url` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `is_active` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `is_disabled` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for modules
-- ----------------------------
DROP TABLE IF EXISTS `modules`;
CREATE TABLE `modules`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `module` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alias` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `path` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user_group` int(11) NULL DEFAULT NULL,
  `id_level` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mutasi_barang
-- ----------------------------
DROP TABLE IF EXISTS `mutasi_barang`;
CREATE TABLE `mutasi_barang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tipe` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_gd_asal` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_gd_tujuan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_kdg_asal` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_kdg_tujuan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mutasi_barang_detail
-- ----------------------------
DROP TABLE IF EXISTS `mutasi_barang_detail`;
CREATE TABLE `mutasi_barang_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for orderbebas
-- ----------------------------
DROP TABLE IF EXISTS `orderbebas`;
CREATE TABLE `orderbebas`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Kepada` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Sopir` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Mobil` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Barang` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `cNoJrn` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lVoid` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `lPosted` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`Faktur`) USING BTREE,
  UNIQUE INDEX `Faktur`(`Faktur`) USING BTREE,
  INDEX `Faktur_2`(`Faktur`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pakan
-- ----------------------------
DROP TABLE IF EXISTS `pakan`;
CREATE TABLE `pakan`  (
  `Faktur` char(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Gudang` char(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kode` char(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmBarang` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Satuan` char(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `StPakan` varchar(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `Harga` double NOT NULL DEFAULT 0,
  `Total` double NOT NULL DEFAULT 0,
  `User` char(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` char(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  INDEX `NewIndex`(`Faktur`, `Kode`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pelunasan_hutang
-- ----------------------------
DROP TABLE IF EXISTS `pelunasan_hutang`;
CREATE TABLE `pelunasan_hutang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tempohari` int(11) NULL DEFAULT NULL,
  `akun_hutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jthtempo` date NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `plafon` decimal(20, 2) NULL DEFAULT NULL,
  `hutang` decimal(20, 2) NULL DEFAULT NULL,
  `bayar` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pelunasan_hutang-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `pelunasan_hutang-backup25122016`;
CREATE TABLE `pelunasan_hutang-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tempohari` int(11) NULL DEFAULT NULL,
  `akun_hutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jthtempo` date NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `plafon` decimal(20, 2) NULL DEFAULT NULL,
  `hutang` decimal(20, 2) NULL DEFAULT NULL,
  `bayar` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pelunasan_piutang
-- ----------------------------
DROP TABLE IF EXISTS `pelunasan_piutang`;
CREATE TABLE `pelunasan_piutang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tempohari` int(11) NULL DEFAULT NULL,
  `jthtempo` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `plafon` decimal(20, 2) NULL DEFAULT NULL,
  `piutang` decimal(20, 2) NULL DEFAULT NULL,
  `bayar` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pelunasan_piutang-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `pelunasan_piutang-backup25122016`;
CREATE TABLE `pelunasan_piutang-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tempohari` int(11) NULL DEFAULT NULL,
  `jthtempo` date NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `plafon` decimal(20, 2) NULL DEFAULT NULL,
  `piutang` decimal(20, 2) NULL DEFAULT NULL,
  `bayar` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for penerimaan_detail
-- ----------------------------
DROP TABLE IF EXISTS `penerimaan_detail`;
CREATE TABLE `penerimaan_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_penerimaan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_pt` int(11) NULL DEFAULT NULL,
  `faktur_pt` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_po` int(11) NULL DEFAULT NULL,
  `faktur_po` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_beli` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_terima` decimal(10, 2) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `is_sesuai` enum('0','1') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_deleted` tinyint(4) NULL DEFAULT NULL,
  `is_edited` tinyint(4) NULL DEFAULT NULL,
  `is_actived` tinyint(4) NULL DEFAULT NULL,
  `del_date` datetime NULL DEFAULT NULL,
  `edit_date` datetime NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for penjualan
-- ----------------------------
DROP TABLE IF EXISTS `penjualan`;
CREATE TABLE `penjualan`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Tgl` date NULL DEFAULT NULL,
  `Kode` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Barcode` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Qty` double NOT NULL DEFAULT 0,
  `Harga` double NOT NULL DEFAULT 0,
  `Satuan` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Discount` double NOT NULL DEFAULT 0,
  `Discount2` double NOT NULL DEFAULT 0,
  `Jumlah` double NOT NULL DEFAULT 0,
  `HP` double NOT NULL DEFAULT 0,
  `Keterangan` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmBarang` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Promo` double NOT NULL DEFAULT 0,
  `QtyRetur` decimal(10, 2) NULL DEFAULT 0.00,
  `SatRetur` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `HN1` double NOT NULL DEFAULT 0,
  `HN2` double NULL DEFAULT 0,
  `HN3` double NULL DEFAULT 0,
  INDEX `NewIndex`(`Faktur`, `Kode`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for penyesuaian
-- ----------------------------
DROP TABLE IF EXISTS `penyesuaian`;
CREATE TABLE `penyesuaian`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total_nilai` decimal(20, 2) NULL DEFAULT NULL,
  `is_saldo` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `is_stockopname` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_rusak` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for penyesuaian_detail
-- ----------------------------
DROP TABLE IF EXISTS `penyesuaian_detail`;
CREATE TABLE `penyesuaian_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `jumlah_baru` int(11) NULL DEFAULT NULL,
  `is_saldo` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `is_stockopname` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_rusak` varchar(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `ket_detail` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for proyek
-- ----------------------------
DROP TABLE IF EXISTS `proyek`;
CREATE TABLE `proyek`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `nama` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `klien_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `spk_no` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `spk_tgl` date NULL DEFAULT NULL,
  `biaya` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `tgl_mulai` date NULL DEFAULT NULL,
  `tgl_selesai` date NULL DEFAULT NULL,
  `status` enum('Start','Pending','Cancel','Close') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'Start',
  `keterangan` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_order
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order`;
CREATE TABLE `purchase_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_po` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl_po` date NULL DEFAULT NULL,
  `termin_hari` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref_beli` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `biaya_kirim` decimal(20, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `id_bayar` int(11) NULL DEFAULT NULL,
  `id_armada` int(11) NULL DEFAULT NULL,
  `tgl_kirim_po` date NULL DEFAULT NULL,
  `tgl_jatuhtempo` date NULL DEFAULT NULL,
  `supir` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nopol` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_kendaraan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `islocked` tinyint(4) NULL DEFAULT NULL,
  `isactive` tinyint(4) NULL DEFAULT NULL,
  `isvalidpo` tinyint(4) NULL DEFAULT 0,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`, `faktur_po`) USING BTREE,
  INDEX `faktur_po`(`faktur_po`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_order-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order-backup25122016`;
CREATE TABLE `purchase_order-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_po` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl_po` date NULL DEFAULT NULL,
  `termin_hari` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref_beli` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_stock_req` int(11) NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `biaya_kirim` decimal(20, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `id_armada` int(11) NULL DEFAULT NULL,
  `tgl_kirim_po` date NULL DEFAULT NULL,
  `tgl_jatuhtempo` date NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `supir` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `nopol` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tipe_kendaraan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `faktur_po`(`faktur_po`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_detail`;
CREATE TABLE `purchase_order_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `po` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_akhir` decimal(10, 2) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_beli` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `isdeleted` tinyint(4) NULL DEFAULT 0,
  `isactive` tinyint(4) NULL DEFAULT 1,
  `id_user` int(11) NULL DEFAULT NULL,
  `datedelete` datetime NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE,
  INDEX `fkpo`(`po`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_order_detail-backup10012017
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_detail-backup10012017`;
CREATE TABLE `purchase_order_detail-backup10012017`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `po` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_akhir` decimal(10, 2) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_beli` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE,
  INDEX `fkpo`(`po`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_order_detail-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `purchase_order_detail-backup25122016`;
CREATE TABLE `purchase_order_detail-backup25122016`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `po` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_akhir` decimal(10, 2) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_beli` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE,
  INDEX `fkpo`(`po`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_request
-- ----------------------------
DROP TABLE IF EXISTS `purchase_request`;
CREATE TABLE `purchase_request`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tanggal_tempo` date NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `catatan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `is_approved` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `purchase_request_detail`;
CREATE TABLE `purchase_request_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_dipesan` decimal(10, 2) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `diskon1` decimal(10, 2) NULL DEFAULT NULL,
  `diskon2` decimal(10, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `pajak` decimal(10, 2) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_return
-- ----------------------------
DROP TABLE IF EXISTS `purchase_return`;
CREATE TABLE `purchase_return`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_pr` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_pt` int(11) NULL DEFAULT NULL,
  `tgl_pr` date NULL DEFAULT NULL,
  `tipe_retur` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `akun_hutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `totalretur` decimal(20, 2) NULL DEFAULT NULL,
  `bayar` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_return_detail
-- ----------------------------
DROP TABLE IF EXISTS `purchase_return_detail`;
CREATE TABLE `purchase_return_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_pr` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_pr` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_transaction
-- ----------------------------
DROP TABLE IF EXISTS `purchase_transaction`;
CREATE TABLE `purchase_transaction`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_pt` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl_pt` date NULL DEFAULT NULL,
  `id_tipe_beli` int(11) NULL DEFAULT NULL,
  `id_po` int(11) NULL DEFAULT NULL,
  `tgl_jatuh_tempo` date NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `akun_hutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref_beli` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_stock_req` int(11) NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `sisabayar` decimal(20, 2) NULL DEFAULT NULL,
  `diskon` decimal(4, 2) NULL DEFAULT NULL,
  `ppn` decimal(4, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `islocked` tinyint(4) NULL DEFAULT NULL,
  `isactive` tinyint(4) NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `isvalidtrx` tinyint(4) NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_transaction-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `purchase_transaction-backup25122016`;
CREATE TABLE `purchase_transaction-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_pt` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl_pt` date NULL DEFAULT NULL,
  `id_tipe_beli` int(11) NULL DEFAULT NULL,
  `id_po` int(11) NULL DEFAULT NULL,
  `tgl_jatuh_tempo` date NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `akun_hutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref_beli` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_stock_req` int(11) NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `sisabayar` decimal(20, 2) NULL DEFAULT NULL,
  `diskon` decimal(4, 2) NULL DEFAULT NULL,
  `ppn` decimal(4, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_transaction_detail
-- ----------------------------
DROP TABLE IF EXISTS `purchase_transaction_detail`;
CREATE TABLE `purchase_transaction_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `pt` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_pt` int(11) NULL DEFAULT NULL,
  `po` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_po` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_beli` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datedelete` datetime NULL DEFAULT NULL,
  `isdelete` tinyint(4) NULL DEFAULT NULL,
  `isactive` tinyint(4) NULL DEFAULT 1,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_transaction_detail-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `purchase_transaction_detail-backup25122016`;
CREATE TABLE `purchase_transaction_detail-backup25122016`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `pt` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_pt` int(11) NULL DEFAULT NULL,
  `po` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_po` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_beli` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for receive_item
-- ----------------------------
DROP TABLE IF EXISTS `receive_item`;
CREATE TABLE `receive_item`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_do` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `tanggal_terima` date NULL DEFAULT NULL,
  `kirim_via` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alamat_terima` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_cabang` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `nopol_pengirim` int(11) NULL DEFAULT NULL,
  `nama_pengirim` decimal(20, 2) NULL DEFAULT NULL,
  `is_approved` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for receive_item_detail
-- ----------------------------
DROP TABLE IF EXISTS `receive_item_detail`;
CREATE TABLE `receive_item_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'faktur po, retur, request internal',
  `faktur_do` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'faktur do supplier/vendor eksternal',
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL COMMENT 'jumlah kirim',
  `jumlah_retur` int(11) NULL DEFAULT NULL COMMENT 'jumlah retur jika ada barang retur',
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_ayam
-- ----------------------------
DROP TABLE IF EXISTS `recording_ayam`;
CREATE TABLE `recording_ayam`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NOT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_ayam-backup
-- ----------------------------
DROP TABLE IF EXISTS `recording_ayam-backup`;
CREATE TABLE `recording_ayam-backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NOT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`, `jumlah`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_ayam_detail
-- ----------------------------
DROP TABLE IF EXISTS `recording_ayam_detail`;
CREATE TABLE `recording_ayam_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_ayam` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `usia` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE,
  INDEX `faktur_recording`(`faktur_recording`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_ayam_detail-backup
-- ----------------------------
DROP TABLE IF EXISTS `recording_ayam_detail-backup`;
CREATE TABLE `recording_ayam_detail-backup`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_ayam` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `usia` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_medis
-- ----------------------------
DROP TABLE IF EXISTS `recording_medis`;
CREATE TABLE `recording_medis`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `umur` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_medis_detail
-- ----------------------------
DROP TABLE IF EXISTS `recording_medis_detail`;
CREATE TABLE `recording_medis_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_medis` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `umur` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_pakan
-- ----------------------------
DROP TABLE IF EXISTS `recording_pakan`;
CREATE TABLE `recording_pakan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `reff` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_pakan-backup
-- ----------------------------
DROP TABLE IF EXISTS `recording_pakan-backup`;
CREATE TABLE `recording_pakan-backup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `reff` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_pakan-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `recording_pakan-backup25122016`;
CREATE TABLE `recording_pakan-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `reff` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_pakan_detail
-- ----------------------------
DROP TABLE IF EXISTS `recording_pakan_detail`;
CREATE TABLE `recording_pakan_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_pakan` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_pakan_detail-backup
-- ----------------------------
DROP TABLE IF EXISTS `recording_pakan_detail-backup`;
CREATE TABLE `recording_pakan_detail-backup`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_pakan` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_pakan_detail-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `recording_pakan_detail-backup25122016`;
CREATE TABLE `recording_pakan_detail-backup25122016`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_pakan` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah_satuan` int(11) NULL DEFAULT NULL,
  `jumlah_total` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_telur
-- ----------------------------
DROP TABLE IF EXISTS `recording_telur`;
CREATE TABLE `recording_telur`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_recording` int(11) NULL DEFAULT NULL,
  `reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun_perkiraan` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `total` decimal(20, 2) NULL DEFAULT NULL,
  `total_kg` decimal(10, 2) NULL DEFAULT NULL,
  `total_butir` int(11) NULL DEFAULT NULL,
  `tipe_stok` enum('Awal','Tambah','Kurang','Akhir') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` tinyint(4) NULL DEFAULT 1,
  `is_void` tinyint(4) NULL DEFAULT 0,
  `is_jrnl` tinyint(4) NULL DEFAULT 0,
  `is_post` tinyint(4) NULL DEFAULT 1,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_telur_detail
-- ----------------------------
DROP TABLE IF EXISTS `recording_telur_detail`;
CREATE TABLE `recording_telur_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_telur` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `jumlah_total` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_butir` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NOT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`, `id_satuan`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recording_telur_detail_copy
-- ----------------------------
DROP TABLE IF EXISTS `recording_telur_detail_copy`;
CREATE TABLE `recording_telur_detail_copy`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `id_recording_telur` int(11) NULL DEFAULT NULL,
  `faktur_recording` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_record` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `harga` decimal(20, 2) NULL DEFAULT NULL,
  `jumlah_total` decimal(10, 2) NULL DEFAULT NULL,
  `jumlah_butir` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NOT NULL,
  `subtotal` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`, `id_satuan`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rekening
-- ----------------------------
DROP TABLE IF EXISTS `rekening`;
CREATE TABLE `rekening`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cJenis` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cGlobal` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cKelompok` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cJenisAcc` varchar(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cType` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cGroup` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cLevel` int(11) NULL DEFAULT NULL,
  `StTampil` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  `cParent` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Awal` double NOT NULL DEFAULT 0,
  `Debet` double NOT NULL DEFAULT 0,
  `Kredit` double NOT NULL DEFAULT 0,
  `Akhir` double NOT NULL DEFAULT 0,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`, `Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1062 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rekening-backup30122016
-- ----------------------------
DROP TABLE IF EXISTS `rekening-backup30122016`;
CREATE TABLE `rekening-backup30122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cJenis` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cGlobal` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cKelompok` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cJenisAcc` varchar(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cType` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cGroup` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cLevel` int(11) NULL DEFAULT NULL,
  `StTampil` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1',
  `cParent` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Awal` double NOT NULL DEFAULT 0,
  `Debet` double NOT NULL DEFAULT 0,
  `Kredit` double NOT NULL DEFAULT 0,
  `Akhir` double NOT NULL DEFAULT 0,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 987 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rekening_saldo
-- ----------------------------
DROP TABLE IF EXISTS `rekening_saldo`;
CREATE TABLE `rekening_saldo`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bukti` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `akun` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rute
-- ----------------------------
DROP TABLE IF EXISTS `rute`;
CREATE TABLE `rute`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wilayah_id` int(11) NULL DEFAULT NULL,
  `rute` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for saldobarang
-- ----------------------------
DROP TABLE IF EXISTS `saldobarang`;
CREATE TABLE `saldobarang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `bulan` int(11) NULL DEFAULT NULL,
  `tahun` year NULL DEFAULT NULL,
  `id_cabang` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for saldobarang_detail
-- ----------------------------
DROP TABLE IF EXISTS `saldobarang_detail`;
CREATE TABLE `saldobarang_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for saldorekening
-- ----------------------------
DROP TABLE IF EXISTS `saldorekening`;
CREATE TABLE `saldorekening`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `noacc` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_supplier` int(11) NULL DEFAULT NULL,
  `tipe` enum('H','P') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `bulan` date NULL DEFAULT NULL,
  `tahun` year NULL DEFAULT NULL,
  `awaltahun` decimal(20, 2) NULL DEFAULT NULL,
  `awalbulan` decimal(20, 2) NULL DEFAULT NULL,
  `awal` decimal(20, 2) NULL DEFAULT NULL,
  `debet` decimal(20, 2) NULL DEFAULT NULL,
  `kredit` decimal(20, 2) NULL DEFAULT NULL,
  `saldo` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales
-- ----------------------------
DROP TABLE IF EXISTS `sales`;
CREATE TABLE `sales`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_karyawan` int(11) NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_order
-- ----------------------------
DROP TABLE IF EXISTS `sales_order`;
CREATE TABLE `sales_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl` date NULL DEFAULT NULL,
  `tgl_kedaluarsa` date NULL DEFAULT NULL,
  `tgl_terima` date NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_sales` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `biaya_lain` decimal(20, 2) NULL DEFAULT NULL,
  `pajak` decimal(5, 2) NULL DEFAULT NULL,
  `total_pajak` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `status` enum('Terima','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Terima',
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `faktur_po`(`faktur`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `sales_order_detail`;
CREATE TABLE `sales_order_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_so` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_jual` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` decimal(20, 2) NULL DEFAULT NULL,
  `diskon2` decimal(20, 2) NULL DEFAULT NULL,
  `diskon3` decimal(20, 2) NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE,
  INDEX `fkpo`(`faktur`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_quote
-- ----------------------------
DROP TABLE IF EXISTS `sales_quote`;
CREATE TABLE `sales_quote`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tanggal` date NULL DEFAULT NULL,
  `kedaluarsa` date NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `kepada` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alamat` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_sales` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `status` enum('Menunggu','Proses','Tutup','Terima') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Menunggu',
  `diskon1` decimal(10, 2) NULL DEFAULT NULL,
  `diskon2` decimal(10, 2) NULL DEFAULT NULL,
  `pajak1` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `pajak2` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `is_approved` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_quote_detail
-- ----------------------------
DROP TABLE IF EXISTS `sales_quote_detail`;
CREATE TABLE `sales_quote_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_reff` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `jumlah_pesanan` int(11) NULL DEFAULT NULL,
  `diskon1` decimal(10, 2) NULL DEFAULT NULL,
  `diskon2` decimal(10, 2) NULL DEFAULT NULL,
  `pajak1` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `pajak2` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_return
-- ----------------------------
DROP TABLE IF EXISTS `sales_return`;
CREATE TABLE `sales_return`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_sr` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_st` int(11) NULL DEFAULT NULL,
  `tgl_sr` date NULL DEFAULT NULL,
  `tipe_retur` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `akun_piutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `totalretur` decimal(20, 2) NULL DEFAULT NULL,
  `bayar` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_return_detail
-- ----------------------------
DROP TABLE IF EXISTS `sales_return_detail`;
CREATE TABLE `sales_return_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur_sr` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_sr` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `harga_jual` decimal(20, 2) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_trx
-- ----------------------------
DROP TABLE IF EXISTS `sales_trx`;
CREATE TABLE `sales_trx`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl` date NULL DEFAULT NULL,
  `tgl_jtempo` date NULL DEFAULT NULL,
  `id_so` int(11) NULL DEFAULT NULL,
  `termin` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_sales` int(11) NULL DEFAULT NULL,
  `akun_piutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_shipping` int(11) NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `ppn` decimal(4, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  `diskon` decimal(4, 2) NULL DEFAULT NULL,
  `faktur_so` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_trx-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `sales_trx-backup25122016`;
CREATE TABLE `sales_trx-backup25122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `tgl` date NULL DEFAULT NULL,
  `tgl_jtempo` date NULL DEFAULT NULL,
  `id_so` int(11) NULL DEFAULT NULL,
  `termin` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_sales` int(11) NULL DEFAULT NULL,
  `akun_piutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ref` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_shipping` int(11) NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `ppn` decimal(4, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  `diskon` decimal(4, 2) NULL DEFAULT NULL,
  `faktur_so` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_trx_detail
-- ----------------------------
DROP TABLE IF EXISTS `sales_trx_detail`;
CREATE TABLE `sales_trx_detail`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_st` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_jual` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `diskon3` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  `faktur_so` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_trx_detail-backup25122016
-- ----------------------------
DROP TABLE IF EXISTS `sales_trx_detail-backup25122016`;
CREATE TABLE `sales_trx_detail-backup25122016`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_ref` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_st` int(11) NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_jual` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `diskon3` double(20, 2) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  `faktur_so` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_trx_detailxxx
-- ----------------------------
DROP TABLE IF EXISTS `sales_trx_detailxxx`;
CREATE TABLE `sales_trx_detailxxx`  (
  `id_detail` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `faktur_so` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jumlah` decimal(10, 2) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `harga_jual` decimal(20, 2) NULL DEFAULT NULL,
  `diskon1` double(20, 2) NULL DEFAULT NULL,
  `diskon2` double(20, 2) NULL DEFAULT NULL,
  `diskon3` double(20, 2) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id_detail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales_trxxx
-- ----------------------------
DROP TABLE IF EXISTS `sales_trxxx`;
CREATE TABLE `sales_trxxx`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `faktur_so` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_so` int(11) NULL DEFAULT NULL,
  `tgl` date NULL DEFAULT NULL,
  `tgl_jtempo` date NULL DEFAULT NULL,
  `termin` int(11) NULL DEFAULT NULL,
  `id_mitra` int(11) NULL DEFAULT NULL,
  `id_customer` int(11) NULL DEFAULT NULL,
  `id_sales` int(11) NULL DEFAULT NULL,
  `akun_piutang` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(11) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_shipping` int(11) NULL DEFAULT NULL,
  `id_bayar` int(11) NULL DEFAULT NULL,
  `totalbayar` decimal(20, 2) NULL DEFAULT NULL,
  `uangmuka` decimal(20, 2) NULL DEFAULT NULL,
  `biayakirim` decimal(20, 2) NULL DEFAULT NULL,
  `ppn` decimal(4, 2) NULL DEFAULT NULL,
  `grandtotal` decimal(20, 2) NULL DEFAULT NULL,
  `diskon` decimal(4, 2) NULL DEFAULT NULL,
  `sisa` decimal(20, 2) NULL DEFAULT NULL,
  `status` enum('Baru','Terkirim','Proses','Selesai') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT 'Baru',
  `is_trx` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `is_void` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_jrnl` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_post` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `date_posted` datetime NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for satuan
-- ----------------------------
DROP TABLE IF EXISTS `satuan`;
CREATE TABLE `satuan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(25) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 31 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for satuan_barang
-- ----------------------------
DROP TABLE IF EXISTS `satuan_barang`;
CREATE TABLE `satuan_barang`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NULL DEFAULT NULL,
  `id_satuan` int(11) NULL DEFAULT NULL,
  `isactive` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sessions
-- ----------------------------
DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions`  (
  `session_id` varchar(40) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `ip_address` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `user_agent` varchar(120) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `last_activity` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `user_data` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`session_id`) USING BTREE,
  INDEX `last_activity_idx`(`last_activity`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for setup_account
-- ----------------------------
DROP TABLE IF EXISTS `setup_account`;
CREATE TABLE `setup_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_siklus` tinyint(4) NULL DEFAULT NULL,
  `id_rekening` int(11) NULL DEFAULT NULL,
  `nama_account` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `parent` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for setup_bulan_keuangan
-- ----------------------------
DROP TABLE IF EXISTS `setup_bulan_keuangan`;
CREATE TABLE `setup_bulan_keuangan`  (
  `id` int(11) NOT NULL,
  `bulan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `bulan_num` int(11) NULL DEFAULT NULL,
  `awal_bulan` date NULL DEFAULT NULL,
  `akhir_bulan` date NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for setup_company
-- ----------------------------
DROP TABLE IF EXISTS `setup_company`;
CREATE TABLE `setup_company`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alamat1` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `alamat2` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `propinsi` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `telp1` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `telp2` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `fax1` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `fax2` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `email` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `website` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `uang1` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `uang2` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `bidang` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `logo_url` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for setup_tahun_keuangan
-- ----------------------------
DROP TABLE IF EXISTS `setup_tahun_keuangan`;
CREATE TABLE `setup_tahun_keuangan`  (
  `id` int(11) NOT NULL,
  `tahun` year NULL DEFAULT NULL,
  `awal_tahun` date NULL DEFAULT NULL,
  `akhir_tahun` date NULL DEFAULT NULL,
  `status_tahun` tinyint(4) NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for siklus
-- ----------------------------
DROP TABLE IF EXISTS `siklus`;
CREATE TABLE `siklus`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_siklus` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tag` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for standart
-- ----------------------------
DROP TABLE IF EXISTS `standart`;
CREATE TABLE `standart`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Umur` double NOT NULL DEFAULT 0,
  `Berat` double NOT NULL DEFAULT 0,
  `Pakan` double NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 139 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for stockin_request
-- ----------------------------
DROP TABLE IF EXISTS `stockin_request`;
CREATE TABLE `stockin_request`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NULL DEFAULT NULL,
  `jml` float NULL DEFAULT NULL,
  `stok` float NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `id_gudang` int(11) NULL DEFAULT NULL,
  `id_kandang` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for stok
-- ----------------------------
DROP TABLE IF EXISTS `stok`;
CREATE TABLE `stok`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_barang` int(11) NOT NULL,
  `stok_awal` decimal(6, 0) NULL DEFAULT NULL,
  `stok_in` decimal(6, 0) NULL DEFAULT NULL,
  `stok_out` decimal(6, 0) NULL DEFAULT NULL,
  `stok_wip` decimal(6, 0) NULL DEFAULT NULL,
  `stok_po` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `stok_so` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `stok_do` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `stok_op` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for subgolongan
-- ----------------------------
DROP TABLE IF EXISTS `subgolongan`;
CREATE TABLE `subgolongan`  (
  `Kode` char(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Keterangan` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Golongan` char(3) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  INDEX `NewIndex`(`Kode`, `Golongan`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supir_armada
-- ----------------------------
DROP TABLE IF EXISTS `supir_armada`;
CREATE TABLE `supir_armada`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_karyawan` int(11) NULL DEFAULT NULL,
  `nama_supir` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `no_ktp` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier
-- ----------------------------
DROP TABLE IF EXISTS `supplier`;
CREATE TABLE `supplier`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Nama` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Telepon` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Fax` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Contact` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `JthTempo` double NOT NULL DEFAULT 0,
  `Diskon` double NOT NULL DEFAULT 0,
  `NPWP` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Hutang` double NOT NULL DEFAULT 0,
  `Bayar` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `Bank` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `AnRekening` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Potongan` double NOT NULL DEFAULT 0,
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`, `Kode`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 75 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier-backup30122016
-- ----------------------------
DROP TABLE IF EXISTS `supplier-backup30122016`;
CREATE TABLE `supplier-backup30122016`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Nama` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Telepon` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Fax` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Contact` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `JthTempo` double NOT NULL DEFAULT 0,
  `Diskon` double NOT NULL DEFAULT 0,
  `NPWP` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Hutang` double NOT NULL DEFAULT 0,
  `Bayar` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `Bank` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `AnRekening` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Potongan` double NOT NULL DEFAULT 0,
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 109 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier-kosong
-- ----------------------------
DROP TABLE IF EXISTS `supplier-kosong`;
CREATE TABLE `supplier-kosong`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Nama` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Alamat` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Telepon` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Fax` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Contact` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `JthTempo` double NOT NULL DEFAULT 0,
  `Diskon` double NOT NULL DEFAULT 0,
  `NPWP` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Hutang` double NOT NULL DEFAULT 0,
  `Bayar` double NOT NULL DEFAULT 0,
  `Sisa` double NOT NULL DEFAULT 0,
  `Bank` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Rekening` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `AnRekening` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Potongan` double NOT NULL DEFAULT 0,
  `User` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `Kode`(`Kode`) USING BTREE,
  INDEX `Kode_2`(`Kode`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tabungan
-- ----------------------------
DROP TABLE IF EXISTS `tabungan`;
CREATE TABLE `tabungan`  (
  `Faktur` varchar(12) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `Tgl` date NULL DEFAULT NULL,
  `Kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NmKaryawan` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `NoAcc` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Debet` double NOT NULL DEFAULT 0,
  `Kredit` double NOT NULL DEFAULT 0,
  `Keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `User` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `Time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cNoJrn` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lVoid` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `lPosted` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  INDEX `NewIndex`(`Faktur`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbjurnal
-- ----------------------------
DROP TABLE IF EXISTS `tbjurnal`;
CREATE TABLE `tbjurnal`  (
  `cNoJrn` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `dTanggal` datetime NULL DEFAULT NULL,
  `cNoBukti` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cKeterangan` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lNoEdit` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lMemo` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `lPosted` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0',
  `lVoid` varchar(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `dTglBtlPost` datetime NULL DEFAULT NULL,
  `dTglVoid` datetime NULL DEFAULT NULL,
  `cAlasanBatal` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `cAlasanVoid` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `UserId` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `UserDate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `nPrinted` decimal(2, 0) NULL DEFAULT NULL,
  PRIMARY KEY (`cNoJrn`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for temp_nomor
-- ----------------------------
DROP TABLE IF EXISTS `temp_nomor`;
CREATE TABLE `temp_nomor`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order` int(11) NULL DEFAULT NULL,
  `nomor` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tokenid` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `sessionid` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jenis` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `isactive` tinyint(4) NULL DEFAULT NULL,
  `agent` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `ipaddress` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `userid` int(11) NULL DEFAULT NULL,
  `modified` datetime NULL DEFAULT NULL,
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `id_struktur` int(11) NULL DEFAULT NULL,
  `ip_address` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `username` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `salt` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `activation_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `forgotten_password_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `forgotten_password_time` int(10) UNSIGNED NULL DEFAULT NULL,
  `remember_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_on` int(10) UNSIGNED NOT NULL,
  `last_login` int(10) UNSIGNED NULL DEFAULT NULL,
  `active` tinyint(1) UNSIGNED NULL DEFAULT NULL,
  `first_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `last_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `company` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users_groups
-- ----------------------------
DROP TABLE IF EXISTS `users_groups`;
CREATE TABLE `users_groups`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL,
  `group_id` mediumint(8) UNSIGNED NOT NULL DEFAULT 4,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uc_users_groups`(`user_id`, `group_id`) USING BTREE,
  INDEX `fk_users_groups_users1_idx`(`user_id`) USING BTREE,
  INDEX `fk_users_groups_groups1_idx`(`group_id`) USING BTREE,
  CONSTRAINT `users_groups_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `users_groups_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wilayah
-- ----------------------------
DROP TABLE IF EXISTS `wilayah`;
CREATE TABLE `wilayah`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kd_wilayah` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `wilayah` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `propinsi` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `keterangan` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `id_user` int(11) NULL DEFAULT NULL,
  `status` enum('1','0') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '1',
  `datetime` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wp
-- ----------------------------
DROP TABLE IF EXISTS `wp`;
CREATE TABLE `wp`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `npwp` char(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `nama` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `alamat` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `kota` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `telpon` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `fax` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `email` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `jenis_usaha` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `klu` char(6) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `pemilik` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `npwp_pemilik` char(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `keterangan` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for 00-00-01-00-view-barang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-00-view-barang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-00-view-barang` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from `barang` `a`;

-- ----------------------------
-- View structure for 00-00-01-01-view-barang-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-01-view-barang-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-01-view-barang-supplier` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime`,`b`.`Nama` AS `nmsupplier`,`b`.`Alamat` AS `Alamat`,`b`.`Kode` AS `kdsupplier`,`b`.`id` AS `id_supplier` from (`00-00-01-00-view-barang` `a` left join `supplier` `b` on((`b`.`Kode` = `a`.`id_supplier`))) where (`b`.`Kode` is not null) group by `a`.`Kode` order by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-01-02-view-barang-no-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-02-view-barang-no-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-02-view-barang-no-supplier` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from (`00-00-01-00-view-barang` `a` left join `supplier` `b` on((`b`.`Kode` = `a`.`id_supplier`))) where (isnull(`b`.`Kode`) or isnull(`a`.`id_supplier`));

-- ----------------------------
-- View structure for 00-00-01-03-view-barang-tanpa-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-03-view-barang-tanpa-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-03-view-barang-tanpa-supplier` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from (`00-00-01-00-view-barang` `a` left join `supplier` `b` on((`b`.`Kode` = `a`.`id_supplier`))) where isnull(`a`.`id_supplier`);

-- ----------------------------
-- View structure for 00-00-01-04-view-semua-barang-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-04-view-semua-barang-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-04-view-semua-barang-supplier` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime`,`b`.`Nama` AS `nmsupplier`,`b`.`Alamat` AS `Alamat`,`b`.`Kode` AS `kdsupplier` from (`00-00-01-00-view-barang` `a` left join `supplier` `b` on((`b`.`Kode` = `a`.`id_supplier`)));

-- ----------------------------
-- View structure for 00-00-01-05-drop-barang-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-05-drop-barang-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-05-drop-barang-supplier` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`b`.`id` AS `id_supplier`,`b`.`Kode` AS `kdsupplier`,`b`.`Nama` AS `nmsupplier`,`b`.`Alamat` AS `Alamat` from (`00-00-01-00-view-barang` `a` left join `supplier` `b` on((`b`.`Kode` = `a`.`id_supplier`))) where (`b`.`Kode` is not null) group by `a`.`Kode` order by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-01-05-view-barang-satuan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-05-view-barang-satuan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-05-view-barang-satuan` AS select `a`.`id` AS `id`,`a`.`id_barang` AS `id_barang`,`a`.`kode` AS `kode`,`b`.`Nama` AS `nmbarang`,`a`.`Satuan1` AS `Satuan1`,`a`.`Isi2` AS `Isi2`,`a`.`Satuan2` AS `Satuan2`,`a`.`Isi3` AS `Isi3`,`a`.`Satuan3` AS `Satuan3`,`a`.`Max` AS `Max`,`a`.`SatuanMax` AS `SatuanMax`,`a`.`Min` AS `Min`,`a`.`SatuanMin` AS `SatuanMin` from (`barang_satuan` `a` join `barang` `b` on((`b`.`id` = `a`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-01-06-view-barang-kategori
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-06-view-barang-kategori`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-06-view-barang-kategori` AS select `barang`.`id` AS `id`,`barang`.`Kode` AS `Kode`,`barang`.`Nama` AS `Nama`,`barang`.`id_golongan` AS `id_golongan` from `barang` order by `barang`.`Kode`,`barang`.`id_golongan`;

-- ----------------------------
-- View structure for 00-00-01-07-view-barang-ayam
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-07-view-barang-ayam`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-07-view-barang-ayam` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from `barang` `a` where (`a`.`id_golongan` = '05');

-- ----------------------------
-- View structure for 00-00-01-08-view-barang-pakan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-08-view-barang-pakan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-08-view-barang-pakan` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from `barang` `a` where (`a`.`id_golongan` = '02');

-- ----------------------------
-- View structure for 00-00-01-09-view-barang-obatvaksin
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-09-view-barang-obatvaksin`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-09-view-barang-obatvaksin` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from `barang` `a` where ((`a`.`id_golongan` = '03') or (`a`.`id_golongan` = '15'));

-- ----------------------------
-- View structure for 00-00-01-10-view-barang-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-10-view-barang-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-10-view-barang-telur` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from `barang` `a` where (`a`.`id_golongan` = '01');

-- ----------------------------
-- View structure for 00-00-01-11-view-barang-sapronak
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-11-view-barang-sapronak`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-11-view-barang-sapronak` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Cabang` AS `Cabang`,`a`.`Nama` AS `Nama`,`a`.`Keterangan` AS `Keterangan`,`a`.`StKon` AS `StKon`,`a`.`id_golongan` AS `id_golongan`,`a`.`id_supplier` AS `id_supplier`,`a`.`User` AS `User`,`a`.`datetime` AS `datetime` from `barang` `a` where (`a`.`id_golongan` = '06');

-- ----------------------------
-- View structure for 00-00-01-12-view-barang-beli-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-12-view-barang-beli-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-12-view-barang-beli-customer` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama` from `00-00-01-00-view-barang` `a` where ((`a`.`Nama` like '%sekem%') or (`a`.`Nama` like 'retak') or (`a`.`Nama` like 'telur') or (`a`.`Nama` like 'jagung') or (`a`.`Nama` like 'katul') or (`a`.`Nama` like 'egg tray'));

-- ----------------------------
-- View structure for 00-00-01-13-view-barang-harga-table
-- ----------------------------
DROP VIEW IF EXISTS `00-00-01-13-view-barang-harga-table`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-01-13-view-barang-harga-table` AS select `a`.`id` AS `id`,`b`.`id_barang` AS `id_barang`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`b`.`hb1` AS `hb1`,`b`.`hb2` AS `hb2`,`b`.`hb3` AS `hb3`,`b`.`HJ1R` AS `hj1r`,`b`.`HJ2R` AS `hj2r`,`b`.`HJ3R` AS `hj3r`,`b`.`max` AS `max`,`b`.`datetime` AS `datetime` from (`barang` `a` join `barang_harga` `b` on((`a`.`id` = `b`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-02-00-view-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-02-00-view-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-02-00-view-supplier` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`Alamat` AS `Alamat`,`a`.`Kota` AS `Kota`,`a`.`Telepon` AS `Telepon`,`a`.`Fax` AS `Fax`,`a`.`NoAcc` AS `NoAcc`,`a`.`Contact` AS `Contact` from `supplier` `a`;

-- ----------------------------
-- View structure for 00-00-02-01-view-supplier-hutang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-02-01-view-supplier-hutang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-02-01-view-supplier-hutang` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`Alamat` AS `Alamat`,`a`.`Kota` AS `Kota`,`a`.`Telepon` AS `Telepon`,`a`.`Fax` AS `Fax`,`a`.`NoAcc` AS `NoAcc`,`a`.`Contact` AS `Contact`,cast(`a`.`Hutang` as decimal(20,2)) AS `Hutang`,cast(`a`.`Bayar` as decimal(20,2)) AS `Bayar`,cast(`a`.`Sisa` as decimal(20,2)) AS `Sisa` from `supplier` `a` where (`a`.`Hutang` > 0);

-- ----------------------------
-- View structure for 00-00-02-02-view-supplier-kode
-- ----------------------------
DROP VIEW IF EXISTS `00-00-02-02-view-supplier-kode`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-02-02-view-supplier-kode` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kode`,substr(`a`.`Kode`,3,4) AS `right`,`a`.`Nama` AS `nama` from `00-00-02-00-view-supplier` `a`;

-- ----------------------------
-- View structure for 00-00-03-00-view-po-invoice
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-00-view-po-invoice`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-00-view-po-invoice` AS select `a`.`id` AS `id`,`a`.`faktur_invoice` AS `faktur_invoice`,`a`.`faktur_po` AS `faktur_po`,`a`.`id_supplier` AS `id_supplier`,`a`.`tgl_invoice` AS `tgl_invoice`,`a`.`pembayaran` AS `pembayaran`,`a`.`jatuh_tempo` AS `jatuh_tempo`,`a`.`status` AS `status`,`a`.`datetime` AS `datetime` from `invoice_po` `a`;

-- ----------------------------
-- View structure for 00-00-03-01-view-po-2015
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-01-view-po-2015`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-01-view-po-2015` AS select `a`.`id` AS `id`,`a`.`Faktur` AS `Faktur`,date_format(`a`.`Time`,'%d-%m-%Y') AS `tgl_po`,format(sum(cast(`a`.`Jumlah` as decimal(20,2))),2,'id_ID') AS `jtotal`,date_format(`a`.`Time`,'%Y') AS `tahun` from `beli` `a` where (date_format(`a`.`Time`,'%Y') = '2015') group by `a`.`Faktur`;

-- ----------------------------
-- View structure for 00-00-03-01-view-po-trx
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-01-view-po-trx`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-01-view-po-trx` AS select `a`.`id` AS `idpo`,`a`.`faktur_po` AS `faktur_po`,`a`.`tgl_po` AS `tgl_po`,`a`.`status` AS `status`,`b`.`Kode` AS `kdsupplier`,`b`.`Nama` AS `namasupplier`,`a`.`tgl_kirim_po` AS `tgl_kirim_po`,`a`.`tgl_jatuhtempo` AS `tgl_jatuhtempo`,`a`.`totalbayar` AS `totalbayar`,`c`.`jenis_pembayaran` AS `jenis_pembayaran`,`a`.`keterangan` AS `keterangan`,md5(`a`.`id`) AS `md5id`,`a`.`grandtotal` AS `grandtotal`,`b`.`id` AS `idsupplier`,`d`.`id` AS `idpt`,`d`.`faktur_pt` AS `usedby` from (((`purchase_order` `a` join `supplier` `b` on((`b`.`id` = `a`.`id_supplier`))) left join `jenis_pembayaran` `c` on((`c`.`id` = `a`.`id_bayar`))) left join `purchase_transaction` `d` on((`d`.`id_po` = `a`.`id`))) order by `a`.`faktur_po`;

-- ----------------------------
-- View structure for 00-00-03-02-view-po-detail-2015
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-02-view-po-detail-2015`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-02-view-po-detail-2015` AS select `a`.`id` AS `id`,`a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,`a`.`Kode` AS `Kode`,`a`.`NmBarang` AS `NmBarang`,cast(`a`.`Qty` as decimal(10,0)) AS `Qty`,`a`.`Satuan` AS `Satuan`,format(cast(`a`.`Harga` as decimal(20,2)),2,'id_ID') AS `harga`,format(cast(`a`.`Jumlah` as decimal(20,2)),2,'id_ID') AS `jtotal`,`a`.`Status` AS `Status`,`a`.`User` AS `User`,`a`.`Time` AS `tanggal`,date_format(`a`.`Time`,'%Y') AS `tahun` from `beli` `a` where (date_format(`a`.`Time`,'%Y') = '2015');

-- ----------------------------
-- View structure for 00-00-03-03-view-detail-po-lamax
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-03-view-detail-po-lamax`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-03-view-detail-po-lamax` AS select `a`.`id_detail` AS `id_detail`,`a`.`po` AS `po`,`a`.`jumlah` AS `jumlah`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`d`.`hb1` AS `harga_kemasan`,`d`.`hb2` AS `harga_satuan`,`e`.`Satuan1` AS `satuan_kemasan`,`e`.`Satuan2` AS `satuan`,(`a`.`jumlah` * `d`.`hb1`) AS `subtot_kemasan`,(`a`.`jumlah` * `d`.`hb2`) AS `subtot_satuan` from (((`purchase_order_detail` `a` join `barang` `c` on((`c`.`id` = `a`.`id_barang`))) join `barang_harga` `d` on((`d`.`id_barang` = `c`.`id`))) join `barang_satuan` `e` on((`e`.`id_barang` = `a`.`id_barang`))) order by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-03-04-view-detail-po
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-04-view-detail-po`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-04-view-detail-po` AS select `a`.`id_detail` AS `id_detail`,`a`.`po` AS `po`,`c`.`id` AS `idbarang`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`a`.`jumlah` AS `jumlah`,`a`.`jumlah_akhir` AS `jumlah_akhir`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`e`.`Satuan1`,if((`a`.`id_satuan` = 2),`e`.`Satuan2`,`e`.`Satuan3`)) AS `satuan`,if((isnull(`a`.`harga_beli`) or (`a`.`harga_beli` = 0)),if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)),`a`.`harga_beli`) AS `harga`,(if((isnull(`a`.`harga_beli`) or (`a`.`harga_beli` = 0)),if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)),`a`.`harga_beli`) * `a`.`jumlah`) AS `subtotal`,`a`.`harga_beli` AS `harga_beli`,`a`.`isactive` AS `isactive`,`a`.`isdeleted` AS `isdeleted`,`c`.`id_supplier` AS `kdsp`,`c`.`NmSupplier1` AS `namasp` from (((`purchase_order_detail` `a` join `barang` `c` on((`c`.`id` = `a`.`id_barang`))) join `barang_harga` `d` on((`d`.`id_barang` = `c`.`id`))) join `barang_satuan` `e` on((`e`.`id_barang` = `a`.`id_barang`))) order by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-03-04-view-detail-po_copy
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-04-view-detail-po_copy`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-04-view-detail-po_copy` AS select `a`.`id_detail` AS `id_detail`,`a`.`po` AS `po`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`a`.`jumlah` AS `jumlah`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`e`.`Satuan1`,if((`a`.`id_satuan` = 2),`e`.`Satuan2`,`e`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga`,(`a`.`jumlah` * if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`))) AS `subtotal`,`c`.`id` AS `idbarang`,`a`.`jumlah_akhir` AS `jumlah_akhir`,`a`.`harga_beli` AS `harga_beli` from (((`purchase_order_detail` `a` join `barang` `c` on((`c`.`id` = `a`.`id_barang`))) join `barang_harga` `d` on((`d`.`id_barang` = `c`.`id`))) join `barang_satuan` `e` on((`e`.`id_barang` = `a`.`id_barang`))) order by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-03-07-view-detail-po-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-07-view-detail-po-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-07-view-detail-po-total` AS select `a`.`id_detail` AS `id_detail`,`a`.`po` AS `po`,sum(`a`.`subtotal`) AS `total`,format(cast(sum(`a`.`subtotal`) as decimal(20,2)),2,'id_ID') AS `rptotal`,`a`.`isactive` AS `isactive`,`a`.`isdeleted` AS `isdeleted` from `00-00-03-04-view-detail-po` `a` where ((`a`.`isactive` = 1) and ((`a`.`isdeleted` = 0) or isnull(`a`.`isdeleted`))) group by `a`.`po`;

-- ----------------------------
-- View structure for 00-00-03-09-view-po-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-09-view-po-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-09-view-po-supplier` AS select distinct `a`.`id` AS `id`,`a`.`faktur_po` AS `faktur_po`,`a`.`tgl_po` AS `tgl_po`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`c`.`JthTempo` AS `JthTempo`,`a`.`id_supplier` AS `id_supplier`,`b`.`pt` AS `pt`,`a`.`isactive` AS `isactivepo`,`a`.`isvalidpo` AS `isvalidpo`,`b`.`isactive` AS `isactivedetail` from ((`purchase_order` `a` left join `supplier` `c` on((`a`.`id_supplier` = `c`.`id`))) left join `purchase_transaction_detail` `b` on((`a`.`faktur_po` = `b`.`po`))) where (`a`.`id_supplier` > 0);

-- ----------------------------
-- View structure for 00-00-03-09-view-po-supplier-backup31102016
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-09-view-po-supplier-backup31102016`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-09-view-po-supplier-backup31102016` AS select distinct `a`.`id` AS `id`,`a`.`faktur_po` AS `faktur_po`,`a`.`tgl_po` AS `tgl_po`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`c`.`JthTempo` AS `JthTempo`,`a`.`id_supplier` AS `id_supplier` from (`purchase_order` `a` left join `supplier` `c` on((`a`.`id_supplier` = `c`.`id`))) where (`a`.`id_supplier` > 0);

-- ----------------------------
-- View structure for 00-00-03-10-view-detail-po
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-10-view-detail-po`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-10-view-detail-po` AS select `b`.`id_detail` AS `id_detail`,`b`.`po` AS `po`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`b`.`jumlah` AS `jumlah`,`b`.`jumlah_akhir` AS `jumlah_akhir`,if((`b`.`id_satuan` = 1),`e`.`hb1`,if((`b`.`id_satuan` = 2),`e`.`hb2`,`e`.`hb3`)) AS `harga`,if((`b`.`id_satuan` = 1),`d`.`Satuan1`,if((`b`.`id_satuan` = 2),`d`.`Satuan2`,`d`.`Satuan3`)) AS `satuan`,(`e`.`hb1` * `b`.`jumlah`) AS `subtot1`,(if((`b`.`id_satuan` = 1),`c`.`Isi2`,0) * `b`.`jumlah`) AS `jml_isi1`,`d`.`Satuan2` AS `Satuan2`,(`e`.`hb2` * `b`.`jumlah`) AS `subtot2`,(if((`b`.`id_satuan` = 2),`c`.`Isi3`,0) * `b`.`jumlah`) AS `jml_isi2`,`d`.`Satuan3` AS `Satuan3`,(`e`.`hb3` * `b`.`jumlah`) AS `subtot3`,`b`.`id_barang` AS `id_barang`,`b`.`id_satuan` AS `id_satuan`,`b`.`harga_beli` AS `harga_beli`,`a`.`tgl_po` AS `tgl_po`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_customer` AS `id_customer`,`a`.`termin_hari` AS `termin_hari`,`a`.`isactive` AS `isactivepo`,`b`.`isactive` AS `isactive` from ((((`purchase_order_detail` `b` left join `barang` `c` on((`c`.`id` = `b`.`id_barang`))) left join `barang_satuan` `d` on((`c`.`id` = `d`.`id_barang`))) join `barang_harga` `e` on((`d`.`id_barang` = `e`.`id_barang`))) left join `purchase_order` `a` on((`a`.`faktur_po` = `b`.`po`))) order by `b`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-03-11-view-po-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-11-view-po-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-11-view-po-customer` AS select distinct `a`.`id` AS `id`,`a`.`faktur_po` AS `faktur_po`,`a`.`tgl_po` AS `tgl_po`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`id_customer` AS `id_customer`,`b`.`JthTempo` AS `JthTempo` from (`purchase_order` `a` left join `customer` `b` on((`a`.`id_customer` = `b`.`id`))) where ((`a`.`id_customer` is not null) or (`a`.`id_customer` > 0));

-- ----------------------------
-- View structure for 00-00-03-12-view-po-detailjumlah
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-12-view-po-detailjumlah`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-12-view-po-detailjumlah` AS select `a`.`id` AS `id`,`a1`.`id_detail` AS `id_detail`,`a`.`faktur_po` AS `faktur_po`,`a`.`tgl_po` AS `tgl_po`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_customer` AS `id_customer`,`b`.`kode` AS `kode`,`c`.`Nama` AS `Nama`,`a1`.`id_barang` AS `id_barang`,`a1`.`jumlah` AS `jumlah`,`a1`.`jumlah_akhir` AS `jumlah_akhir`,`a1`.`id_satuan` AS `id_satuan`,if((`a1`.`id_satuan` = 1),`b`.`Satuan1`,if((`a1`.`id_satuan` = 2),`b`.`Satuan2`,`b`.`Satuan3`)) AS `satuan`,if((`a1`.`id_satuan` = 1),`a1`.`jumlah`,NULL) AS `qty1`,if((`a1`.`id_satuan` = 1),`b`.`Satuan1`,NULL) AS `sat1`,if((`a1`.`id_satuan` = 2),`a1`.`jumlah`,NULL) AS `qty2`,if((`a1`.`id_satuan` = 2),`b`.`Satuan2`,NULL) AS `sat2`,if((`a1`.`id_satuan` = 3),`a1`.`jumlah`,NULL) AS `qty3`,if((`a1`.`id_satuan` = 3),`b`.`Satuan3`,NULL) AS `sat3`,`b`.`Satuan1` AS `Satuan1`,`b`.`Isi2` AS `Isi2`,`b`.`Satuan2` AS `Satuan2`,`b`.`Isi3` AS `Isi3`,`b`.`Satuan3` AS `Satuan3`,`b`.`Max` AS `Max`,`b`.`SatuanMax` AS `SatuanMax`,`b`.`Min` AS `Min`,`b`.`SatuanMin` AS `SatuanMin` from (((`purchase_order` `a` left join `purchase_order_detail` `a1` on((`a1`.`po` = `a`.`faktur_po`))) left join `barang_satuan` `b` on((`b`.`id_barang` = `a1`.`id_barang`))) join `barang` `c` on((`a1`.`id_barang` = `c`.`id`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-03-13-view-realjumlah-po
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-13-view-realjumlah-po`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-13-view-realjumlah-po` AS select `a`.`id` AS `id`,`a`.`id_detail` AS `id_detail`,`a`.`faktur_po` AS `faktur_po`,`a`.`tgl_po` AS `tgl_po`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_customer` AS `id_customer`,`a`.`kode` AS `kode`,`a`.`id_barang` AS `id_barang`,`a`.`jumlah` AS `jumlah`,`a`.`jumlah_akhir` AS `jumlah_akhir`,`a`.`jumlah` AS `realqty1`,`a`.`Satuan1` AS `realsat1`,(`a`.`jumlah` * `a`.`Isi2`) AS `realqty2`,`a`.`Satuan2` AS `realsat2`,(`a`.`jumlah` * `a`.`Isi3`) AS `realqty3`,`a`.`Satuan3` AS `realsat3`,`a`.`id_satuan` AS `id_satuan`,`a`.`qty1` AS `qty1`,`a`.`sat1` AS `sat1`,`a`.`qty2` AS `qty2`,`a`.`sat2` AS `sat2`,`a`.`qty3` AS `qty3`,`a`.`sat3` AS `sat3`,`a`.`Satuan1` AS `Satuan1`,`a`.`Isi2` AS `Isi2`,`a`.`Satuan2` AS `Satuan2`,`a`.`Isi3` AS `Isi3`,`a`.`Satuan3` AS `Satuan3`,`a`.`Max` AS `Max`,`a`.`SatuanMax` AS `SatuanMax`,`a`.`Min` AS `Min`,`a`.`SatuanMin` AS `SatuanMin` from `00-00-03-12-view-po-detailjumlah` `a`;

-- ----------------------------
-- View structure for 00-00-03-14-query-po-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-03-14-query-po-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-03-14-query-po-detail` AS select `a1`.`id` AS `id`,`a2`.`id_detail` AS `id_detail`,`a1`.`faktur_po` AS `faktur_po`,`a1`.`tgl_po` AS `tgl_po`,`a1`.`id_supplier` AS `id_supplier`,`a1`.`id_customer` AS `id_customer`,`a2`.`id_barang` AS `id_barang`,`a2`.`jumlah` AS `jumlah`,`a2`.`jumlah_akhir` AS `jumlah_akhir`,`a2`.`id_satuan` AS `id_satuan`,if((`a2`.`id_satuan` = 1),`b`.`Satuan1`,if((`a2`.`id_satuan` = 2),`b`.`Satuan2`,`b`.`Satuan3`)) AS `satuan`,`a2`.`harga_beli` AS `harga_beli`,`b`.`Isi2` AS `Isi2`,`b`.`Isi3` AS `Isi3`,`b`.`Satuan1` AS `Satuan1`,`b`.`Satuan2` AS `Satuan2`,`b`.`Satuan3` AS `Satuan3` from ((`purchase_order` `a1` left join `purchase_order_detail` `a2` on((`a2`.`po` = `a1`.`faktur_po`))) left join `barang_satuan` `b` on((`a2`.`id_barang` = `b`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-04-00-view-rekening
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-00-view-rekening`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-00-view-rekening` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `Keterangan`,`a`.`cParent` AS `parents`,if((`a`.`cKelompok` = 1),'Aktiva','Pasiva') AS `nmgroup`,`a`.`cKelompok` AS `rek_group` from `rekening` `a`;

-- ----------------------------
-- View structure for 00-00-04-01-view-rekening-aktiva
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-01-view-rekening-aktiva`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-01-view-rekening-aktiva` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,1) = 1) order by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-02-view-rekening-pasiva
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-02-view-rekening-pasiva`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-02-view-rekening-pasiva` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,1) = 2) group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-03-view-rekening-modal
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-03-view-rekening-modal`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-03-view-rekening-modal` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,1) = 3);

-- ----------------------------
-- View structure for 00-00-04-04-view-rekening-pendapatan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-04-view-rekening-pendapatan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-04-view-rekening-pendapatan` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,1) = 4) order by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-05-view-rekening-biaya
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-05-view-rekening-biaya`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-05-view-rekening-biaya` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,1) = 5) group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-06-view-rekening-bank
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-06-view-rekening-bank`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-06-view-rekening-bank` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '1.200') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-06-view-rekening-kas
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-06-view-rekening-kas`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-06-view-rekening-kas` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '1.100') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-07-view-rekening-piutang-dagang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-07-view-rekening-piutang-dagang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-07-view-rekening-piutang-dagang` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '1.250') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-08-view-rekening-hpp
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-08-view-rekening-hpp`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-08-view-rekening-hpp` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,1) = '6') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-09-view-rekening-hutang-dagang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-09-view-rekening-hutang-dagang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-09-view-rekening-hutang-dagang` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '2.300') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-10-view-rekening-piutang-karyawan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-10-view-rekening-piutang-karyawan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-10-view-rekening-piutang-karyawan` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '1.700') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-11-view-rekening-biaya-penyusutan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-11-view-rekening-biaya-penyusutan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-11-view-rekening-biaya-penyusutan` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '5.900') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-12-view-rekening-retur-jual
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-12-view-rekening-retur-jual`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-12-view-rekening-retur-jual` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '5.700') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-13-view-rek-penjualan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-13-view-rek-penjualan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-13-view-rek-penjualan` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '4.900') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-14-view-rek-pembelian
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-14-view-rek-pembelian`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-14-view-rek-pembelian` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdrekening`,`a`.`Keterangan` AS `desc`,`a`.`cParent` AS `parent`,`a`.`cKelompok` AS `rek_group`,`a`.`User` AS `users`,`a`.`Time` AS `datetime` from `rekening` `a` where (left(`a`.`Kode`,5) = '2.200') group by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-04-15-view-rekening-piutang-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-15-view-rekening-piutang-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-15-view-rekening-piutang-customer` AS select `a`.`id` AS `id`,`a`.`Kode` AS `kdsp`,`b`.`Kode` AS `kdrek`,`a`.`NoAcc` AS `NoAcc`,`a`.`Nama` AS `Nama`,`a`.`Alamat` AS `Alamat`,`a`.`JthTempo` AS `JthTempo`,`a`.`Hutang` AS `Hutang`,`a`.`Bayar` AS `Bayar`,`a`.`Sisa` AS `Sisa`,`b`.`id` AS `idrek`,`b`.`Keterangan` AS `Keterangan` from (`customer` `a` join `rekening` `b` on((`a`.`NoAcc` = `b`.`Kode`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-04-16-view-rekening-hutang-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-16-view-rekening-hutang-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-16-view-rekening-hutang-supplier` AS select `b`.`Kode` AS `kdrek`,`b`.`id` AS `idrek`,`b`.`Keterangan` AS `Keterangan`,`a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`JthTempo` AS `JthTempo`,`a`.`Hutang` AS `Hutang`,`a`.`Bayar` AS `Bayar`,`a`.`Sisa` AS `Sisa`,`a`.`Alamat` AS `Alamat`,`a`.`Kota` AS `Kota`,left(`b`.`Kode`,3) AS `asasd` from (`rekening` `b` left join `supplier` `a` on((`a`.`NoAcc` = `b`.`Kode`))) where (left(`b`.`Kode`,3) = '2.3');

-- ----------------------------
-- View structure for 00-00-04-17-view-rekening-saldo
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-17-view-rekening-saldo`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-17-view-rekening-saldo` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`noacc` AS `noacc`,`b`.`cParent` AS `induk`,`a`.`id_customer` AS `id_customer`,`a`.`id_supplier` AS `id_supplier`,`a`.`tipe` AS `tipe`,`a`.`awaltahun` AS `awaltahun`,`a`.`awal` AS `awal`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`saldo` AS `saldo`,`b`.`Kode` AS `Kode`,`b`.`cJenis` AS `cJenis`,`b`.`cGlobal` AS `cGlobal`,`b`.`Keterangan` AS `Keterangan`,`b`.`cKelompok` AS `cKelompok`,`b`.`cJenisAcc` AS `cJenisAcc`,`a`.`awalbulan` AS `awalbulan`,`a`.`tahun` AS `tahun`,if((left(`b`.`Kode`,5) = '1.250'),'piutang',if((left(`b`.`Kode`,5) = '2.300'),'hutang',NULL)) AS `jenis` from (`saldorekening` `a` join `rekening` `b` on((`a`.`noacc` = `b`.`Kode`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-04-18-view-rekening-saldoperkode
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-18-view-rekening-saldoperkode`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-18-view-rekening-saldoperkode` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`noacc` AS `noacc`,`a`.`id_customer` AS `id_customer`,`a`.`id_supplier` AS `id_supplier`,`a`.`tipe` AS `tipe`,sum(`a`.`awaltahun`) AS `awaltahun`,sum(`a`.`awal`) AS `awal`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`saldo` AS `saldo`,`b`.`Kode` AS `Kode`,`b`.`cJenis` AS `cJenis`,`b`.`cGlobal` AS `cGlobal`,`b`.`Keterangan` AS `Keterangan`,`b`.`cKelompok` AS `cKelompok`,`b`.`cJenisAcc` AS `cJenisAcc`,`a`.`awalbulan` AS `awalbulan`,`a`.`tahun` AS `tahun`,if((left(`b`.`Kode`,5) = '1.250'),'piutang',if((left(`b`.`Kode`,5) = '2.300'),'hutang',NULL)) AS `jenis` from (`saldorekening` `a` join `rekening` `b` on((`a`.`noacc` = `b`.`Kode`))) group by `b`.`Kode`,`a`.`tahun` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-04-19-view-rek-spcs
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-19-view-rek-spcs`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-19-view-rek-spcs` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Keterangan` AS `Keterangan`,`a`.`cJenis` AS `cJenis`,`b`.`id` AS `idsp`,`b`.`Kode` AS `kdsp`,`b`.`Nama` AS `namasp`,`c`.`id` AS `idcs`,`c`.`Kode` AS `kdcs`,`c`.`Nama` AS `namacs` from ((`rekening` `a` left join `supplier` `b` on((`a`.`Kode` = `b`.`NoAcc`))) left join `customer` `c` on((`c`.`NoAcc` = `a`.`Kode`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-04-20-view-rek-vendor
-- ----------------------------
DROP VIEW IF EXISTS `00-00-04-20-view-rek-vendor`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-04-20-view-rek-vendor` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,`a`.`Keterangan` AS `Keterangan`,`a`.`cJenis` AS `cJenis`,if((left(`a`.`Kode`,5) = '1.250'),`a`.`idcs`,if((left(`a`.`Kode`,5) = '2.300'),`a`.`idsp`,NULL)) AS `idvendor`,if((left(`a`.`Kode`,5) = '1.250'),`a`.`kdcs`,if((left(`a`.`Kode`,5) = '2.300'),`a`.`kdsp`,NULL)) AS `kdvendor`,if((left(`a`.`Kode`,5) = '1.250'),`a`.`namacs`,if((left(`a`.`Kode`,5) = '2.300'),`a`.`namasp`,NULL)) AS `namavendor` from `00-00-04-19-view-rek-spcs` `a`;

-- ----------------------------
-- View structure for 00-00-05-00-view-customer-kode
-- ----------------------------
DROP VIEW IF EXISTS `00-00-05-00-view-customer-kode`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-05-00-view-customer-kode` AS select `a`.`id` AS `id`,`a`.`Kode` AS `Kode`,substr(`a`.`Kode`,-(4)) AS `right`,`a`.`Nama` AS `Nama` from `customer` `a`;

-- ----------------------------
-- View structure for 00-00-06-00-view-purchasing
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-00-view-purchasing`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-00-view-purchasing` AS select `a`.`id` AS `id`,`a`.`faktur_pt` AS `faktur_pt`,`a`.`tgl_pt` AS `tgl_pt`,`a`.`id_tipe_beli` AS `id_tipe_beli`,`a`.`id_po` AS `id_po`,`purchase_order`.`faktur_po` AS `faktur_po`,`purchase_order`.`tgl_po` AS `tgl_po` from (`purchase_transaction` `a` left join `purchase_order` on((`purchase_order`.`id` = `a`.`id_po`)));

-- ----------------------------
-- View structure for 00-00-06-00-view-transaksi-beli
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-00-view-transaksi-beli`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-00-view-transaksi-beli` AS select `a`.`id` AS `id`,`a`.`faktur_pt` AS `faktur_pt`,`a`.`tgl_pt` AS `tgl_pt`,`a`.`id_tipe_beli` AS `id_tipe_beli`,`b`.`jenis_beli` AS `jenis_beli`,`a`.`tgl_jatuh_tempo` AS `tgl_jatuh_tempo`,if((((`a`.`id_supplier` > 0) or (`a`.`id_supplier` is not null)) and (`a`.`id_tipe_beli` = 2)),`d`.`Nama`,if((((`a`.`id_customer` > 0) or (`a`.`id_customer` is not null)) and (`a`.`id_customer` = 1)),`e`.`Nama`,'')) AS `Nama`,if(((`a`.`id_supplier` > 0) and (`a`.`id_supplier` is not null)),`d`.`Kode`,if(((`a`.`id_customer` > 0) and (`a`.`id_customer` is not null)),`e`.`Kode`,'')) AS `Kode`,`d`.`id` AS `id_supplier`,`d`.`Kode` AS `kdsp`,`d`.`Nama` AS `namasp`,`a`.`id_customer` AS `id_customer`,`e`.`Nama` AS `namacs`,`e`.`Kode` AS `kdcs`,`a`.`totalbayar` AS `totalbayar`,`a`.`uangmuka` AS `uangmuka`,`a`.`biayakirim` AS `biayakirim`,`a`.`ppn` AS `ppn`,`a`.`grandtotal` AS `grandtotal`,`a`.`sisa` AS `hutang`,`a`.`sisabayar` AS `piutang`,`a`.`diskon` AS `diskon`,`b`.`datetime` AS `datetime`,`a`.`id_supplier` AS `idsp`,`f`.`po` AS `po`,`g`.`tgl_po` AS `tgl_po`,`a`.`isactive` AS `isactive`,`a`.`islocked` AS `islocked` from (((((`purchase_transaction` `a` left join `jenis_pembelian` `b` on((`b`.`id` = `a`.`id_tipe_beli`))) left join `supplier` `d` on((`a`.`id_supplier` = `d`.`id`))) left join `customer` `e` on((`e`.`id` = `a`.`id_customer`))) left join `purchase_transaction_detail` `f` on((`a`.`faktur_pt` = `f`.`pt`))) left join `purchase_order` `g` on((`g`.`faktur_po` = `f`.`po`))) group by `a`.`id` order by `a`.`faktur_pt`;

-- ----------------------------
-- View structure for 00-00-06-00-view-transaksi-beli-backup31102016
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-00-view-transaksi-beli-backup31102016`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-00-view-transaksi-beli-backup31102016` AS select `a`.`id` AS `id`,`a`.`faktur_pt` AS `faktur_pt`,`a`.`tgl_pt` AS `tgl_pt`,`a`.`id_tipe_beli` AS `id_tipe_beli`,`b`.`jenis_beli` AS `jenis_beli`,if((`a`.`id_po` <> 0),`c`.`faktur_po`,'') AS `po`,`c`.`tgl_po` AS `tgl_po`,`a`.`tgl_jatuh_tempo` AS `tgl_jatuh_tempo`,if((((`a`.`id_supplier` > 0) or (`a`.`id_supplier` is not null)) and (`a`.`id_tipe_beli` = 2)),`d`.`Nama`,if((((`a`.`id_customer` > 0) or (`a`.`id_customer` is not null)) and (`a`.`id_customer` = 1)),`e`.`Nama`,'')) AS `Nama`,if(((`a`.`id_supplier` > 0) and (`a`.`id_supplier` is not null)),`d`.`Kode`,if(((`a`.`id_customer` > 0) and (`a`.`id_customer` is not null)),`e`.`Kode`,'')) AS `Kode`,`d`.`id` AS `id_supplier`,`d`.`Kode` AS `kdsp`,`d`.`Nama` AS `namasp`,`a`.`id_customer` AS `id_customer`,`e`.`Nama` AS `namacs`,`e`.`Kode` AS `kdcs`,`a`.`totalbayar` AS `totalbayar`,`a`.`uangmuka` AS `uangmuka`,`a`.`biayakirim` AS `biayakirim`,`a`.`ppn` AS `ppn`,`a`.`grandtotal` AS `grandtotal`,`a`.`sisa` AS `hutang`,`a`.`sisabayar` AS `piutang`,`a`.`diskon` AS `diskon`,`b`.`datetime` AS `datetime`,`a`.`id_supplier` AS `idsp` from ((((`purchase_transaction` `a` left join `jenis_pembelian` `b` on((`b`.`id` = `a`.`id_tipe_beli`))) left join `purchase_order` `c` on((`c`.`id` = `a`.`id_po`))) left join `supplier` `d` on(((`d`.`id` = `c`.`id_supplier`) or (`a`.`id_supplier` = `d`.`id`)))) left join `customer` `e` on((`e`.`id` = `a`.`id_customer`))) order by `a`.`id` desc,`a`.`faktur_pt`;

-- ----------------------------
-- View structure for 00-00-06-00-view-transaksi-beli-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-00-view-transaksi-beli-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-00-view-transaksi-beli-detail` AS select `a`.`id_detail` AS `id_detail`,`x`.`tgl_pt` AS `tgl_pt`,`a`.`po` AS `faktur_po`,`a`.`pt` AS `faktur_pt`,`a`.`id_barang` AS `id_barang`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`jumlah` AS `jumlah`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,`a`.`harga_beli` AS `harga_beli`,(if(((`a`.`harga_beli` <= 0) or isnull(`a`.`harga_beli`)),if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)),`a`.`harga_beli`) * `a`.`jumlah`) AS `subtotal`,if(((`a`.`harga_beli` <= 0) or isnull(`a`.`harga_beli`)),if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)),`a`.`harga_beli`) AS `harga`,`a`.`keterangan` AS `keterangan`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_supplier` AS `idsp`,`f`.`Kode` AS `kdsp`,`f`.`Nama` AS `namasp`,`a`.`id_customer` AS `id_customer`,`a`.`id_customer` AS `idcs`,`e`.`Kode` AS `kdcs`,`e`.`Nama` AS `namacs`,`x`.`totalbayar` AS `totalbayar`,`x`.`uangmuka` AS `uangmuka`,`x`.`biayakirim` AS `biayakirim`,`x`.`grandtotal` AS `grandtotal`,`x`.`sisa` AS `sisa`,`x`.`sisabayar` AS `sisabayar`,`a`.`id_pt` AS `id_pt`,`a`.`id_po` AS `id_po`,`b`.`id` AS `id`,`c`.`Isi2` AS `Isi2`,`c`.`Isi3` AS `Isi3`,`c`.`Satuan1` AS `Satuan1`,`c`.`Satuan2` AS `Satuan2`,`c`.`Satuan3` AS `Satuan3`,if((`a`.`id_satuan` = 1),`a`.`jumlah`,0) AS `jml1`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,0) AS `sat1`,if((`a`.`id_satuan` = 2),`a`.`jumlah`,0) AS `jml2`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,0) AS `sat2`,if((`a`.`id_satuan` = 3),`a`.`jumlah`,0) AS `jml3`,if((`a`.`id_satuan` = 3),`c`.`Satuan3`,0) AS `sat3`,if(((`a`.`id_supplier` > 0) and (`a`.`id_customer` = 0)),`f`.`Kode`,if(((`a`.`id_customer` > 0) and (`a`.`id_supplier` = 0)),`e`.`Kode`,0)) AS `kd`,if(((`a`.`id_supplier` > 0) and (`a`.`id_customer` = 0)),`f`.`Nama`,if(((`a`.`id_customer` > 0) and (`a`.`id_supplier` = 0)),`e`.`Nama`,0)) AS `nm`,`a`.`isactive` AS `isactive`,`a`.`isdelete` AS `isdelete`,`a`.`datedelete` AS `datedelete` from ((((((`purchase_transaction_detail` `a` left join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) left join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) left join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `purchase_transaction` `x` on((`x`.`faktur_pt` = `a`.`pt`))) left join `supplier` `f` on((`a`.`id_supplier` = `f`.`id`))) left join `customer` `e` on((`a`.`id_customer` = `e`.`id`)));

-- ----------------------------
-- View structure for 00-00-06-01-view-trx-beli-detail-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-01-view-trx-beli-detail-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-01-view-trx-beli-detail-total` AS select `a`.`id_pt` AS `id_pt`,`a`.`faktur_pt` AS `faktur_pt`,sum(`a`.`subtotal`) AS `total`,format(sum(`a`.`subtotal`),2) AS `rptotal`,`a`.`faktur_po` AS `faktur_po` from `00-00-06-00-view-transaksi-beli-detail` `a` group by `a`.`faktur_pt`;

-- ----------------------------
-- View structure for 00-00-06-02-view-hutang-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-02-view-hutang-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-02-view-hutang-supplier` AS select `a`.`idsp` AS `idsp`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,sum(`a`.`totalbayar`) AS `totalbayar`,sum(`a`.`uangmuka`) AS `uangmuka`,sum((`a`.`totalbayar` - `a`.`uangmuka`)) AS `sisa_hutang` from `00-00-06-00-view-transaksi-beli` `a` group by `a`.`idsp`,`a`.`Kode` order by `a`.`Kode`;

-- ----------------------------
-- View structure for 00-00-06-03-view-rekam-beli-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-03-view-rekam-beli-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-03-view-rekam-beli-customer` AS select `a`.`id` AS `id`,`a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,`a`.`Kode` AS `Kode`,`a`.`NmBarang` AS `NmBarang`,`a`.`Qty` AS `Qty`,`a`.`Satuan` AS `Satuan`,`a`.`Harga` AS `Harga`,`a`.`Jumlah` AS `Jumlah` from `beli` `a` where ((`a`.`NmBarang` like '%sekem%') or (`a`.`NmBarang` like 'retak') or (`a`.`NmBarang` like 'telur') or (`a`.`NmBarang` like 'jagung') or (`a`.`NmBarang` like 'katul'));

-- ----------------------------
-- View structure for 00-00-06-04-view-per-transaksi-beli
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-04-view-per-transaksi-beli`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-04-view-per-transaksi-beli` AS select `a`.`id` AS `id`,`a`.`faktur_pt` AS `faktur_pt`,date_format(`a`.`tgl_pt`,'%d/%m/%Y') AS `tgl_pt`,`a`.`id_tipe_beli` AS `id_tipe_beli`,`b`.`jenis_beli` AS `jenis_beli`,`a`.`id_supplier` AS `idsp`,`supplier`.`Kode` AS `kdsp`,`supplier`.`Nama` AS `namasp`,`a`.`id_customer` AS `idcs`,`e`.`Kode` AS `kdcs`,`e`.`Nama` AS `namacs`,`xx`.`id` AS `idbrg`,`xx`.`Kode` AS `kdbrg`,`xx`.`Nama` AS `namabrg`,`xx`.`jumlah` AS `jumlah`,`xx`.`satuan` AS `satuan`,`a`.`totalbayar` AS `totalbayar`,`a`.`uangmuka` AS `uangmuka`,`a`.`biayakirim` AS `biayakirim`,`a`.`grandtotal` AS `grandtotal`,`a`.`sisa` AS `sisa`,`a`.`sisabayar` AS `sisabayar`,cast(`xx`.`harga` as decimal(20,2)) AS `harga` from ((((`purchase_transaction` `a` left join `jenis_pembelian` `b` on((`b`.`id` = `a`.`id_tipe_beli`))) left join `supplier` on((`a`.`id_supplier` = `supplier`.`id`))) left join `customer` `e` on((`e`.`id` = `a`.`id_customer`))) left join `00-00-06-00-view-transaksi-beli-detail` `xx` on((`xx`.`faktur_pt` = `a`.`faktur_pt`))) order by `a`.`id`,`a`.`faktur_pt`;

-- ----------------------------
-- View structure for 00-00-06-05-query-pt-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-06-05-query-pt-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-06-05-query-pt-detail` AS select `a`.`id` AS `id`,`a`.`faktur_pt` AS `faktur_pt`,`a`.`tgl_pt` AS `tgl_pt`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_customer` AS `id_customer`,`b`.`id_detail` AS `id_detail`,`b`.`po` AS `po`,`b`.`id_po` AS `id_po`,`b`.`id_barang` AS `id_barang`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`b`.`jumlah` AS `jumlah`,`b`.`id_satuan` AS `id_satuan`,if((`b`.`id_satuan` = 1),`d`.`Satuan1`,if((`b`.`id_satuan` = 2),`d`.`Satuan2`,`d`.`Satuan3`)) AS `satuan`,`b`.`harga_beli` AS `harga_beli`,`d`.`Isi2` AS `Isi2`,`d`.`Isi3` AS `Isi3`,`d`.`Satuan1` AS `Satuan1`,`d`.`Satuan2` AS `Satuan2`,`d`.`Satuan3` AS `Satuan3` from (((`purchase_transaction` `a` join `purchase_transaction_detail` `b` on((`b`.`pt` = `a`.`faktur_pt`))) join `barang_satuan` `d` on((`b`.`id_barang` = `d`.`id_barang`))) join `barang` `c` on((`b`.`id_barang` = `c`.`id`)));

-- ----------------------------
-- View structure for 00-00-07-00-view-purchase-return
-- ----------------------------
DROP VIEW IF EXISTS `00-00-07-00-view-purchase-return`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-07-00-view-purchase-return` AS select `a`.`id` AS `id`,`a`.`faktur_pr` AS `faktur_pr`,`c`.`faktur_pt` AS `pt`,`a`.`tgl_pr` AS `tgl_pr`,if((`a`.`tipe_retur` = 1),'Berdasarkan Supplier','Dari Transaksi Pembelian') AS `tipe_retur`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`totalretur` AS `totalretur`,`a`.`bayar` AS `bayar`,`a`.`biayakirim` AS `biayakirim`,`c`.`ppn` AS `ppn`,`a`.`grandtotal` AS `grandtotal`,`a`.`keterangan` AS `keterangan`,`a`.`id_supplier` AS `id_supplier` from ((`purchase_return` `a` join `supplier` `b` on((`b`.`id` = `a`.`id_supplier`))) left join `purchase_transaction` `c` on((`a`.`id_pt` = `c`.`id`)));

-- ----------------------------
-- View structure for 00-00-07-01-view-detail-return
-- ----------------------------
DROP VIEW IF EXISTS `00-00-07-01-view-detail-return`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-07-01-view-detail-return` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur_pr` AS `faktur_pr`,`a`.`id_pr` AS `id_pr`,`a`.`jumlah` AS `jumlah`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_beli`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah`) AS `subtotal`,`a`.`keterangan` AS `keterangan` from (((`purchase_return_detail` `a` join `barang` `b` on((`b`.`id` = `a`.`id_barang`))) join `barang_satuan` `c` on((`c`.`id_barang` = `a`.`id_barang`))) join `barang_harga` `d` on((`d`.`id_barang` = `a`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-08-01-view-kas-masuk-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-08-01-view-kas-masuk-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-08-01-view-kas-masuk-total` AS select `a`.`id_kas_masuk` AS `id_kas_masuk`,`a`.`faktur_kas` AS `faktur_kas`,sum(`a`.`nominal`) AS `kas_total` from `kas_masuk_detail` `a` group by `a`.`faktur_kas`,`a`.`id_kas_masuk`;

-- ----------------------------
-- View structure for 00-00-08-02-view-kas-keluar-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-08-02-view-kas-keluar-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-08-02-view-kas-keluar-total` AS select `a`.`id_kas_keluar` AS `id_kas_keluar`,`a`.`faktur_kas` AS `faktur_kas`,sum(`a`.`nominal`) AS `kas_total` from `kas_keluar_detail` `a` group by `a`.`faktur_kas`,`a`.`id_kas_keluar`;

-- ----------------------------
-- View structure for 00-00-08-03-view-kas-masuk
-- ----------------------------
DROP VIEW IF EXISTS `00-00-08-03-view-kas-masuk`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-08-03-view-kas-masuk` AS select `a`.`id` AS `id`,`a`.`faktur_kas` AS `faktur_kas`,`a`.`tgl_kas` AS `tgl_kas`,`a`.`akun_kas` AS `akun_kas`,`a`.`nominal` AS `nominal`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`b`.`Alamat` AS `Alamat`,`b`.`Wilayah` AS `Wilayah`,`a`.`keterangan` AS `keterangan` from (`kas_masuk` `a` join `customer` `b` on((`b`.`id` = `a`.`id_customer`)));

-- ----------------------------
-- View structure for 00-00-08-04-view-kas-keluar
-- ----------------------------
DROP VIEW IF EXISTS `00-00-08-04-view-kas-keluar`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-08-04-view-kas-keluar` AS select `a`.`id` AS `id`,`a`.`faktur_kas` AS `faktur_kas`,`a`.`tgl_kas` AS `tgl_kas`,`a`.`akun_kas` AS `akun_kas`,`a`.`nominal` AS `nominal`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`b`.`Alamat` AS `Alamat`,`a`.`keterangan` AS `keterangan` from (`kas_keluar` `a` join `supplier` `b` on((`b`.`id` = `a`.`id_supplier`)));

-- ----------------------------
-- View structure for 00-00-09-01-view-bank-kode
-- ----------------------------
DROP VIEW IF EXISTS `00-00-09-01-view-bank-kode`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-09-01-view-bank-kode` AS select `a`.`id` AS `id`,`a`.`bukti_bank` AS `bukti_bank`,`a`.`tipe_trx` AS `tipe_trx`,substr(`a`.`bukti_bank`,1,2) AS `first` from `bank` `a`;

-- ----------------------------
-- View structure for 00-00-09-02-view-total-detail-bank
-- ----------------------------
DROP VIEW IF EXISTS `00-00-09-02-view-total-detail-bank`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-09-02-view-total-detail-bank` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_trx_bank` AS `id_trx_bank`,`a`.`bukti_bank` AS `bukti_bank`,sum(`a`.`nominal_detail`) AS `total_nominal` from `bank_detail` `a` group by `a`.`bukti_bank`,`a`.`id_trx_bank`;

-- ----------------------------
-- View structure for 00-00-09-03-view-bank
-- ----------------------------
DROP VIEW IF EXISTS `00-00-09-03-view-bank`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-09-03-view-bank` AS select `a`.`id` AS `id`,`a`.`bukti_bank` AS `bukti_bank`,`a`.`tipe_trx` AS `tipe_trx`,`a`.`akun_bank` AS `akun_bank`,`a`.`tgl_bank` AS `tgl_bank`,`a`.`keterangan` AS `keterangan`,`a`.`total_bank` AS `total_bank`,`a`.`total_giro` AS `total_giro`,`a`.`ref` AS `ref`,`b`.`Kode` AS `kode_bank`,`b`.`Rekening` AS `Rekening`,`b`.`Keterangan` AS `ket_rek`,concat(`c`.`Nama`,' (',`c`.`Kode`,')') AS `supplier`,concat(`d`.`Nama`,' (',`d`.`Kode`,')') AS `customer` from (((`banks` `b` join `bank` `a` on((`b`.`id` = `a`.`id_bank`))) left join `supplier` `c` on((`c`.`id` = `a`.`id_supplier`))) left join `customer` `d` on((`d`.`id` = `a`.`id_customer`)));

-- ----------------------------
-- View structure for 00-00-09-04-view-total-tt-giro
-- ----------------------------
DROP VIEW IF EXISTS `00-00-09-04-view-total-tt-giro`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-09-04-view-total-tt-giro` AS select `a`.`id` AS `id`,`a`.`id_trx_bank` AS `id_trx_bank`,`a`.`bukti_bank` AS `bukti_bank`,sum(`a`.`nominal`) AS `total_tt_giro` from `bank_giro` `a` group by `a`.`bukti_bank`,`a`.`id_trx_bank` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-10-01-view-bukti-bank-masuk
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-01-view-bukti-bank-masuk`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-01-view-bukti-bank-masuk` AS select `a`.`id` AS `id`,`a`.`bukti_bank` AS `bukti`,`a`.`akun_bank` AS `akun`,`a`.`tipe_trx` AS `tipe_trx` from `bank` `a` where (`a`.`tipe_trx` = 'D');

-- ----------------------------
-- View structure for 00-00-10-02-view-bukti-bank-keluar
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-02-view-bukti-bank-keluar`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-02-view-bukti-bank-keluar` AS select `a`.`id` AS `id`,`a`.`bukti_bank` AS `bukti`,`a`.`akun_bank` AS `akun`,`a`.`tipe_trx` AS `tipe_trx` from `bank` `a` where (`a`.`tipe_trx` = 'K');

-- ----------------------------
-- View structure for 00-00-10-03-view-bukti-kas-masuk
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-03-view-bukti-kas-masuk`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-03-view-bukti-kas-masuk` AS select `a`.`id` AS `id`,`a`.`faktur_kas` AS `bukti`,`a`.`akun_kas` AS `akun` from `kas_masuk` `a`;

-- ----------------------------
-- View structure for 00-00-10-04-view-bukti-kas-keluar
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-04-view-bukti-kas-keluar`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-04-view-bukti-kas-keluar` AS select `a`.`id` AS `id`,`a`.`faktur_kas` AS `bukti`,`a`.`akun_kas` AS `akun` from `kas_keluar` `a`;

-- ----------------------------
-- View structure for 00-00-10-05-view-bukti-pt
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-05-view-bukti-pt`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-05-view-bukti-pt` AS select `a`.`id` AS `id`,`a`.`faktur_pt` AS `bukti`,`a`.`akun_hutang` AS `akun` from `purchase_transaction` `a`;

-- ----------------------------
-- View structure for 00-00-10-06-view-bukti-purchase-return
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-06-view-bukti-purchase-return`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-06-view-bukti-purchase-return` AS select `a`.`id` AS `id`,`a`.`faktur_pr` AS `bukti`,`a`.`akun_hutang` AS `akun` from `purchase_return` `a`;

-- ----------------------------
-- View structure for 00-00-10-07-view-bukti-sales-trx
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-07-view-bukti-sales-trx`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-07-view-bukti-sales-trx` AS select `a`.`id` AS `id`,`a`.`faktur` AS `bukti`,`a`.`akun_piutang` AS `akun` from `sales_trx` `a`;

-- ----------------------------
-- View structure for 00-00-10-08-view-bukti-sales-return
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-08-view-bukti-sales-return`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-08-view-bukti-sales-return` AS select `a`.`id` AS `id`,`a`.`faktur_sr` AS `bukti`,`a`.`akun_piutang` AS `akun` from `sales_return` `a`;

-- ----------------------------
-- View structure for 00-00-10-09-view-bukti-recording-ayam
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-09-view-bukti-recording-ayam`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-09-view-bukti-recording-ayam` AS select `a`.`id` AS `id`,`a`.`faktur` AS `bukti`,`a`.`akun_perkiraan` AS `akun` from `recording_ayam` `a`;

-- ----------------------------
-- View structure for 00-00-10-10-view-bukti-recording-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-10-view-bukti-recording-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-10-view-bukti-recording-telur` AS select `a`.`id` AS `id`,`a`.`faktur` AS `bukti`,`a`.`akun_perkiraan` AS `akun` from `recording_telur` `a`;

-- ----------------------------
-- View structure for 00-00-10-11-view-bukti-recording-pakan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-11-view-bukti-recording-pakan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-11-view-bukti-recording-pakan` AS select `a`.`id` AS `id`,`a`.`faktur` AS `bukti`,`a`.`akun_perkiraan` AS `akun` from `recording_pakan` `a`;

-- ----------------------------
-- View structure for 00-00-10-12-view-bukti-recording-medis
-- ----------------------------
DROP VIEW IF EXISTS `00-00-10-12-view-bukti-recording-medis`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-10-12-view-bukti-recording-medis` AS select `a`.`id` AS `id`,`a`.`faktur` AS `bukti`,`a`.`akun_perkiraan` AS `akun` from `recording_medis` `a`;

-- ----------------------------
-- View structure for 00-00-11-01-view-jurnal-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-11-01-view-jurnal-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-11-01-view-jurnal-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_jurnal` AS `id_jurnal`,`a`.`no_jurnal` AS `no_jurnal`,`a`.`akun_detail` AS `akun_detail`,`a`.`ket_detail` AS `ket_detail`,if((`a`.`tipe_detail` = 'D'),`a`.`nilai`,0) AS `debet`,if((`a`.`tipe_detail` = 'K'),`a`.`nilai`,0) AS `kredit` from `jurnal_detail` `a`;

-- ----------------------------
-- View structure for 00-00-11-02-view-total-jurnal
-- ----------------------------
DROP VIEW IF EXISTS `00-00-11-02-view-total-jurnal`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-11-02-view-total-jurnal` AS select `a`.`id_jurnal` AS `id_jurnal`,`a`.`no_jurnal` AS `no_jurnal`,sum(`a`.`debet`) AS `totdebet`,sum(`a`.`kredit`) AS `totkredit` from `00-00-11-01-view-jurnal-detail` `a` group by `a`.`no_jurnal`;

-- ----------------------------
-- View structure for 00-00-12-00-view-rekam-ayam
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-00-view-rekam-ayam`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-00-view-rekam-ayam` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`id_kandang` AS `id_kandang`,`b`.`Kode` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`b`.`Gudang` AS `gudang`,`c`.`nama` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`b`.`Mitra` AS `mitra`,`b`.`NmMitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`d`.`nama` AS `recording`,`a`.`total` AS `total`,`a`.`jumlah` AS `jumlah`,date_format(`a`.`tanggal`,'%Y-%m') AS `tahunbulan`,substr(`a`.`faktur`,1,3) AS `kodefaktur`,substr(`a`.`faktur`,4,4) AS `blnthnfaktur`,year(`a`.`tanggal`) AS `tahun`,week(`a`.`tanggal`,3) AS `minggu`,`a`.`keterangan` AS `keterangan`,`a`.`datetime` AS `datetime` from (((`recording_ayam` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`))) left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`))) join `jenis_recording` `d` on((`a`.`id_recording` = `d`.`id`)));

-- ----------------------------
-- View structure for 00-00-12-01-view-rekam-ayam
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-01-view-rekam-ayam`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-01-view-rekam-ayam` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_recording_ayam` AS `id_recording_ayam`,`a`.`faktur_recording` AS `faktur`,`b`.`Kode` AS `Kode`,`a`.`usia` AS `usia`,`b`.`Nama` AS `Nama`,`a`.`jumlah_satuan` AS `jumlah_satuan`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah_satuan`) AS `subtotal`,`a`.`keterangan` AS `keterangan` from ((((`recording_ayam_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `recording_ayam` `x` on(((`x`.`id` = `a`.`id_recording_ayam`) or (`x`.`faktur` = `a`.`faktur_recording`)))) group by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-12-01-view-rekam-ayam-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-01-view-rekam-ayam-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-01-view-rekam-ayam-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_recording_ayam` AS `id_recording_ayam`,`a`.`faktur_recording` AS `faktur`,`a`.`faktur_reff` AS `faktur_reff`,`b`.`Kode` AS `Kode`,`a`.`usia` AS `usia`,`b`.`Nama` AS `Nama`,`a`.`jumlah_satuan` AS `jumlah_satuan`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah_satuan`) AS `subtotal`,`a`.`keterangan` AS `keterangan` from ((((`recording_ayam_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `recording_ayam` `x` on(((`x`.`id` = `a`.`id_recording_ayam`) or (`x`.`faktur` = `a`.`faktur_recording`)))) group by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-12-02-view-rekam-ayam-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-02-view-rekam-ayam-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-02-view-rekam-ayam-total` AS select `a`.`id_recording_ayam` AS `id_recording_ayam`,`a`.`faktur` AS `faktur`,sum(`a`.`subtotal`) AS `total`,`a`.`jumlah_satuan` AS `jumlah`,if((`a`.`id_satuan` = 1),`b`.`Satuan1`,`b`.`Satuan2`) AS `satuan`,if(((`a`.`id_satuan` = 1) and (`b`.`Isi2` > 0)),(`a`.`jumlah_satuan` * 100),`a`.`jumlah_satuan`) AS `jmlekor`,`b`.`Satuan1` AS `Satuan1`,`b`.`Satuan2` AS `Satuan2`,`b`.`Satuan3` AS `Satuan3`,`b`.`Isi2` AS `Isi2`,`b`.`Isi3` AS `Isi3` from (`00-00-12-01-view-rekam-ayam-detail` `a` join `barang_satuan` `b` on((`b`.`kode` = `a`.`Kode`))) group by `a`.`faktur` order by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-12-03-view-rekam-ayam-mati-kartustok
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-03-view-rekam-ayam-mati-kartustok`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-03-view-rekam-ayam-mati-kartustok` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`kredit` AS `kredit`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,`b`.`tanggal` AS `tgl_fak`,`b`.`id_kandang` AS `id_kandang`,`b`.`id_gudang` AS `id_gudang`,`b`.`id_mitra` AS `id_mitra`,`b`.`id_recording` AS `id_recording` from (`kartustok` `a` left join `recording_ayam` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where ((left(`a`.`faktur_ref`,3) = 'RAM') and (`a`.`tipe` = 'K') and (`a`.`tipe_kartustok` = 'Ayam'));

-- ----------------------------
-- View structure for 00-00-12-04-view-rekam-ayam-afkir-kartustok
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-04-view-rekam-ayam-afkir-kartustok`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-04-view-rekam-ayam-afkir-kartustok` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`kredit` AS `kredit`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,`b`.`tanggal` AS `tgl_fak`,`b`.`id_kandang` AS `id_kandang`,`b`.`id_gudang` AS `id_gudang`,`b`.`id_mitra` AS `id_mitra`,`b`.`id_recording` AS `id_recording` from (`kartustok` `a` left join `recording_ayam` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where ((left(`a`.`faktur_ref`,3) = 'RAA') and (`a`.`tipe` = 'K') and (`a`.`tipe_kartustok` = 'Ayam'));

-- ----------------------------
-- View structure for 00-00-12-05-view-rekap-ayam-perkandang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-05-view-rekap-ayam-perkandang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-05-view-rekap-ayam-perkandang` AS select `a`.`kandang` AS `kandang`,sum(if((substr(`a`.`faktur`,3,1) = 'I'),`a`.`jumlah`,0)) AS `isi`,sum(if((substr(`a`.`faktur`,3,1) = 'T'),`a`.`jumlah`,0)) AS `tambah`,sum(if((substr(`a`.`faktur`,3,1) = 'A'),`a`.`jumlah`,0)) AS `afkir`,sum(if((substr(`a`.`faktur`,3,1) = 'M'),`a`.`jumlah`,0)) AS `mati`,sum(if((substr(`a`.`faktur`,3,1) = 'K'),`a`.`jumlah`,0)) AS `kosong` from `00-00-12-00-view-rekam-ayam` `a` where (`a`.`kandang` is not null) group by `a`.`kandang`;

-- ----------------------------
-- View structure for 00-00-12-06-view-rekam-ayam-isi-kartustok
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-06-view-rekam-ayam-isi-kartustok`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-06-view-rekam-ayam-isi-kartustok` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`debet` AS `debet`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,`b`.`tanggal` AS `tgl_fak`,`b`.`id_kandang` AS `id_kandang`,`b`.`id_gudang` AS `id_gudang`,`b`.`id_mitra` AS `id_mitra`,`b`.`id_recording` AS `id_recording` from (`kartustok` `a` left join `recording_ayam` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where ((left(`a`.`faktur_ref`,3) = 'RAI') and (`a`.`tipe` = 'D') and (`a`.`tipe_kartustok` = 'Ayam'));

-- ----------------------------
-- View structure for 00-00-12-07-view-rekam-ayam-tambah-kartustok
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-07-view-rekam-ayam-tambah-kartustok`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-07-view-rekam-ayam-tambah-kartustok` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`debet` AS `debet`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,`b`.`tanggal` AS `tgl_fak`,`b`.`id_kandang` AS `id_kandang`,`b`.`id_gudang` AS `id_gudang`,`b`.`id_mitra` AS `id_mitra`,`b`.`id_recording` AS `id_recording` from (`kartustok` `a` left join `recording_ayam` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where ((left(`a`.`faktur_ref`,3) = 'RAT') and (`a`.`tipe` = 'D') and (`a`.`tipe_kartustok` = 'Ayam'));

-- ----------------------------
-- View structure for 00-00-12-08-view-rekam-ayam-kosong-kartustok
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-08-view-rekam-ayam-kosong-kartustok`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-08-view-rekam-ayam-kosong-kartustok` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`kredit` AS `kredit`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,`b`.`tanggal` AS `tgl_fak`,`b`.`id_kandang` AS `id_kandang`,`b`.`id_gudang` AS `id_gudang`,`b`.`id_mitra` AS `id_mitra`,`b`.`id_recording` AS `id_recording` from (`kartustok` `a` left join `recording_ayam` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where ((left(`a`.`faktur_ref`,3) = 'RAK') and (`a`.`tipe` = 'K') and (`a`.`tipe_kartustok` = 'Ayam') and (`b`.`id_kandang` is not null) and (`b`.`id_mitra` is not null));

-- ----------------------------
-- View structure for 00-00-12-10-view-rekam-ayam-is-lama
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-10-view-rekam-ayam-is-lama`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-10-view-rekam-ayam-is-lama` AS select `a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,week(`a`.`Tgl`,0) AS `weekyear`,`a`.`Kandang` AS `Kandang`,`a`.`Kandang` AS `kd_kandang`,`a`.`NmKandang` AS `NmKandang`,`a`.`Qty` AS `Qty`,`a`.`QtyReal` AS `QtyReal`,`a`.`Customer` AS `kd_mitra`,`a`.`NmCustomer` AS `nm_mitra`,`a`.`Satuan` AS `Satuan`,`a`.`Umur` AS `Umur`,`a`.`Kode` AS `KodeBarang`,`a`.`Nmbarang` AS `Nmbarang` from `isilayer` `a` where (left(`a`.`Faktur`,2) = 'IS') order by `a`.`Tgl`;

-- ----------------------------
-- View structure for 00-00-12-10-view-rekam-ayam-mtaf-lama
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-10-view-rekam-ayam-mtaf-lama`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-10-view-rekam-ayam-mtaf-lama` AS select `a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,week(`a`.`Tgl`,3) AS `weeks`,sum(`a`.`Qty`) AS `total`,sum(`a`.`QtyReal`) AS `qtyasli`,`a`.`Umur` AS `Umur`,`a`.`Customer` AS `Customer`,`a`.`NmCustomer` AS `NmCustomer`,`a`.`Kandang` AS `Kandang`,`a`.`NmKandang` AS `NmKandang`,`a`.`Kode` AS `Kode`,`a`.`Nmbarang` AS `Nmbarang`,`a`.`Satuan` AS `Satuan`,`a`.`User` AS `User`,`a`.`Time` AS `Time` from `isilayer` `a` where ((left(`a`.`Faktur`,2) = 'AM') or (left(`a`.`Faktur`,2) = 'PJ')) group by `a`.`Kandang`,`a`.`Tgl` order by `a`.`Tgl`;

-- ----------------------------
-- View structure for 00-00-12-10-view-rekam-ayam-ta-lama
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-10-view-rekam-ayam-ta-lama`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-10-view-rekam-ayam-ta-lama` AS select `a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,week(`a`.`Tgl`,3) AS `weeks`,year(`a`.`Tgl`) AS `yearnum`,sum(`a`.`Qty`) AS `total`,sum(`a`.`QtyReal`) AS `qtyasli`,`a`.`Customer` AS `Customer`,`a`.`NmCustomer` AS `NmCustomer`,`a`.`Kandang` AS `Kandang`,`a`.`NmKandang` AS `NmKandang`,`a`.`Kode` AS `Kode`,`a`.`Nmbarang` AS `Nmbarang`,`a`.`Satuan` AS `Satuan`,`a`.`Umur` AS `Umur`,`a`.`User` AS `User`,`a`.`Time` AS `Time` from `isilayer` `a` where (left(`a`.`Faktur`,2) = 'TA') group by `a`.`Kandang`,`a`.`Faktur` order by `a`.`Tgl`;

-- ----------------------------
-- View structure for 00-00-12-12-view-rekam-ayam-lama
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-12-view-rekam-ayam-lama`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-12-view-rekam-ayam-lama` AS select `a`.`Kandang` AS `Kandang`,`a`.`Kandang` AS `kd_kandang`,`a`.`Tgl` AS `Tgl`,week(`a`.`Tgl`,0) AS `weekyear`,`a`.`NmKandang` AS `NmKandang`,`a`.`Customer` AS `kd_mitra`,`a`.`NmCustomer` AS `nm_mitra`,sum(if((left(`a`.`Faktur`,2) = 'IS'),`a`.`Qty`,0)) AS `isi`,sum(if((left(`a`.`Faktur`,2) = 'TA'),`a`.`Qty`,0)) AS `tambah`,sum(if((left(`a`.`Faktur`,2) = 'AM'),`a`.`Qty`,0)) AS `mati`,sum(if((left(`a`.`Faktur`,2) = 'PJ'),`a`.`Qty`,0)) AS `afkir`,`a`.`Satuan` AS `Satuan`,`a`.`Umur` AS `Umur`,`a`.`Kode` AS `KodeBarang`,`a`.`Nmbarang` AS `Nmbarang`,`a`.`User` AS `User`,`a`.`Time` AS `Time` from `isilayer` `a` group by `a`.`Kandang`,`a`.`Tgl` order by `a`.`Tgl`;

-- ----------------------------
-- View structure for 00-00-12-15-view-rekam-ayam-baru
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-15-view-rekam-ayam-baru`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-15-view-rekam-ayam-baru` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`id_kandang` AS `id_kandang`,`a`.`kandang` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`gudang` AS `gudang`,`a`.`namagudang` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`a`.`recording` AS `recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`tahun` AS `tahun`,`a`.`minggu` AS `weekyear`,`b`.`Kode` AS `kdbarang`,`b`.`Nama` AS `nama`,`b`.`jumlah_satuan` AS `jumlah`,`b`.`usia` AS `usia`,floor((`b`.`usia` / 7)) AS `weekolds`,(`b`.`usia` % 7) AS `weeksdays`,`a`.`datetime` AS `datetime` from (`00-00-12-00-view-rekam-ayam` `a` left join `00-00-12-01-view-rekam-ayam-detail` `b` on((`a`.`faktur` = `b`.`faktur`)));

-- ----------------------------
-- View structure for 00-00-12-16-view-rekam-isi-ayam-baru
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-16-view-rekam-isi-ayam-baru`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-16-view-rekam-isi-ayam-baru` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`tanggal` AS `Tgl`,`a`.`id_kandang` AS `id_kandang`,`a`.`kandang` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`gudang` AS `gudang`,`a`.`namagudang` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`a`.`recording` AS `recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`tahun` AS `tahun`,`a`.`weekyear` AS `weekyear`,`a`.`kdbarang` AS `kdbarang`,`a`.`nama` AS `nama`,`a`.`jumlah` AS `jumlah`,`a`.`jumlah` AS `Qty`,`a`.`usia` AS `usia`,`a`.`usia` AS `Umur`,`a`.`weekolds` AS `weekolds`,`a`.`weeksdays` AS `weeksdays`,date_format(`a`.`datetime`,'%Y-%m-%d') AS `datestamp` from `00-00-12-15-view-rekam-ayam-baru` `a` where (`a`.`kodefaktur` = 'RAI');

-- ----------------------------
-- View structure for 00-00-12-17-view-rekam-tambah-ayam-baru
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-17-view-rekam-tambah-ayam-baru`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-17-view-rekam-tambah-ayam-baru` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`id_kandang` AS `id_kandang`,`a`.`kandang` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`gudang` AS `gudang`,`a`.`namagudang` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`a`.`recording` AS `recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`tahun` AS `tahun`,`a`.`weekyear` AS `weekyear`,`a`.`kdbarang` AS `kdbarang`,`a`.`nama` AS `nama`,`a`.`jumlah` AS `jumlah`,`a`.`usia` AS `usia`,`a`.`weekolds` AS `weekolds`,`a`.`weeksdays` AS `weeksdays`,date_format(`a`.`datetime`,'%Y-%m-%d') AS `datestamp` from `00-00-12-15-view-rekam-ayam-baru` `a` where (`a`.`kodefaktur` = 'RAT');

-- ----------------------------
-- View structure for 00-00-12-18-view-rekam-ayam-mati-baru
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-18-view-rekam-ayam-mati-baru`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-18-view-rekam-ayam-mati-baru` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`id_kandang` AS `id_kandang`,`a`.`kandang` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`gudang` AS `gudang`,`a`.`namagudang` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`a`.`recording` AS `recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`tahun` AS `tahun`,`a`.`weekyear` AS `weekyear`,`a`.`kdbarang` AS `kdbarang`,`a`.`nama` AS `nama`,`a`.`jumlah` AS `jumlah`,`a`.`usia` AS `usia`,`a`.`weekolds` AS `weekolds`,`a`.`weeksdays` AS `weeksdays`,date_format(`a`.`datetime`,'%Y-%m-%d') AS `datestamp` from `00-00-12-15-view-rekam-ayam-baru` `a` where (`a`.`kodefaktur` = 'RAM');

-- ----------------------------
-- View structure for 00-00-12-19-view-rekam-ayam-afkir
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-19-view-rekam-ayam-afkir`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-19-view-rekam-ayam-afkir` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`id_kandang` AS `id_kandang`,`a`.`kandang` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`gudang` AS `gudang`,`a`.`namagudang` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`a`.`recording` AS `recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`tahun` AS `tahun`,`a`.`weekyear` AS `weekyear`,`a`.`kdbarang` AS `kdbarang`,`a`.`nama` AS `nama`,`a`.`jumlah` AS `jumlah`,`a`.`usia` AS `usia`,`a`.`weekolds` AS `weekolds`,`a`.`weeksdays` AS `weeksdays` from `00-00-12-15-view-rekam-ayam-baru` `a` where (`a`.`kodefaktur` = 'RAA');

-- ----------------------------
-- View structure for 00-00-12-20-view-rekam-ayam-kosong
-- ----------------------------
DROP VIEW IF EXISTS `00-00-12-20-view-rekam-ayam-kosong`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-12-20-view-rekam-ayam-kosong` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`id_kandang` AS `id_kandang`,`a`.`kandang` AS `kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`gudang` AS `gudang`,`a`.`namagudang` AS `namagudang`,`a`.`id_mitra` AS `id_mitra`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`id_recording` AS `id_recording`,`a`.`recording` AS `recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`tahun` AS `tahun`,`a`.`weekyear` AS `weekyear`,`a`.`kdbarang` AS `kdbarang`,`a`.`nama` AS `nama`,`a`.`jumlah` AS `jumlah`,`a`.`usia` AS `usia`,`a`.`weekolds` AS `weekolds`,`a`.`weeksdays` AS `weeksdays` from `00-00-12-15-view-rekam-ayam-baru` `a` where (`a`.`kodefaktur` = 'RAK');

-- ----------------------------
-- View structure for 00-00-13-00-view-rekam-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-00-view-rekam-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-00-view-rekam-telur` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`reff` AS `faktur_reff`,`a`.`tanggal` AS `tanggal`,`b`.`Kode` AS `kandang`,`b`.`Gudang` AS `gudang`,`c`.`nama` AS `namagudang`,`b`.`Mitra` AS `mitra`,`b`.`NmMitra` AS `namamitra`,`d`.`nama` AS `recording`,`a`.`jumlah` AS `jumlah`,`a`.`total` AS `total`,`a`.`id_kandang` AS `id_kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`id_recording` AS `id_recording`,`a`.`id_mitra` AS `id_mitra`,date_format(`a`.`tanggal`,'%Y-%m') AS `tahunbulan`,substr(`a`.`faktur`,1,3) AS `kodefaktur`,substr(`a`.`faktur`,4,4) AS `blnthnfaktur`,`a`.`keterangan` AS `keterangan`,`a`.`total_butir` AS `total_butir`,`a`.`total_kg` AS `total_kg` from (((`recording_telur` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`))) left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`))) join `jenis_recording` `d` on((`a`.`id_recording` = `d`.`id`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-13-01-view-rekam-telur-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-01-view-rekam-telur-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-01-view-rekam-telur-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_recording_telur` AS `id_recording_telur`,`a`.`faktur_recording` AS `faktur`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`jumlah_butir` AS `jumlah_butir`,`a`.`jumlah_total` AS `jumlah_total`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah_total`) AS `subtotal`,`a`.`keterangan` AS `keterangan` from ((((`recording_telur_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `recording_telur` `x` on(((`x`.`id` = `a`.`id_recording_telur`) or (`x`.`faktur` = `a`.`faktur_recording`)))) group by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-13-02-view-rekam-telur-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-02-view-rekam-telur-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-02-view-rekam-telur-total` AS select `a`.`id_recording_telur` AS `id_recording_telur`,`a`.`faktur` AS `faktur`,`a`.`harga_satuan` AS `harga_satuan`,`a`.`jumlah_total` AS `jumlah`,`a`.`jumlah_butir` AS `butir`,sum(`a`.`subtotal`) AS `total` from `00-00-13-01-view-rekam-telur-detail` `a` group by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-13-03-view-rekap-hasil-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-03-view-rekap-hasil-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-03-view-rekap-hasil-telur` AS select `a`.`faktur` AS `faktur`,sum(`a`.`jumlah_total`) AS `jml`,sum(`a`.`subtotal`) AS `total`,`a`.`satuan` AS `satuan` from `00-00-13-01-view-rekam-telur-detail` `a` where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG'));

-- ----------------------------
-- View structure for 00-00-13-04-view-rekap-stok-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-04-view-rekap-stok-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-04-view-rekap-stok-telur` AS select sum(if(((`a`.`Nama` = 'Telur') or (`a`.`Kode` = '010001')),`a`.`jumlah_total`,0)) AS `Telur`,sum(if(((`a`.`Nama` = 'Retak') or (`a`.`Kode` = '010002')),`a`.`jumlah_total`,0)) AS `Retak`,sum(`a`.`jumlah_total`) AS `jml`,sum(`a`.`subtotal`) AS `total`,`a`.`satuan` AS `satuan` from `00-00-13-01-view-rekam-telur-detail` `a` where ((substr(`a`.`faktur`,1,3) = 'RTS') and (`a`.`satuan` = 'KG'));

-- ----------------------------
-- View structure for 00-00-13-05-view-rekap-rusak-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-05-view-rekap-rusak-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-05-view-rekap-rusak-telur` AS select `a`.`faktur` AS `faktur`,sum(`a`.`jumlah_total`) AS `jml`,sum(`a`.`subtotal`) AS `total`,`a`.`satuan` AS `satuan` from `00-00-13-01-view-rekam-telur-detail` `a` where ((substr(`a`.`faktur`,1,3) = 'RTR') and (`a`.`satuan` = 'KG'));

-- ----------------------------
-- View structure for 00-00-13-06-view-rekap-pakai-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-06-view-rekap-pakai-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-06-view-rekap-pakai-telur` AS select `a`.`faktur` AS `faktur`,sum(`a`.`jumlah_total`) AS `jml`,sum(`a`.`subtotal`) AS `total`,`a`.`satuan` AS `satuan` from `00-00-13-01-view-rekam-telur-detail` `a` where ((substr(`a`.`faktur`,1,3) = 'RTP') and (`a`.`satuan` = 'KG'));

-- ----------------------------
-- View structure for 00-00-13-07-view-rekap-hasil-harian
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-07-view-rekap-hasil-harian`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-07-view-rekap-hasil-harian` AS select `b`.`tanggal` AS `tanggal`,month(`b`.`tanggal`) AS `bulan`,year(`b`.`tanggal`) AS `tahun`,sum(`a`.`jumlah_total`) AS `jml`,`a`.`satuan` AS `satuan`,sum(`a`.`subtotal`) AS `total` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG')) group by `b`.`tanggal`;

-- ----------------------------
-- View structure for 00-00-13-08-view-rekap-hasil-telur-perbulan-kandang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-08-view-rekap-hasil-telur-perbulan-kandang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-08-view-rekap-hasil-telur-perbulan-kandang` AS select `b`.`tanggal` AS `tanggal`,month(`b`.`tanggal`) AS `bulan`,year(`b`.`tanggal`) AS `tahun`,sum(`a`.`jumlah_total`) AS `jml`,`a`.`satuan` AS `satuan`,sum(`a`.`subtotal`) AS `total`,`b`.`kandang` AS `kandang`,`b`.`gudang` AS `gudang`,`b`.`namagudang` AS `namagudang` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG')) group by month(`b`.`tanggal`),`b`.`kandang` order by `b`.`tanggal`,`b`.`kandang`;

-- ----------------------------
-- View structure for 00-00-13-09-view-rekap-hasil-bulanan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-09-view-rekap-hasil-bulanan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-09-view-rekap-hasil-bulanan` AS select year(`b`.`tanggal`) AS `tahun`,month(`b`.`tanggal`) AS `bulan`,date_format(`b`.`tanggal`,'%Y-%m') AS `bulanan`,sum(`a`.`jumlah_total`) AS `hasil`,`a`.`satuan` AS `satuan`,sum(`a`.`subtotal`) AS `total` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG') and (year(`b`.`tanggal`) = year(now()))) group by year(`b`.`tanggal`),month(`b`.`tanggal`);

-- ----------------------------
-- View structure for 00-00-13-10-view-matriks-hasil-bulanan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-10-view-matriks-hasil-bulanan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-10-view-matriks-hasil-bulanan` AS select year(`b`.`tanggal`) AS `tahun`,sum(if((month(`b`.`tanggal`) = 1),`a`.`jumlah_total`,0)) AS `jan`,sum(if((month(`b`.`tanggal`) = 2),`a`.`jumlah_total`,0)) AS `feb`,sum(if((month(`b`.`tanggal`) = 3),`a`.`jumlah_total`,0)) AS `mar`,sum(if((month(`b`.`tanggal`) = 4),`a`.`jumlah_total`,0)) AS `apr`,sum(if((month(`b`.`tanggal`) = 5),`a`.`jumlah_total`,0)) AS `mei`,sum(if((month(`b`.`tanggal`) = 6),`a`.`jumlah_total`,0)) AS `jun`,sum(if((month(`b`.`tanggal`) = 7),`a`.`jumlah_total`,0)) AS `jul`,sum(if((month(`b`.`tanggal`) = 8),`a`.`jumlah_total`,0)) AS `ags`,sum(if((month(`b`.`tanggal`) = 9),`a`.`jumlah_total`,0)) AS `sep`,sum(if((month(`b`.`tanggal`) = 10),`a`.`jumlah_total`,0)) AS `okt`,sum(if((month(`b`.`tanggal`) = 11),`a`.`jumlah_total`,0)) AS `nov`,sum(if((month(`b`.`tanggal`) = 12),`a`.`jumlah_total`,0)) AS `des` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG')) group by year(`b`.`tanggal`);

-- ----------------------------
-- View structure for 00-00-13-11-view-rekap-stok-bulanan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-11-view-rekap-stok-bulanan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-11-view-rekap-stok-bulanan` AS select year(`b`.`tanggal`) AS `tahun`,month(`b`.`tanggal`) AS `bulan`,date_format(`b`.`tanggal`,'%Y-%m') AS `bulanan`,sum(`a`.`jumlah_total`) AS `hasil`,`a`.`satuan` AS `satuan`,sum(`a`.`subtotal`) AS `total`,sum(if((`a`.`Kode` = '010001'),`a`.`jumlah_total`,0)) AS `telur`,sum(if((`a`.`Kode` = '010002'),`a`.`jumlah_total`,0)) AS `retak`,(sum(if((`a`.`Kode` = '010001'),`a`.`jumlah_total`,0)) + sum(if((`a`.`Kode` = '010002'),`a`.`jumlah_total`,0))) AS `stok` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTS') and (`a`.`satuan` = 'KG') and (year(`b`.`tanggal`) = year(now()))) group by year(`b`.`tanggal`),month(`b`.`tanggal`);

-- ----------------------------
-- View structure for 00-00-13-12-view-rekap-hasil-bulanan-kandang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-12-view-rekap-hasil-bulanan-kandang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-12-view-rekap-hasil-bulanan-kandang` AS select year(`b`.`tanggal`) AS `tahun`,month(`b`.`tanggal`) AS `bulan`,date_format(`b`.`tanggal`,'%Y-%m') AS `bulanan`,`b`.`kandang` AS `kandang`,sum(`a`.`jumlah_total`) AS `hasil`,`a`.`satuan` AS `satuan`,sum(`a`.`subtotal`) AS `total` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG') and (year(`b`.`tanggal`) = year(now()))) group by month(`b`.`tanggal`),`b`.`kandang`;

-- ----------------------------
-- View structure for 00-00-13-13-view-rekap-hasil-bulanan-permitra
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-13-view-rekap-hasil-bulanan-permitra`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-13-view-rekap-hasil-bulanan-permitra` AS select year(`b`.`tanggal`) AS `tahun`,month(`b`.`tanggal`) AS `bulan`,date_format(`b`.`tanggal`,'%Y-%m') AS `bulanan`,sum(if((`b`.`mitra` = 'B0027'),`a`.`jumlah_total`,0)) AS `bakalan`,sum(if((`b`.`mitra` = 'R0020'),`a`.`jumlah_total`,0)) AS `peletrenteng`,sum(if((`b`.`mitra` = 'W0008'),`a`.`jumlah_total`,0)) AS `wagir`,`a`.`satuan` AS `satuan`,sum(`a`.`subtotal`) AS `total` from (`00-00-13-01-view-rekam-telur-detail` `a` join `00-00-13-00-view-rekam-telur` `b` on((`a`.`faktur` = `b`.`faktur`))) where ((substr(`a`.`faktur`,1,3) = 'RTH') and (`a`.`satuan` = 'KG') and (year(`b`.`tanggal`) = year(now()))) group by month(`b`.`tanggal`);

-- ----------------------------
-- View structure for 00-00-13-15-view-rekam-hasil-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-13-15-view-rekam-hasil-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-13-15-view-rekam-hasil-telur` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,week(`a`.`tanggal`,0) AS `minggu`,year(`a`.`tanggal`) AS `tahun`,`a`.`kandang` AS `kandang`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`a`.`recording` AS `recording`,`a`.`jumlah` AS `jumlah`,`a`.`id_kandang` AS `id_kandang`,`a`.`id_recording` AS `id_recording`,`a`.`tahunbulan` AS `tahunbulan`,`a`.`kodefaktur` AS `kodefaktur`,`a`.`blnthnfaktur` AS `blnthnfaktur`,`a`.`keterangan` AS `keterangan`,`00-00-13-01-view-rekam-telur-detail`.`jumlah_butir` AS `jumlah_butir`,`00-00-13-01-view-rekam-telur-detail`.`jumlah_total` AS `jumlah_total` from (`00-00-13-00-view-rekam-telur` `a` join `00-00-13-01-view-rekam-telur-detail` on((`00-00-13-01-view-rekam-telur-detail`.`faktur` = `a`.`faktur`))) where (left(`a`.`faktur`,3) = 'RTH');

-- ----------------------------
-- View structure for 00-00-14-00-view-rekam-pakan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-00-view-rekam-pakan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-00-view-rekam-pakan` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`b`.`Kode` AS `kandang`,`b`.`Gudang` AS `gudang`,`c`.`nama` AS `namagudang`,`b`.`Mitra` AS `mitra`,`b`.`NmMitra` AS `namamitra`,`d`.`nama` AS `recording`,`a`.`total` AS `total` from (((`recording_pakan` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`))) left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`))) join `jenis_recording` `d` on((`a`.`id_recording` = `d`.`id`)));

-- ----------------------------
-- View structure for 00-00-14-01-view-rekam-pakan-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-01-view-rekam-pakan-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-01-view-rekam-pakan-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_recording_pakan` AS `id_recording_pakan`,`a`.`faktur_recording` AS `faktur_recording`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`jumlah_satuan` AS `jumlah_satuan`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah_satuan`) AS `subtotal`,`a`.`keterangan` AS `keterangan` from ((((`recording_pakan_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `recording_pakan` `x` on(((`x`.`id` = `a`.`id_recording_pakan`) or (`x`.`faktur` = `a`.`faktur_recording`)))) group by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-14-02-view-rekam-pakan-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-02-view-rekam-pakan-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-02-view-rekam-pakan-total` AS select `a`.`id_recording_pakan` AS `id_recording_pakan`,`a`.`faktur_recording` AS `faktur`,sum(`a`.`subtotal`) AS `total` from `00-00-14-01-view-rekam-pakan-detail` `a` group by `a`.`faktur_recording`;

-- ----------------------------
-- View structure for 00-00-14-03-view-rekam-pakan-lama-kandang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-03-view-rekam-pakan-lama-kandang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-03-view-rekam-pakan-lama-kandang` AS select `a`.`cNoJrn` AS `cNoJrn`,date_format(`a`.`dTanggal`,'%Y-%m-%d') AS `dTanggal`,`a`.`cNoBukti` AS `cNoBukti`,trim(right(`a`.`cKeterangan`,(length(`a`.`cKeterangan`) - locate(':',`a`.`cKeterangan`,22)))) AS `kandang`,year(`a`.`dTanggal`) AS `yearnum`,week(`a`.`dTanggal`,3) AS `minggu`,`b`.`Kode` AS `Kode`,`b`.`NmBarang` AS `NmBarang`,`b`.`Qty` AS `Qty`,`b`.`Satuan` AS `Satuan`,`b`.`Gudang` AS `Gudang` from (`tbjurnal` `a` left join `pakan` `b` on((`b`.`Faktur` = `a`.`cNoBukti`))) where ((left(`a`.`cNoBukti`,2) = 'PT') and (`b`.`Kode` = '020078')) order by `a`.`dTanggal`;

-- ----------------------------
-- View structure for 00-00-14-04-view-pakan-baru
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-04-view-pakan-baru`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-04-view-pakan-baru` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,left(`a`.`faktur`,2) AS `kd`,`a`.`tanggal` AS `tanggal`,year(`a`.`tanggal`) AS `tahun`,week(`a`.`tanggal`,0) AS `weekyear`,`a`.`kandang` AS `kandang`,`a`.`mitra` AS `mitra`,`a`.`namamitra` AS `namamitra`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`b`.`jumlah_satuan` AS `jumlah`,`b`.`satuan` AS `satuan` from (`00-00-14-00-view-rekam-pakan` `a` join `00-00-14-01-view-rekam-pakan-detail` `b` on((`a`.`faktur` = `b`.`faktur_recording`))) where (left(`a`.`faktur`,2) = 'RP');

-- ----------------------------
-- View structure for 00-00-14-04-view-summary-rekam-pakan-kandang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-04-view-summary-rekam-pakan-kandang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-04-view-summary-rekam-pakan-kandang` AS select `a`.`cNoJrn` AS `cNoJrn`,date_format(`a`.`dTanggal`,'%Y-%m-%d') AS `dTanggal`,`a`.`cNoBukti` AS `cNoBukti`,trim(right(`a`.`cKeterangan`,(length(`a`.`cKeterangan`) - locate(':',`a`.`cKeterangan`,22)))) AS `kandang`,year(`a`.`dTanggal`) AS `yearnum`,week(`a`.`dTanggal`,3) AS `minggu`,`b`.`Kode` AS `Kode`,`b`.`NmBarang` AS `NmBarang`,sum(`b`.`Qty`) AS `Qty`,`b`.`Satuan` AS `Satuan`,`b`.`Gudang` AS `Gudang` from (`tbjurnal` `a` left join `pakan` `b` on((`b`.`Faktur` = `a`.`cNoBukti`))) where ((left(`a`.`cNoBukti`,2) = 'PT') and (`b`.`Kode` = '020078')) group by year(`a`.`dTanggal`),trim(right(`a`.`cKeterangan`,(length(`a`.`cKeterangan`) - locate(':',`a`.`cKeterangan`,22)))),week(`a`.`dTanggal`,3) order by `a`.`dTanggal`;

-- ----------------------------
-- View structure for 00-00-14-05-view-rekam-pakan-jadi
-- ----------------------------
DROP VIEW IF EXISTS `00-00-14-05-view-rekam-pakan-jadi`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-14-05-view-rekam-pakan-jadi` AS select `a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,`a`.`Gudang` AS `Gudang`,`a`.`Kode` AS `Kode`,`a`.`NmBarang` AS `NmBarang`,`a`.`Qty` AS `Qty`,`a`.`Satuan` AS `Satuan` from `pakan` `a` where (`a`.`Kode` = '020078');

-- ----------------------------
-- View structure for 00-00-15-00-view-rekam-medis
-- ----------------------------
DROP VIEW IF EXISTS `00-00-15-00-view-rekam-medis`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-15-00-view-rekam-medis` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`b`.`Kode` AS `kandang`,`b`.`Gudang` AS `gudang`,`c`.`nama` AS `namagudang`,`b`.`Mitra` AS `mitra`,`b`.`NmMitra` AS `namamitra`,`d`.`nama` AS `recording`,`a`.`total` AS `total` from (((`recording_medis` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`))) left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`))) join `jenis_recording` `d` on((`a`.`id_recording` = `d`.`id`)));

-- ----------------------------
-- View structure for 00-00-15-01-view-rekam-medis-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-15-01-view-rekam-medis-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-15-01-view-rekam-medis-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`id_recording_medis` AS `id_recording_medis`,`a`.`faktur_recording` AS `faktur`,`b`.`Kode` AS `Kode`,`a`.`umur` AS `umur`,`b`.`Nama` AS `Nama`,`a`.`jumlah_satuan` AS `jumlah_satuan`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah_satuan`) AS `subtotal`,`a`.`keterangan` AS `keterangan` from ((((`recording_medis_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `recording_medis` `x` on(((`x`.`id` = `a`.`id_recording_medis`) or (`x`.`faktur` = `a`.`faktur_recording`)))) group by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-15-02-view-rekam-medis-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-15-02-view-rekam-medis-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-15-02-view-rekam-medis-total` AS select `a`.`id_recording_medis` AS `id_recording_medis`,`a`.`faktur` AS `faktur`,sum(`a`.`subtotal`) AS `total` from `00-00-15-01-view-rekam-medis-detail` `a` group by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-16-00-view-rekam-assembly
-- ----------------------------
DROP VIEW IF EXISTS `00-00-16-00-view-rekam-assembly`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-16-00-view-rekam-assembly` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`b`.`kd_gudang` AS `kd_gudang`,`b`.`nama` AS `nama`,`c`.`faktur` AS `formula`,`c`.`tanggal` AS `tgl_formula`,`a`.`jumlah` AS `jumlah`,`a`.`id_gudang` AS `id_gudang`,`a`.`id_formulasi` AS `id_formulasi` from ((`assembly_pakan` `a` join `gudang` `b` on((`a`.`id_gudang` = `b`.`id`))) left join `formulasi` `c` on((`a`.`id_formulasi` = `c`.`id`))) order by `a`.`id` desc;

-- ----------------------------
-- View structure for 00-00-16-01-view-rekam-assembly-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-16-01-view-rekam-assembly-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-16-01-view-rekam-assembly-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`jumlah_satuan` AS `jumlah_satuan`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,if(((`a`.`id_satuan` = 1) and ((`c`.`Satuan1` = 'ZK') or (`c`.`Satuan1` = 'zk'))),(`c`.`Isi2` * `a`.`jumlah_satuan`),if(((`a`.`id_satuan` = 2) and ((`c`.`Satuan2` = 'KG') or (`c`.`Satuan2` = 'kg'))),`a`.`jumlah_satuan`,`a`.`jumlah_satuan`)) AS `satuan_real`,(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah_satuan`) AS `subtotal`,`b`.`Keterangan` AS `keterangan`,`a`.`urutan` AS `urutan`,`c`.`Isi2` AS `Isi2`,`c`.`Isi3` AS `Isi3`,`c`.`Satuan1` AS `Satuan1`,`c`.`Satuan2` AS `Satuan2`,`c`.`Satuan3` AS `Satuan3` from ((((`assembly_pakan_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `assembly_pakan` `x` on((`x`.`faktur` = `a`.`faktur`))) group by `a`.`id_detail` order by `a`.`urutan`,`a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-16-02-view-rekam-assembly-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-16-02-view-rekam-assembly-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-16-02-view-rekam-assembly-total` AS select `a`.`faktur` AS `faktur`,sum(`a`.`subtotal`) AS `total`,sum(`a`.`satuan_real`) AS `jumlah`,round((sum(`a`.`subtotal`) / sum(`a`.`satuan_real`)),3) AS `harga_jadi` from `00-00-16-01-view-rekam-assembly-detail` `a` group by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-17-00-view-purchase-request
-- ----------------------------
DROP VIEW IF EXISTS `00-00-17-00-view-purchase-request`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-17-00-view-purchase-request` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`b`.`Kode` AS `kandang`,`b`.`Gudang` AS `gudang`,`c`.`nama` AS `namagudang`,`b`.`Mitra` AS `mitra`,`b`.`NmMitra` AS `namamitra`,`a`.`is_approved` AS `is_approved`,`a`.`keterangan` AS `keterangan` from ((`purchase_request` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`))) left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`)));

-- ----------------------------
-- View structure for 00-00-17-01-view-purchase-request-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-17-01-view-purchase-request-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-17-01-view-purchase-request-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`a`.`jumlah` AS `jumlah`,`b`.`Nama` AS `Nama`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`keterangan` AS `keterangan`,`c`.`kode` AS `kode` from ((`purchase_request_detail` `a` join `barang` `b` on((`b`.`id` = `a`.`id_barang`))) join `barang_satuan` `c` on((`c`.`id_barang` = `a`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-18-00-view-receive-item
-- ----------------------------
DROP VIEW IF EXISTS `00-00-18-00-view-receive-item`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-18-00-view-receive-item` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`b`.`Kode` AS `kandang`,`b`.`Gudang` AS `gudang`,`c`.`nama` AS `namagudang`,`b`.`Mitra` AS `mitra`,`b`.`NmMitra` AS `namamitra`,`a`.`is_approved` AS `is_approved`,`a`.`keterangan` AS `keterangan`,`a`.`faktur_reff` AS `faktur_reff`,`a`.`faktur_do` AS `faktur_do`,`a`.`id_kandang` AS `id_kandang`,`a`.`id_gudang` AS `id_gudang`,`a`.`id_supplier` AS `id_supplier`,`a`.`tanggal_terima` AS `tanggal_terima`,`a`.`kirim_via` AS `kirim_via`,`a`.`nopol_pengirim` AS `nopol_pengirim`,`a`.`nama_pengirim` AS `nama_pengirim` from ((`receive_item` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`))) left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-18-01-view-receive-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-18-01-view-receive-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-18-01-view-receive-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`a`.`jumlah` AS `jumlah`,`b`.`Nama` AS `Nama`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`keterangan` AS `keterangan`,`c`.`kode` AS `kode`,`a`.`jumlah_retur` AS `jumlah_retur` from ((`receive_item_detail` `a` join `barang` `b` on((`b`.`id` = `a`.`id_barang`))) join `barang_satuan` `c` on((`c`.`id_barang` = `a`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-19-00-view-sales-order
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-00-view-sales-order`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-00-view-sales-order` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tgl` AS `tgl`,`b`.`Kode` AS `kdcustomer`,`b`.`Nama` AS `namacustomer`,`a`.`tgl_terima` AS `tgl_terima`,`a`.`tgl_kedaluarsa` AS `tgl_kedaluarsa`,`a`.`totalbayar` AS `totalbayar`,`c`.`jenis_pembayaran` AS `jenis_pembayaran`,`a`.`keterangan` AS `keterangan`,md5(`a`.`id`) AS `md5id`,`d`.`nama` AS `nama`,`a`.`grandtotal` AS `grandtotal`,`b`.`id` AS `idcustomer`,`e`.`faktur` AS `fakturtrx` from ((((`sales_order` `a` left join `customer` `b` on((`b`.`id` = `a`.`id_customer`))) left join `jenis_pembayaran` `c` on((`c`.`id` = `a`.`id_bayar`))) left join `sales` `d` on((`a`.`id_sales` = `d`.`id_karyawan`))) left join `sales_trx` `e` on((`a`.`faktur` = `e`.`faktur_so`))) order by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-19-00-view-sales-trx
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-00-view-sales-trx`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-00-view-sales-trx` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tgl` AS `tgl`,`b`.`Kode` AS `kdcustomer`,`b`.`Nama` AS `namacustomer`,`a`.`totalbayar` AS `totalbayar`,`c`.`jenis_pembayaran` AS `jenis_pembayaran`,`a`.`keterangan` AS `keterangan`,md5(`a`.`id`) AS `md5id`,`a`.`faktur_so` AS `faktur_so`,`a`.`id_so` AS `id_so`,`a`.`uangmuka` AS `uangmuka`,`a`.`biayakirim` AS `biayakirim`,`a`.`ppn` AS `ppn`,`a`.`grandtotal` AS `grandtotal`,`a`.`diskon` AS `diskon`,`a`.`sisa` AS `sisa`,`a`.`status` AS `status`,`a`.`tgl_jtempo` AS `tgl_jtempo`,`a`.`akun_piutang` AS `akun_piutang`,`s`.`nama` AS `namasales` from (((`sales_trx` `a` join `customer` `b` on((`b`.`id` = `a`.`id_customer`))) left join `jenis_pembayaran` `c` on((`c`.`id` = `a`.`id_bayar`))) left join `sales` `s` on((`a`.`id_sales` = `s`.`id`))) order by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-19-01-view-sales-order-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-01-view-sales-order-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-01-view-sales-order-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`jumlah` AS `jumlah`,`a`.`id_satuan` AS `id_satuan`,`a`.`harga_jual` AS `harga_jual`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,if((`a`.`harga_jual` > 0),`a`.`harga_jual`,if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`))) AS `harga`,if((`a`.`harga_jual` > 0),(`a`.`harga_jual` * `a`.`jumlah`),(if((`a`.`id_satuan` = 1),`d`.`hb1`,if((`a`.`id_satuan` = 2),`d`.`hb2`,`d`.`hb3`)) * `a`.`jumlah`)) AS `subtotal`,`b`.`id` AS `idbarang` from ((((`sales_order_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `sales_order` `x` on((`x`.`faktur` = `a`.`faktur`))) group by `a`.`id_detail`;

-- ----------------------------
-- View structure for 00-00-19-01-view-sales-trx-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-01-view-sales-trx-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-01-view-sales-trx-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`jumlah` AS `jumlah`,`a`.`id_satuan` AS `id_satuan`,if((`a`.`id_satuan` = 1),`d`.`HJ1R`,if((`a`.`id_satuan` = 2),`d`.`HJ2R`,`d`.`HJ3R`)) AS `harga_satuan`,`a`.`harga_jual` AS `harga_jual`,if((`a`.`harga_jual` > 0),`a`.`harga_jual`,if((`a`.`id_satuan` = 1),`d`.`HJ1R`,if((`a`.`id_satuan` = 2),`d`.`HJ2R`,`d`.`HJ3R`))) AS `harga`,(if((`a`.`harga_jual` > 0),`a`.`harga_jual`,if((`a`.`id_satuan` = 1),`d`.`HJ1R`,if((`a`.`id_satuan` = 2),`d`.`HJ2R`,`d`.`HJ3R`))) * `a`.`jumlah`) AS `subtotal` from ((((`sales_trx_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) left join `sales_trx` `x` on((`x`.`faktur` = `a`.`faktur`)));

-- ----------------------------
-- View structure for 00-00-19-02-view-sales-order-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-02-view-sales-order-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-02-view-sales-order-total` AS select `a`.`faktur` AS `faktur`,sum(`a`.`subtotal`) AS `total` from `00-00-19-01-view-sales-order-detail` `a` group by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-19-02-view-sales-trx-total
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-02-view-sales-trx-total`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-02-view-sales-trx-total` AS select `a`.`faktur` AS `faktur`,sum(`a`.`subtotal`) AS `total` from `00-00-19-01-view-sales-trx-detail` `a` group by `a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-19-04-view-per-transaksi-jual-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-04-view-per-transaksi-jual-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-04-view-per-transaksi-jual-detail` AS select `x`.`id` AS `id`,`a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`x`.`tgl` AS `tgl`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`jumlah` AS `jumlah`,if((`a`.`id_satuan` = 1),`d`.`HJ1R`,if((`a`.`id_satuan` = 2),`d`.`HJ2R`,`d`.`HJ3R`)) AS `harga_satuan`,`a`.`harga_jual` AS `harga_jual`,if((`a`.`harga_jual` > 0),`a`.`harga_jual`,if((`a`.`id_satuan` = 1),`d`.`HJ1R`,if((`a`.`id_satuan` = 2),`d`.`HJ2R`,`d`.`HJ3R`))) AS `harga`,(if((`a`.`harga_jual` > 0),`a`.`harga_jual`,if((`a`.`id_satuan` = 1),`d`.`HJ1R`,if((`a`.`id_satuan` = 2),`d`.`HJ2R`,`d`.`HJ3R`))) * `a`.`jumlah`) AS `subtotal`,`x`.`id_customer` AS `id_customer`,`a`.`id_barang` AS `id_barang` from ((((`sales_trx_detail` `a` join `barang_satuan` `c` on((`a`.`id_barang` = `c`.`id_barang`))) join `barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `barang_harga` `d` on((`b`.`id` = `d`.`id_barang`))) join `sales_trx` `x` on((`x`.`faktur` = `a`.`faktur`)));

-- ----------------------------
-- View structure for 00-00-19-05-view-transaksi-jual-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-19-05-view-transaksi-jual-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-19-05-view-transaksi-jual-detail` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_so` AS `faktur_so`,`a`.`tgl` AS `tgl`,`a`.`id_customer` AS `id_customer`,`b`.`id_barang` AS `id_barang`,`c`.`Kode` AS `Kode`,`c`.`Nama` AS `Nama`,`b`.`jumlah` AS `jumlah`,`b`.`id_satuan` AS `id_satuan`,if((`b`.`id_satuan` = 1),`d`.`Satuan1`,if((`b`.`id_satuan` = 2),`d`.`Satuan2`,`d`.`Satuan3`)) AS `satuan`,if((`b`.`id_satuan` = 1),`e`.`HJ1R`,if((`b`.`id_satuan` = 2),`e`.`HJ2R`,`e`.`HJ3R`)) AS `harga_table`,`b`.`harga_jual` AS `harga_jual`,if((`b`.`harga_jual` > 0),`b`.`harga_jual`,if((`b`.`id_satuan` = 1),`e`.`HJ1R`,if((`b`.`id_satuan` = 2),`e`.`HJ2R`,`e`.`HJ3R`))) AS `harga`,`a`.`totalbayar` AS `totalbayar`,`a`.`uangmuka` AS `uangmuka`,`a`.`biayakirim` AS `biayakirim`,`a`.`ppn` AS `ppn`,`a`.`grandtotal` AS `grandtotal`,`a`.`diskon` AS `diskon`,`a`.`sisa` AS `sisa`,`b`.`id_detail` AS `id_detail`,`d`.`Isi2` AS `Isi2`,`d`.`Isi3` AS `Isi3`,`d`.`Satuan1` AS `Satuan1`,`d`.`Satuan2` AS `Satuan2`,`d`.`Satuan3` AS `Satuan3`,`e`.`HJ1R` AS `hj1r`,`e`.`HJ2R` AS `hj2r`,`e`.`HJ3R` AS `hj3r` from ((((`sales_trx` `a` join `sales_trx_detail` `b` on((`a`.`faktur` = `b`.`faktur`))) join `barang` `c` on((`b`.`id_barang` = `c`.`id`))) join `barang_satuan` `d` on((`c`.`id` = `d`.`id_barang`))) join `barang_harga` `e` on((`c`.`id` = `e`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-20-00-view-penyesuaian-barang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-20-00-view-penyesuaian-barang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-20-00-view-penyesuaian-barang` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_reff` AS `faktur_reff`,`a`.`akun` AS `akun`,`a`.`tanggal` AS `tanggal`,`c`.`nama` AS `namagudang`,`a`.`keterangan` AS `keterangan` from (`penyesuaian` `a` left join `gudang` `c` on((`a`.`id_gudang` = `c`.`id`)));

-- ----------------------------
-- View structure for 00-00-20-01-view-penyesuaian-barang-detail
-- ----------------------------
DROP VIEW IF EXISTS `00-00-20-01-view-penyesuaian-barang-detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-20-01-view-penyesuaian-barang-detail` AS select `a`.`id_detail` AS `id_detail`,`a`.`faktur` AS `faktur`,`a`.`jumlah` AS `jumlah`,`b`.`Nama` AS `Nama`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`jumlah_baru` AS `jumlah_baru`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`ket_detail` AS `keterangan`,`c`.`kode` AS `kode` from ((`penyesuaian_detail` `a` join `barang` `b` on((`b`.`id` = `a`.`id_barang`))) join `barang_satuan` `c` on((`c`.`id_barang` = `a`.`id_barang`)));

-- ----------------------------
-- View structure for 00-00-21-00-view-armada
-- ----------------------------
DROP VIEW IF EXISTS `00-00-21-00-view-armada`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-21-00-view-armada` AS select `a`.`id` AS `id`,`b`.`nama` AS `nama`,`c`.`nama_supir` AS `nama_supir`,`b`.`kode` AS `kode`,`b`.`nopol` AS `nopol`,`c`.`id_karyawan` AS `id_karyawan` from ((`armada` `a` join `kendaraan` `b` on((`b`.`id` = `a`.`kendaraan_id`))) join `supir_armada` `c` on((`c`.`id` = `a`.`supir_id`)));

-- ----------------------------
-- View structure for 00-00-22-00-view-formula
-- ----------------------------
DROP VIEW IF EXISTS `00-00-22-00-view-formula`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-22-00-view-formula` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`tanggal` AS `tanggal`,`a`.`nama` AS `nama`,`a`.`keterangan` AS `keterangan`,`a`.`id_kandang` AS `id_kandang`,`b`.`Kode` AS `Kode`,`b`.`Gudang` AS `Gudang`,`b`.`NmGudang` AS `NmGudang`,`b`.`Mitra` AS `Mitra`,`b`.`NmMitra` AS `NmMitra`,`a`.`jml_hasil_jadi` AS `jml_hasil_jadi`,`a`.`satuan_jadi` AS `satuan_jadi` from (`formulasi` `a` left join `kandang` `b` on((`a`.`id_kandang` = `b`.`id`)));

-- ----------------------------
-- View structure for 00-00-23-view-kandang-isi
-- ----------------------------
DROP VIEW IF EXISTS `00-00-23-view-kandang-isi`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-23-view-kandang-isi` AS select `a`.`Faktur` AS `Faktur`,`a`.`Tgl` AS `Tgl`,`a`.`Customer` AS `Customer`,`a`.`Kandang` AS `Kandang`,`a`.`Gudang` AS `Gudang`,`a`.`NmGudang` AS `NmGudang`,`a`.`Keterangan` AS `Keterangan`,`a`.`Kode` AS `Kode`,`a`.`Nmbarang` AS `Nmbarang`,`a`.`Qty` AS `Qty`,`a`.`Satuan` AS `Satuan`,`a`.`QtyReal` AS `QtyReal`,`a`.`Umur` AS `Umur`,`a`.`NmCustomer` AS `NmCustomer` from `isilayer` `a` where (left(`a`.`Faktur`,2) = 'IS') order by `a`.`Tgl` desc;

-- ----------------------------
-- View structure for 00-00-24-00-view-hutang-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-00-view-hutang-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-00-view-hutang-customer` AS select `c`.`id` AS `id`,`c`.`faktur` AS `faktur`,`c`.`faktur_ref` AS `faktur_ref`,`c`.`tanggal` AS `tanggal`,`c`.`akun_hutang` AS `akun_hutang`,`c`.`mutasidebet` AS `mutasidebet`,`c`.`mutasikredit` AS `mutasikredit`,`c`.`saldodebet` AS `saldodebet`,`c`.`saldokredit` AS `saldokredit`,`c`.`keterangan` AS `keterangan`,`d`.`Kode` AS `Kode`,`d`.`Nama` AS `Nama`,`d`.`JthTempo` AS `JthTempo`,(`c`.`tanggal` + interval `d`.`JthTempo` day) AS `jatuhtempo`,(curdate() - (`c`.`tanggal` + interval `d`.`JthTempo` day)) AS `warningtempo`,`c`.`id_customer` AS `idcs` from (`kartuhutang` `c` join `customer` `d` on((`d`.`id` = `c`.`id_customer`)));

-- ----------------------------
-- View structure for 00-00-24-00-view-hutang-pembelian-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-00-view-hutang-pembelian-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-00-view-hutang-pembelian-supplier` AS select `c`.`id` AS `id`,`c`.`faktur` AS `faktur`,`c`.`faktur_ref` AS `faktur_ref`,`c`.`tanggal` AS `tanggal`,`c`.`akun_hutang` AS `akun_hutang`,`c`.`mutasidebet` AS `mutasidebet`,`c`.`mutasikredit` AS `mutasikredit`,`c`.`saldodebet` AS `saldodebet`,`c`.`saldokredit` AS `saldokredit`,`c`.`keterangan` AS `keterangan`,`d`.`Kode` AS `Kode`,`d`.`Nama` AS `Nama`,`d`.`JthTempo` AS `JthTempo`,(`c`.`tanggal` + interval `d`.`JthTempo` day) AS `jatuhtempo`,(curdate() - (`c`.`tanggal` + interval `d`.`JthTempo` day)) AS `warningtempo`,`c`.`id_supplier` AS `idsp` from (`kartuhutang` `c` join `supplier` `d` on((`d`.`id` = `c`.`id_supplier`))) where (left(`c`.`faktur_ref`,2) = 'PT');

-- ----------------------------
-- View structure for 00-00-24-00-view-hutang-supplier
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-00-view-hutang-supplier`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-00-view-hutang-supplier` AS select `c`.`id` AS `id`,`c`.`faktur` AS `faktur`,`c`.`faktur_ref` AS `faktur_ref`,`c`.`tanggal` AS `tanggal`,`c`.`akun_hutang` AS `akun_hutang`,`c`.`mutasidebet` AS `mutasidebet`,`c`.`mutasikredit` AS `mutasikredit`,`c`.`saldodebet` AS `saldodebet`,`c`.`saldokredit` AS `saldokredit`,`c`.`keterangan` AS `keterangan`,`d`.`Kode` AS `Kode`,`d`.`Nama` AS `Nama`,`d`.`JthTempo` AS `JthTempo`,(`c`.`tanggal` + interval `d`.`JthTempo` day) AS `jatuhtempo`,(curdate() - (`c`.`tanggal` + interval `d`.`JthTempo` day)) AS `warningtempo`,`c`.`id_supplier` AS `idsp` from (`kartuhutang` `c` join `supplier` `d` on((`d`.`id` = `c`.`id_supplier`)));

-- ----------------------------
-- View structure for 00-00-24-00-view-piutang-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-00-view-piutang-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-00-view-piutang-customer` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,(`a`.`tanggal` + interval `b`.`JthTempo` day) AS `jatuhtempo`,(curdate() - (`a`.`tanggal` + interval `b`.`JthTempo` day)) AS `warningtempo`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`keterangan` AS `keterangan`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`b`.`JthTempo` AS `JthTempo`,`a`.`id_customer` AS `id_customer`,`b`.`Plafond` AS `Plafond`,`b`.`Subsidi` AS `Subsidi` from (`kartupiutang` `a` join `customer` `b` on((`b`.`id` = `a`.`id_customer`)));

-- ----------------------------
-- View structure for 00-00-24-01-view-piutang-sum-saldodebet
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-01-view-piutang-sum-saldodebet`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-01-view-piutang-sum-saldodebet` AS select `a`.`id` AS `id`,`a`.`akun_hutang` AS `akun_hutang`,sum(`a`.`mutasidebet`) AS `mutasidebet`,sum(`a`.`mutasikredit`) AS `mutasikredit`,if((sum(`a`.`mutasikredit`) > 0),(sum(`a`.`mutasidebet`) - sum(`a`.`mutasikredit`)),sum(`a`.`mutasidebet`)) AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`id_customer` AS `id_customer` from `kartupiutang` `a` group by `a`.`akun_hutang` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-01-view-piutang-sum-saldokredit
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-01-view-piutang-sum-saldokredit`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-01-view-piutang-sum-saldokredit` AS select `a`.`id` AS `id`,`a`.`akun_hutang` AS `akun_hutang`,sum(`a`.`saldokredit`) AS `saldokredit` from `kartupiutang` `a` group by `a`.`akun_hutang`;

-- ----------------------------
-- View structure for 00-00-24-02-view-rekap-piutang-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-02-view-rekap-piutang-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-02-view-rekap-piutang-customer` AS select `a`.`id` AS `id`,`a`.`akun_hutang` AS `akun_hutang`,sum(`a`.`mutasidebet`) AS `mutasidebet`,sum(`a`.`mutasikredit`) AS `mutasikredit`,if((sum(`a`.`mutasikredit`) > 0),(sum(`a`.`mutasidebet`) - sum(`a`.`mutasikredit`)),sum(`a`.`mutasidebet`)) AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`id_customer` AS `id_customer`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`b`.`Alamat` AS `Alamat`,`b`.`JthTempo` AS `JthTempo`,`b`.`Plafond` AS `Plafond`,`b`.`Golongan` AS `Golongan`,`b`.`Subsidi` AS `Subsidi`,`b`.`Kota` AS `Kota` from (`kartupiutang` `a` left join `customer` `b` on((`a`.`id_customer` = `b`.`id`))) group by `a`.`akun_hutang` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-03-view-pelunasan-piutang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-03-view-pelunasan-piutang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-03-view-pelunasan-piutang` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `fakturlunas`,`b`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`b`.`tanggal` AS `tglunas`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`id_customer` AS `id_customer`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`b`.`plafon` AS `plafon`,`b`.`piutang` AS `piutang`,`b`.`bayar` AS `bayar`,`b`.`sisa` AS `sisa`,`a`.`keterangan` AS `keterangan`,`c`.`Kode` AS `Kode`,`c`.`Golongan` AS `Golongan`,`c`.`Nama` AS `Nama`,`c`.`Alamat` AS `Alamat`,`c`.`Kota` AS `Kota` from ((`kartupiutang` `a` left join `pelunasan_piutang` `b` on((`a`.`faktur_ref` = `b`.`faktur`))) left join `customer` `c` on((`a`.`id_customer` = `c`.`id`))) where (left(`a`.`faktur_ref`,2) = 'PP');

-- ----------------------------
-- View structure for 00-00-24-03-view-status-penjualan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-03-view-status-penjualan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-03-view-status-penjualan` AS select `a`.`id_customer` AS `id_customer`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`Plafond` AS `Plafond`,`a`.`Golongan` AS `Golongan`,`a`.`Subsidi` AS `Subsidi`,if((`a`.`saldodebet` <= `a`.`Plafond`),1,0) AS `bawahplafon`,if(((`a`.`saldodebet` <= `a`.`Plafond`) and (`a`.`Golongan` = 'TK')),1,0) AS `tokotrx` from `00-00-24-02-view-rekap-piutang-customer` `a` order by `a`.`id_customer`;

-- ----------------------------
-- View structure for 00-00-24-04-view-piutang-lunas
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-04-view-piutang-lunas`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-04-view-piutang-lunas` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`saldodebet` AS `saldodebet`,`a`.`Plafond` AS `Plafond`,`a`.`keterangan` AS `keterangan`,`a`.`id_customer` AS `id_customer`,`b`.`id` AS `idlunas`,`b`.`fakturlunas` AS `fakturlunas`,if((`b`.`fakturlunas` is not null),'lunas','belum lunas') AS `statlunas`,`a`.`JthTempo` AS `JthTempo`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`Subsidi` AS `Subsidi`,`a`.`jatuhtempo` AS `jatuhtempo`,`a`.`warningtempo` AS `warningtempo`,`b`.`tanggal` AS `tgllunas` from (`00-00-24-00-view-piutang-customer` `a` left join `00-00-24-03-view-pelunasan-piutang` `b` on((`a`.`faktur_ref` = `b`.`faktur_ref`))) where (`a`.`mutasidebet` is not null) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-05-view-summary-piutang-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-05-view-summary-piutang-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-05-view-summary-piutang-customer` AS select `a`.`id` AS `id`,`a`.`akun_hutang` AS `akun_hutang`,sum(if((left(`a`.`faktur_ref`,2) = 'SA'),`a`.`saldodebet`,0)) AS `saldoawal`,sum(`a`.`mutasidebet`) AS `mutasidebet`,sum(`a`.`mutasikredit`) AS `mutasikredit`,if((sum(`a`.`mutasikredit`) > 0),(sum(`a`.`mutasidebet`) - sum(`a`.`mutasikredit`)),sum(`a`.`mutasidebet`)) AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`id_customer` AS `id_customer`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`b`.`Alamat` AS `Alamat`,`b`.`JthTempo` AS `JthTempo`,`b`.`Plafond` AS `Plafond`,`b`.`Golongan` AS `Golongan`,`b`.`Subsidi` AS `Subsidi`,`b`.`Kota` AS `Kota` from (`kartupiutang` `a` left join `customer` `b` on((`a`.`id_customer` = `b`.`id`))) group by `a`.`akun_hutang` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-06-view-piutang-jatuhtempo
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-06-view-piutang-jatuhtempo`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-06-view-piutang-jatuhtempo` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`jatuhtempo` AS `jatuhtempo`,`a`.`warningtempo` AS `warningtempo`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`tgllunas` AS `tgllunas`,`a`.`JthTempo` AS `JthTempo`,`a`.`fakturlunas` AS `fakturlunas`,`a`.`id_customer` AS `id_customer`,`a`.`Plafond` AS `Plafond`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`keterangan` AS `keterangan` from `00-00-24-04-view-piutang-lunas` `a` where ((left(`a`.`faktur_ref`,2) = 'ST') and isnull(`a`.`fakturlunas`));

-- ----------------------------
-- View structure for 00-00-24-07-view-hutang-sum-saldokredit
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-07-view-hutang-sum-saldokredit`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-07-view-hutang-sum-saldokredit` AS select `a`.`id` AS `id`,`a`.`akun_hutang` AS `akun_hutang`,sum(if((left(`a`.`faktur_ref`,2) = 'SA'),`a`.`mutasikredit`,0)) AS `saldoawal`,sum(`a`.`mutasidebet`) AS `mutasidebet`,sum(`a`.`mutasikredit`) AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,if((sum(`a`.`mutasidebet`) > 0),(sum(`a`.`mutasikredit`) - sum(`a`.`mutasidebet`)),sum(`a`.`mutasikredit`)) AS `saldokredit`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),sha(md5('sp')),sha(md5('cs'))) AS `type`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`id`,`c`.`id`) AS `idvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Kode`,`c`.`Kode`) AS `kdvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Nama`,`c`.`Nama`) AS `namavendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Alamat`,`c`.`Alamat`) AS `alamatvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),'Supplier','Customer') AS `jenisvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`JthTempo`,`c`.`JthTempo`) AS `tempovendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Kota`,'') AS `kotavendor`,`b`.`id` AS `idsp`,`c`.`id` AS `idcs` from ((`kartuhutang` `a` left join `supplier` `b` on((`a`.`id_supplier` = `b`.`id`))) left join `customer` `c` on((`c`.`id` = `a`.`id_customer`))) where ((`a`.`id_customer` is not null) or (`a`.`id_supplier` is not null)) group by `a`.`akun_hutang` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-08-view-hutang-summary
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-08-view-hutang-summary`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-08-view-hutang-summary` AS select `a`.`id` AS `id`,`a`.`akun_hutang` AS `akun_hutang`,sum(if((left(`a`.`faktur_ref`,2) = 'SA'),`a`.`mutasikredit`,0)) AS `saldoawal`,sum(`a`.`mutasidebet`) AS `mutasidebet`,sum(if((left(`a`.`faktur_ref`,2) <> 'SA'),`a`.`mutasikredit`,0)) AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,if((sum(`a`.`mutasidebet`) > 0),(sum(`a`.`mutasikredit`) - sum(`a`.`mutasidebet`)),sum(`a`.`mutasikredit`)) AS `saldokredit`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),sha(md5('sp')),sha(md5('cs'))) AS `type`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`id`,`c`.`id`) AS `idvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Kode`,`c`.`Kode`) AS `kdvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Nama`,`c`.`Nama`) AS `namavendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Alamat`,`c`.`Alamat`) AS `alamatvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),'Supplier','Customer') AS `jenisvendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`JthTempo`,`c`.`JthTempo`) AS `tempovendor`,if(((`a`.`id_supplier` <> NULL) or (`a`.`id_supplier` > 0)),`b`.`Kota`,'') AS `kotavendor`,`b`.`id` AS `idsp`,`c`.`id` AS `idcs` from ((`kartuhutang` `a` left join `supplier` `b` on((`a`.`id_supplier` = `b`.`id`))) left join `customer` `c` on((`c`.`id` = `a`.`id_customer`))) where ((`a`.`id_customer` is not null) or (`a`.`id_supplier` is not null)) group by `a`.`akun_hutang` order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-09-view-pelunasan-hutang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-09-view-pelunasan-hutang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-09-view-pelunasan-hutang` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`tgltempo` AS `tgltempo`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`id_customer` AS `id_customer`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_mitra` AS `id_mitra`,`a`.`id_gudang` AS `id_gudang`,`a`.`id_kandang` AS `id_kandang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`keterangan` AS `keterangan`,`b`.`hutang` AS `hutang`,`b`.`bayar` AS `bayar`,(`b`.`hutang` - `b`.`bayar`) AS `sisahutang`,`b`.`faktur` AS `fakturlunas`,`b`.`id` AS `idlunas`,`b`.`tempohari` AS `tempohari`,(`a`.`tanggal` + interval `b`.`tempohari` day) AS `jatuhtempo`,(curdate() - (`a`.`tanggal` + interval `b`.`tempohari` day)) AS `warningtempo` from (`kartuhutang` `a` left join `pelunasan_hutang` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where (left(`a`.`faktur_ref`,2) = 'PH');

-- ----------------------------
-- View structure for 00-00-24-09-view-pelunasan-hutang-backup28122016
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-09-view-pelunasan-hutang-backup28122016`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-09-view-pelunasan-hutang-backup28122016` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`tgltempo` AS `tgltempo`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`id_customer` AS `id_customer`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_mitra` AS `id_mitra`,`a`.`id_gudang` AS `id_gudang`,`a`.`id_kandang` AS `id_kandang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`keterangan` AS `keterangan`,`b`.`hutang` AS `hutang`,`b`.`bayar` AS `bayar`,(`b`.`hutang` - `b`.`bayar`) AS `sisahutang`,`b`.`faktur` AS `fakturlunas`,`b`.`id` AS `idlunas`,`b`.`tempohari` AS `tempohari`,(`a`.`tanggal` + interval `b`.`tempohari` day) AS `jatuhtempo`,(curdate() - (`a`.`tanggal` + interval `b`.`tempohari` day)) AS `warningtempo` from (`kartuhutang` `a` left join `pelunasan_hutang` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where (left(`a`.`faktur_ref`,2) = 'PH');

-- ----------------------------
-- View structure for 00-00-24-10-view-hutang-customer-lunas
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-10-view-hutang-customer-lunas`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-10-view-hutang-customer-lunas` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`keterangan` AS `keterangan`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`JthTempo` AS `JthTempo`,(`a`.`tanggal` + interval `a`.`JthTempo` day) AS `jatuhtempo`,(curdate() - (`a`.`tanggal` + interval `a`.`JthTempo` day)) AS `warningtempo`,`a`.`idcs` AS `idcs`,`b`.`hutang` AS `hutang`,`b`.`bayar` AS `bayar`,`b`.`sisahutang` AS `sisahutang`,`b`.`fakturlunas` AS `fakturlunas`,`b`.`idlunas` AS `idlunas`,if((`b`.`fakturlunas` is not null),'lunas','belum lunas') AS `statlunas` from (`00-00-24-00-view-hutang-customer` `a` left join `00-00-24-09-view-pelunasan-hutang` `b` on((`a`.`faktur_ref` = `b`.`faktur_ref`))) order by `a`.`id`;

-- ----------------------------
-- View structure for 00-00-24-11-view-hutang-beli-supplier-lunas
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-11-view-hutang-beli-supplier-lunas`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-11-view-hutang-beli-supplier-lunas` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`keterangan` AS `keterangan`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`JthTempo` AS `JthTempo`,(`a`.`tanggal` + interval `a`.`JthTempo` day) AS `jatuhtempo`,(curdate() - (`a`.`tanggal` + interval `a`.`JthTempo` day)) AS `warningtempo`,`a`.`idsp` AS `idsp`,`b`.`hutang` AS `hutang`,`b`.`bayar` AS `bayar`,`b`.`sisahutang` AS `sisahutang`,`b`.`fakturlunas` AS `fakturlunas`,`b`.`idlunas` AS `idlunas`,if((`b`.`fakturlunas` is not null),'lunas','belum lunas') AS `statlunas` from (`00-00-24-00-view-hutang-pembelian-supplier` `a` left join `00-00-24-09-view-pelunasan-hutang` `b` on((`b`.`faktur_ref` = `a`.`faktur_ref`)));

-- ----------------------------
-- View structure for 00-00-24-12-view-usiapiutang-customer
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-12-view-usiapiutang-customer`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-12-view-usiapiutang-customer` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`JthTempo` AS `JthTempo`,`a`.`jatuhtempo` AS `jatuhtempo`,`a`.`warningtempo` AS `warningtempo`,(to_days(`a`.`jatuhtempo`) - to_days(curdate())) AS `usiapiutang`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`mutasidebet` AS `mutasidebet`,`a`.`mutasikredit` AS `mutasikredit`,`a`.`saldodebet` AS `saldodebet`,`a`.`saldokredit` AS `saldokredit`,`a`.`keterangan` AS `keterangan`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`id_customer` AS `id_customer`,`a`.`Plafond` AS `Plafond`,`a`.`Subsidi` AS `Subsidi` from `00-00-24-00-view-piutang-customer` `a`;

-- ----------------------------
-- View structure for 00-00-24-13-view-hutang-beli-supplier-macet
-- ----------------------------
DROP VIEW IF EXISTS `00-00-24-13-view-hutang-beli-supplier-macet`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-24-13-view-hutang-beli-supplier-macet` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun_hutang` AS `akun_hutang`,`a`.`Kode` AS `Kode`,`a`.`Nama` AS `Nama`,`a`.`JthTempo` AS `JthTempo`,`a`.`jatuhtempo` AS `jatuhtempo`,`a`.`warningtempo` AS `warningtempo`,(to_days(`a`.`jatuhtempo`) - to_days(curdate())) AS `usiahutang`,`a`.`idcs` AS `idcs`,if((((to_days(`a`.`jatuhtempo`) - to_days(curdate())) >= 356) and (`a`.`jatuhtempo` >= curdate())),1,0) AS `ismacet` from `00-00-24-00-view-hutang-customer` `a`;

-- ----------------------------
-- View structure for 00-00-25-00-kartustok-rekam-ayam
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-00-kartustok-rekam-ayam`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-00-kartustok-rekam-ayam` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun` AS `akun`,`a`.`tipe_kartustok` AS `tipe_kartustok`,`a`.`tipe` AS `tipe`,`a`.`jumlah` AS `jumlah`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`b`.`id_kandang` AS `id_kandang`,`b`.`kandang` AS `kandang`,`b`.`id_gudang` AS `id_gudang`,`b`.`gudang` AS `gudang`,`b`.`namagudang` AS `namagudang`,`b`.`id_mitra` AS `id_mitra`,`b`.`namamitra` AS `namamitra`,`c`.`kode` AS `kode`,`c`.`nmbarang` AS `nmbarang`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,if(`a`.`id_satuan`,`c`.`Satuan3`,0))) AS `satuan`,`a`.`id_satuan` AS `id_satuan`,`a`.`id_barang` AS `id_barang` from ((`kartustok` `a` left join `00-00-12-00-view-rekam-ayam` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) left join `00-00-01-05-view-barang-satuan` `c` on((`c`.`id_barang` = `a`.`id_barang`))) where ((`a`.`tipe_kartustok` = 'Ayam') and (`a`.`faktur_ref` <> '0') and (`a`.`faktur_ref` is not null));

-- ----------------------------
-- View structure for 00-00-25-00-kartustok-rekam-pakan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-00-kartustok-rekam-pakan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-00-kartustok-rekam-pakan` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun` AS `akun`,`a`.`tipe_kartustok` AS `tipe_kartustok`,`a`.`tipe` AS `tipe`,`a`.`jumlah` AS `jumlah`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`keterangan` AS `keterangan`,`c`.`kode` AS `kode`,`c`.`nmbarang` AS `nmbarang`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,if(`a`.`id_satuan`,`c`.`Satuan3`,0))) AS `satuan`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan` from ((`kartustok` `a` left join `00-00-12-00-view-rekam-ayam` on((`00-00-12-00-view-rekam-ayam`.`faktur` = `a`.`faktur_ref`))) left join `00-00-01-05-view-barang-satuan` `c` on((`c`.`id_barang` = `a`.`id_barang`))) where ((`a`.`tipe_kartustok` = 'Pakan') and (`a`.`faktur_ref` <> '0') and (`a`.`faktur_ref` is not null));

-- ----------------------------
-- View structure for 00-00-25-00-kartustok-rekam-telur
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-00-kartustok-rekam-telur`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-00-kartustok-rekam-telur` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun` AS `akun`,`a`.`tipe_kartustok` AS `tipe_kartustok`,`a`.`tipe` AS `tipe`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`keterangan` AS `keterangan`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime` from (`kartustok` `a` left join `00-00-13-00-view-rekam-telur` `b` on((`b`.`faktur` = `a`.`faktur_ref`))) where ((`a`.`tipe_kartustok` = 'Telur') and (`a`.`faktur_ref` <> '0') and (`a`.`faktur_ref` is not null));

-- ----------------------------
-- View structure for 00-00-25-01-kartustok-barang
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-01-kartustok-barang`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-01-kartustok-barang` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun` AS `akun`,`a`.`tipe_kartustok` AS `tipe_kartustok`,`a`.`tipe` AS `tipe`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`keterangan` AS `keterangan`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime` from `kartustok` `a` where ((`a`.`tipe_kartustok` = 'Barang') and (`a`.`faktur_ref` <> '0') and (`a`.`faktur_ref` is not null));

-- ----------------------------
-- View structure for 00-00-25-02-kartustok-barang-beli
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-02-kartustok-barang-beli`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-02-kartustok-barang-beli` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun` AS `akun`,`a`.`tipe_kartustok` AS `tipe_kartustok`,`a`.`tipe` AS `tipe`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`keterangan` AS `keterangan`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,left(`a`.`faktur_ref`,2) AS `kd`,`b`.`tgl_pt` AS `tgl_pt`,`b`.`Nama` AS `Nama`,`b`.`Kode` AS `Kode`,`b`.`idsp` AS `idsp`,`b`.`id_customer` AS `id_customer`,`b`.`kdcs` AS `kdcs`,`b`.`namacs` AS `namacs`,`b`.`id_supplier` AS `id_supplier` from (`kartustok` `a` join `00-00-06-00-view-transaksi-beli` `b` on((`a`.`faktur_ref` = `b`.`faktur_pt`))) where ((`a`.`tipe_kartustok` = 'Barang') and (`a`.`faktur_ref` <> '0') and (`a`.`faktur_ref` is not null) and (left(`a`.`faktur_ref`,2) = 'PT'));

-- ----------------------------
-- View structure for 00-00-25-03-kartustok-barang-jual
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-03-kartustok-barang-jual`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-03-kartustok-barang-jual` AS select `a`.`id` AS `id`,`a`.`faktur` AS `faktur`,`a`.`faktur_ref` AS `faktur_ref`,`a`.`tanggal` AS `tanggal`,`a`.`akun` AS `akun`,`a`.`tipe_kartustok` AS `tipe_kartustok`,`a`.`tipe` AS `tipe`,`a`.`jumlah` AS `jumlah`,`a`.`id_barang` AS `id_barang`,`a`.`id_satuan` AS `id_satuan`,`a`.`debet` AS `debet`,`a`.`kredit` AS `kredit`,`a`.`keterangan` AS `keterangan`,`a`.`user_id` AS `user_id`,`a`.`datetime` AS `datetime`,left(`a`.`faktur_ref`,2) AS `kd` from `kartustok` `a` where ((`a`.`tipe_kartustok` = 'Barang') and (`a`.`faktur_ref` <> '0') and (`a`.`faktur_ref` is not null) and (left(`a`.`faktur_ref`,2) = 'ST'));

-- ----------------------------
-- View structure for 00-00-25-04-kartustok-barang-rekap
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-04-kartustok-barang-rekap`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-04-kartustok-barang-rekap` AS select `a`.`id_barang` AS `id_barang`,`b`.`id` AS `id`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) AS `Masuk`,sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0)) AS `Keluar`,sum(if(((left(`a`.`faktur_ref`,2) = 'SA') and (`a`.`id_satuan` = 1)),`a`.`jumlah`,0)) AS `awalsat1`,sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D') and (left(`a`.`faktur_ref`,2) <> 'SA')),`a`.`jumlah`,0)) AS `insat1`,sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat1`,(sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat1`,`c`.`Satuan1` AS `Satuan1`,sum(if(((left(`a`.`faktur_ref`,2) = 'SA') and (`a`.`id_satuan` = 2)),`a`.`jumlah`,0)) AS `awalsat2`,sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D') and (left(`a`.`faktur_ref`,2) <> 'SA')),`a`.`jumlah`,0)) AS `insat2`,sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat2`,(sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat2`,`c`.`Satuan2` AS `Satuan2`,sum(if(((left(`a`.`faktur_ref`,2) = 'SA') and (`a`.`id_satuan` = 3)),`a`.`jumlah`,0)) AS `awalsat3`,sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D') and (left(`a`.`faktur_ref`,2) <> 'SA')),`a`.`jumlah`,0)) AS `insat3`,sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat3`,(sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat3`,`c`.`Satuan3` AS `Satuan3`,(sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) - sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0))) AS `Saldo`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`id_satuan` AS `id_satuan`,`c`.`Isi2` AS `Isi2`,`c`.`Isi3` AS `Isi3`,`c`.`Max` AS `Max`,`c`.`SatuanMax` AS `SatuanMax`,`c`.`Min` AS `Min`,`c`.`SatuanMin` AS `SatuanMin` from ((`kartustok` `a` join `00-00-01-00-view-barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `00-00-01-05-view-barang-satuan` `c` on((`b`.`id` = `c`.`id_barang`))) group by `a`.`id_barang` order by `a`.`id_barang`;

-- ----------------------------
-- View structure for 00-00-25-05-kartustok-barang-rekap-groupbysatuan
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-05-kartustok-barang-rekap-groupbysatuan`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-05-kartustok-barang-rekap-groupbysatuan` AS select `a`.`id_barang` AS `id_barang`,`b`.`id` AS `id`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) AS `Masuk`,sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) AS `insat1`,sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) AS `insat2`,sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) AS `insat3`,sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0)) AS `Keluar`,sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat1`,sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat2`,sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat3`,(sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) - sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0))) AS `Saldo`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`id_satuan` AS `id_satuan`,`c`.`Satuan1` AS `Satuan1`,`c`.`Isi2` AS `Isi2`,`c`.`Satuan2` AS `Satuan2`,`c`.`Isi3` AS `Isi3`,`c`.`Satuan3` AS `Satuan3`,`c`.`Max` AS `Max`,`c`.`SatuanMax` AS `SatuanMax`,`c`.`Min` AS `Min`,`c`.`SatuanMin` AS `SatuanMin` from ((`kartustok` `a` join `00-00-01-00-view-barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `00-00-01-05-view-barang-satuan` `c` on((`b`.`id` = `c`.`id_barang`))) group by `a`.`id_barang`,`a`.`id_satuan` order by `a`.`id_barang`;

-- ----------------------------
-- View structure for 00-00-25-06-kartustok-barang-rekap-groupbykartustok
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-06-kartustok-barang-rekap-groupbykartustok`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-06-kartustok-barang-rekap-groupbykartustok` AS select `a`.`id_barang` AS `id_barang`,`b`.`id` AS `id`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) AS `Masuk`,sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0)) AS `Keluar`,sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) AS `insat1`,sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat1`,(sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat1`,`c`.`Satuan1` AS `Satuan1`,`c`.`Isi2` AS `Isi2`,sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) AS `insat2`,sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat2`,(sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) + (sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) * `c`.`Isi2`)) AS `totinsat2`,(sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat2`,(((sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) * `c`.`Isi2`) + (sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)))) AS `saldo_dlm_sat2`,`c`.`Satuan2` AS `Satuan2`,`c`.`Isi3` AS `Isi3`,sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)) AS `outsat3`,sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) AS `insat3`,(sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) + (sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) * `c`.`Isi3`)) AS `totinsat3`,(sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat3`,(((sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) * `c`.`Isi3`) + (sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0)))) AS `saldo_dlm_sat3`,`c`.`Satuan3` AS `Satuan3`,(sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) - sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0))) AS `Saldo`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`id_satuan` AS `id_satuan`,`c`.`Max` AS `Max`,`c`.`SatuanMax` AS `SatuanMax`,`c`.`Min` AS `Min`,`c`.`SatuanMin` AS `SatuanMin` from ((`kartustok` `a` join `00-00-01-00-view-barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `00-00-01-05-view-barang-satuan` `c` on((`b`.`id` = `c`.`id_barang`))) group by `a`.`id_barang` order by `c`.`id_barang`;

-- ----------------------------
-- View structure for 00-00-25-07-kartustok-barang-nosum
-- ----------------------------
DROP VIEW IF EXISTS `00-00-25-07-kartustok-barang-nosum`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-25-07-kartustok-barang-nosum` AS select `a`.`id_barang` AS `id_barang`,`b`.`id` AS `id`,`a`.`faktur` AS `faktur`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,if((`a`.`tipe` = 'D'),`a`.`jumlah`,0) AS `Masuk`,if((`a`.`tipe` = 'K'),`a`.`jumlah`,0) AS `Keluar`,if(((left(`a`.`faktur_ref`,2) = 'SA') and (`a`.`id_satuan` = 1)),`a`.`jumlah`,0) AS `awalsat1`,if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D') and (left(`a`.`faktur_ref`,2) <> 'SA')),`a`.`jumlah`,0) AS `insat1`,if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0) AS `outsat1`,(sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 1) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat1`,`c`.`Satuan1` AS `Satuan1`,if(((left(`a`.`faktur_ref`,2) = 'SA') and (`a`.`id_satuan` = 2)),`a`.`jumlah`,0) AS `awalsat2`,if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D') and (left(`a`.`faktur_ref`,2) <> 'SA')),`a`.`jumlah`,0) AS `insat2`,if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0) AS `outsat2`,(sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 2) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat2`,`c`.`Satuan2` AS `Satuan2`,if(((left(`a`.`faktur_ref`,2) = 'SA') and (`a`.`id_satuan` = 3)),`a`.`jumlah`,0) AS `awalsat3`,if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D') and (left(`a`.`faktur_ref`,2) <> 'SA')),`a`.`jumlah`,0) AS `insat3`,if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0) AS `outsat3`,(sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'D')),`a`.`jumlah`,0)) - sum(if(((`a`.`id_satuan` = 3) and (`a`.`tipe` = 'K')),`a`.`jumlah`,0))) AS `saldosat3`,`c`.`Satuan3` AS `Satuan3`,(sum(if((`a`.`tipe` = 'D'),`a`.`jumlah`,0)) - sum(if((`a`.`tipe` = 'K'),`a`.`jumlah`,0))) AS `Saldo`,if((`a`.`id_satuan` = 1),`c`.`Satuan1`,if((`a`.`id_satuan` = 2),`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,`a`.`id_satuan` AS `id_satuan`,`c`.`Isi2` AS `Isi2`,`c`.`Isi3` AS `Isi3`,`c`.`Max` AS `Max`,`c`.`SatuanMax` AS `SatuanMax`,`c`.`Min` AS `Min`,`c`.`SatuanMin` AS `SatuanMin`,`a`.`tanggal` AS `tanggal` from ((`kartustok` `a` join `00-00-01-00-view-barang` `b` on((`a`.`id_barang` = `b`.`id`))) join `00-00-01-05-view-barang-satuan` `c` on((`b`.`id` = `c`.`id_barang`))) group by `a`.`id_barang`,`a`.`id` order by `a`.`id_barang`,`a`.`tanggal`,`a`.`faktur`;

-- ----------------------------
-- View structure for 00-00-view-child-menu
-- ----------------------------
DROP VIEW IF EXISTS `00-00-view-child-menu`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `00-00-view-child-menu` AS select `a`.`id` AS `id`,`a`.`title` AS `title`,`a`.`url` AS `url`,`a`.`parent` AS `parent`,`a`.`module` AS `module`,`a`.`data-remote` AS `data-remote`,`a`.`data-target` AS `data-target`,`a`.`levelid` AS `levelid`,`a`.`groupid` AS `groupid`,`a`.`is_ajax_url` AS `is_ajax_url`,`a`.`datetime` AS `datetime`,`a`.`icon` AS `icon`,`a`.`orders` AS `orders`,`a`.`is_active` AS `is_active`,`a`.`is_disabled` AS `is_disabled` from `menu` `a` where (`a`.`parent` <> 0);

-- ----------------------------
-- View structure for 0000_2601_view_detail_returbelix
-- ----------------------------
DROP VIEW IF EXISTS `0000_2601_view_detail_returbelix`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `0000_2601_view_detail_returbelix` AS select `a`.`id_detail` AS `id_detail`,`x`.`tgl_pt` AS `tgl_pt`,`a`.`po` AS `faktur_po`,`a`.`pt` AS `faktur_pt`,`a`.`id_barang` AS `id_barang`,`b`.`Kode` AS `Kode`,`b`.`Nama` AS `Nama`,`a`.`jumlah` AS `jumlah`,`a`.`id_satuan` AS `id_satuan`,if(`a`.`id_satuan` = 1,`c`.`Satuan1`,if(`a`.`id_satuan` = 2,`c`.`Satuan2`,`c`.`Satuan3`)) AS `satuan`,if(`a`.`id_satuan` = 1,`d`.`hb1`,if(`a`.`id_satuan` = 2,`d`.`hb2`,`d`.`hb3`)) AS `harga_satuan`,`a`.`harga_beli` AS `harga_beli`,if(`a`.`harga_beli` <= 0 or `a`.`harga_beli` is null,if(`a`.`id_satuan` = 1,`d`.`hb1`,if(`a`.`id_satuan` = 2,`d`.`hb2`,`d`.`hb3`)),`a`.`harga_beli`) * `a`.`jumlah` AS `subtotal`,if(`a`.`harga_beli` <= 0 or `a`.`harga_beli` is null,if(`a`.`id_satuan` = 1,`d`.`hb1`,if(`a`.`id_satuan` = 2,`d`.`hb2`,`d`.`hb3`)),`a`.`harga_beli`) AS `harga`,`a`.`keterangan` AS `keterangan`,`a`.`id_supplier` AS `id_supplier`,`a`.`id_supplier` AS `idsp`,`f`.`Kode` AS `kdsp`,`f`.`Nama` AS `namasp`,`a`.`id_customer` AS `id_customer`,`a`.`id_customer` AS `idcs`,`e`.`Kode` AS `kdcs`,`e`.`Nama` AS `namacs`,`x`.`totalbayar` AS `totalbayar`,`x`.`uangmuka` AS `uangmuka`,`x`.`biayakirim` AS `biayakirim`,`x`.`grandtotal` AS `grandtotal`,`x`.`sisa` AS `sisa`,`x`.`sisabayar` AS `sisabayar`,`a`.`id_pt` AS `id_pt`,`a`.`id_po` AS `id_po`,`b`.`id` AS `id`,`c`.`Isi2` AS `Isi2`,`c`.`Isi3` AS `Isi3`,`c`.`Satuan1` AS `Satuan1`,`c`.`Satuan2` AS `Satuan2`,`c`.`Satuan3` AS `Satuan3`,if(`a`.`id_satuan` = 1,`a`.`jumlah`,0) AS `jml1`,if(`a`.`id_satuan` = 1,`c`.`Satuan1`,0) AS `sat1`,if(`a`.`id_satuan` = 2,`a`.`jumlah`,0) AS `jml2`,if(`a`.`id_satuan` = 2,`c`.`Satuan2`,0) AS `sat2`,if(`a`.`id_satuan` = 3,`a`.`jumlah`,0) AS `jml3`,if(`a`.`id_satuan` = 3,`c`.`Satuan3`,0) AS `sat3`,if(`a`.`id_supplier` > 0 and `a`.`id_customer` = 0,`f`.`Kode`,if(`a`.`id_customer` > 0 and `a`.`id_supplier` = 0,`e`.`Kode`,0)) AS `kd`,if(`a`.`id_supplier` > 0 and `a`.`id_customer` = 0,`f`.`Nama`,if(`a`.`id_customer` > 0 and `a`.`id_supplier` = 0,`e`.`Nama`,0)) AS `nm`,`a`.`isactive` AS `isactive`,`a`.`isdelete` AS `isdelete`,`a`.`datedelete` AS `datedelete` from ((((((`purchase_transaction_detail` `a` left join `barang_satuan` `c` on(`a`.`id_barang` = `c`.`id_barang`)) left join `barang` `b` on(`a`.`id_barang` = `b`.`id`)) left join `barang_harga` `d` on(`b`.`id` = `d`.`id_barang`)) left join `purchase_transaction` `x` on(`x`.`faktur_pt` = `a`.`pt`)) left join `supplier` `f` on(`a`.`id_supplier` = `f`.`id`)) left join `customer` `e` on(`a`.`id_customer` = `e`.`id`)) ;

SET FOREIGN_KEY_CHECKS = 1;
