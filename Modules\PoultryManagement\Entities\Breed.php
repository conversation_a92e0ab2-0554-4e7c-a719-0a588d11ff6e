<?php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Breed extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'breed_code',
        'breed_name',
        'breed_type',
        'origin_country',
        'description',
        'characteristics',
        'performance_standards',
        'mature_weight_female',
        'mature_weight_male',
        'egg_production_peak',
        'egg_weight_average',
        'production_period_weeks',
        'feed_conversion_ratio',
        'mortality_rate_standard',
        'housing_density_recommended',
        'temperature_range_optimal',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'characteristics' => 'array',
        'performance_standards' => 'array',
        'mature_weight_female' => 'decimal:2',
        'mature_weight_male' => 'decimal:2',
        'egg_production_peak' => 'decimal:2',
        'egg_weight_average' => 'decimal:2',
        'production_period_weeks' => 'integer',
        'feed_conversion_ratio' => 'decimal:2',
        'mortality_rate_standard' => 'decimal:2',
        'housing_density_recommended' => 'decimal:2',
        'temperature_range_optimal' => 'array',
        'is_active' => 'boolean',
    ];

    // Boot method to auto-generate UUID
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($breed) {
            if (empty($breed->uuid)) {
                $breed->uuid = Str::uuid();
            }
        });
    }

    // Relationships
    public function flocks()
    {
        return $this->hasMany(Flock::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('breed_type', $type);
    }

    // Accessors
    public function getActiveFlockCountAttribute(): int
    {
        return $this->flocks()->where('status', 'active')->count();
    }

    public function getTotalPopulationAttribute(): int
    {
        return $this->flocks()->where('status', 'active')->sum('current_count');
    }

    // Methods
    public function getPerformanceComparison($flock): array
    {
        // This will be implemented when we have production data
        return [
            'hen_day_production' => [
                'actual' => 0,
                'standard' => $this->egg_production_peak,
                'variance' => 0,
            ],
            'mortality_rate' => [
                'actual' => 0,
                'standard' => $this->mortality_rate_standard,
                'variance' => 0,
            ],
            'feed_conversion' => [
                'actual' => 0,
                'standard' => $this->feed_conversion_ratio,
                'variance' => 0,
            ],
        ];
    }
}
