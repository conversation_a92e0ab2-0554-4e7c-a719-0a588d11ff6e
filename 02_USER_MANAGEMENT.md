# 02 - USER MANAGEMENT MODULE

## 📋 OVERVIEW

Modul User Management menyediakan sistem authentication, authorization, dan user management yang comprehensive untuk ERP Poultry Management System. Modul ini menggunakan role-based access control (RBAC) dengan permission granular untuk mengatur akses user ke berbagai fitur sistem.

## 🎯 TUJUAN

- Secure authentication dan session management
- Role-based access control (RBAC) system
- User profile management dan preferences
- Activity logging dan audit trail
- Multi-level authorization (farm, house, flock level)
- Password policy dan security features
- User onboarding dan training tracking

## ⏱️ ESTIMASI WAKTU

**Total**: 16-20 jam
- Backend implementation: 10-12 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Security Specialist** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- User management
users
user_profiles
user_preferences
user_sessions

-- Role & Permission system
roles
permissions
role_permissions
user_roles
user_permissions

-- Activity & Audit
activity_logs
login_attempts
password_resets
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create User Management module
php artisan module:make UserManagement

# Create module components
php artisan module:make-controller UserManagement AuthController --api
php artisan module:make-controller UserManagement UserController --api
php artisan module:make-controller UserManagement RoleController --api
php artisan module:make-controller UserManagement PermissionController --api
php artisan module:make-model UserManagement Role
php artisan module:make-model UserManagement Permission
php artisan module:make-model UserManagement UserProfile
php artisan module:make-request UserManagement LoginRequest
php artisan module:make-request UserManagement RegisterRequest
php artisan module:make-request UserManagement UserStoreRequest
php artisan module:make-resource UserManagement UserResource
php artisan module:make-resource UserManagement RoleResource
php artisan module:make-policy UserManagement UserPolicy
php artisan module:make-seeder UserManagement UserManagementSeeder
```

### **1.2 User Model Enhancement**

```php
<?php
// app/Models/User.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, LogsActivity, SoftDeletes;

    protected $fillable = [
        'uuid',
        'employee_id',
        'name',
        'email',
        'phone',
        'password',
        'avatar',
        'status',
        'last_login_at',
        'email_verified_at',
        'phone_verified_at',
        'two_factor_enabled',
        'timezone',
        'language',
        'created_by',
        'updated_by',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'two_factor_enabled' => 'boolean',
        'deleted_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function profile()
    {
        return $this->hasOne(\Modules\UserManagement\Entities\UserProfile::class);
    }

    public function preferences()
    {
        return $this->hasOne(\Modules\UserManagement\Entities\UserPreference::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function farms()
    {
        return $this->belongsToMany(\Modules\PoultryManagement\Entities\Farm::class, 'user_farms');
    }

    public function assignedHouses()
    {
        return $this->belongsToMany(\Modules\PoultryManagement\Entities\House::class, 'user_houses');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByRole($query, $role)
    {
        return $query->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    public function scopeByFarm($query, $farmId)
    {
        return $query->whereHas('farms', function ($q) use ($farmId) {
            $q->where('farm_id', $farmId);
        });
    }

    // Accessors
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    public function getAvatarUrlAttribute(): string
    {
        return $this->avatar 
            ? asset('storage/avatars/' . $this->avatar)
            : asset('images/default-avatar.png');
    }

    public function getIsOnlineAttribute(): bool
    {
        return $this->last_login_at && $this->last_login_at->gt(now()->subMinutes(5));
    }

    // Methods
    public function hasAccessToFarm($farmId): bool
    {
        if ($this->hasRole('super_admin')) {
            return true;
        }

        return $this->farms()->where('farm_id', $farmId)->exists();
    }

    public function hasAccessToHouse($houseId): bool
    {
        if ($this->hasRole(['super_admin', 'farm_manager'])) {
            return true;
        }

        return $this->assignedHouses()->where('house_id', $houseId)->exists();
    }

    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    public function generateEmployeeId(): string
    {
        $year = now()->year;
        $lastUser = static::where('employee_id', 'like', "EMP{$year}%")
            ->orderBy('employee_id', 'desc')
            ->first();

        if ($lastUser) {
            $lastNumber = (int) substr($lastUser->employee_id, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return "EMP{$year}{$newNumber}";
    }
}
```

### **1.3 Role Model**

```php
<?php
// Modules/UserManagement/Entities/Role.php

namespace Modules\UserManagement\Entities;

use Spatie\Permission\Models\Role as SpatieRole;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Role extends SpatieRole
{
    use LogsActivity;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'level',
        'is_system',
        'guard_name',
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'level' => 'integer',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'display_name', 'description'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Scopes
    public function scopeSystemRoles($query)
    {
        return $query->where('is_system', true);
    }

    public function scopeCustomRoles($query)
    {
        return $query->where('is_system', false);
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', '<=', $level);
    }

    // Accessors
    public function getIsEditableAttribute(): bool
    {
        return !$this->is_system;
    }

    public function getIsDeletableAttribute(): bool
    {
        return !$this->is_system && $this->users()->count() === 0;
    }

    // Methods
    public function canAssignTo($user): bool
    {
        if (!$user->hasRole('super_admin')) {
            $userMaxLevel = $user->roles()->max('level') ?? 0;
            return $this->level >= $userMaxLevel;
        }

        return true;
    }
}
```

### **1.4 Authentication Controller**

```php
<?php
// Modules/UserManagement/Http/Controllers/AuthController.php

namespace Modules\UserManagement\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use Modules\UserManagement\Http\Requests\LoginRequest;
use Modules\UserManagement\Http\Requests\RegisterRequest;
use Modules\UserManagement\Http\Resources\UserResource;

class AuthController extends Controller
{
    public function login(LoginRequest $request): JsonResponse
    {
        $this->ensureIsNotRateLimited($request);

        $credentials = $request->only('email', 'password');

        if (!Auth::attempt($credentials, $request->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey($request));

            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = Auth::user();
        
        // Check if user is active
        if (!$user->is_active) {
            Auth::logout();
            throw ValidationException::withMessages([
                'email' => ['Your account has been deactivated.'],
            ]);
        }

        // Update last login
        $user->updateLastLogin();

        // Create token
        $token = $user->createToken('auth-token', $this->getTokenAbilities($user))->plainTextToken;

        RateLimiter::clear($this->throttleKey($request));

        return response()->json([
            'message' => 'Login successful',
            'user' => new UserResource($user->load(['roles', 'permissions', 'profile'])),
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $userData = $request->validated();
        $userData['password'] = Hash::make($userData['password']);
        $userData['uuid'] = \Str::uuid();
        $userData['employee_id'] = (new User)->generateEmployeeId();
        $userData['status'] = 'active';

        $user = User::create($userData);

        // Assign default role
        $user->assignRole('farm_worker');

        // Create profile
        $user->profile()->create([
            'first_name' => $userData['name'],
            'last_name' => '',
        ]);

        // Create token
        $token = $user->createToken('auth-token', $this->getTokenAbilities($user))->plainTextToken;

        return response()->json([
            'message' => 'Registration successful',
            'user' => new UserResource($user->load(['roles', 'permissions', 'profile'])),
            'token' => $token,
            'token_type' => 'Bearer',
        ], 201);
    }

    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logout successful',
        ]);
    }

    public function me(Request $request): JsonResponse
    {
        $user = $request->user()->load(['roles', 'permissions', 'profile', 'farms']);

        return response()->json([
            'user' => new UserResource($user),
        ]);
    }

    public function refreshToken(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Revoke current token
        $request->user()->currentAccessToken()->delete();
        
        // Create new token
        $token = $user->createToken('auth-token', $this->getTokenAbilities($user))->plainTextToken;

        return response()->json([
            'token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    private function ensureIsNotRateLimited(Request $request): void
    {
        if (RateLimiter::tooManyAttempts($this->throttleKey($request), 5)) {
            $seconds = RateLimiter::availableIn($this->throttleKey($request));

            throw ValidationException::withMessages([
                'email' => ["Too many login attempts. Please try again in {$seconds} seconds."],
            ]);
        }
    }

    private function throttleKey(Request $request): string
    {
        return strtolower($request->input('email')) . '|' . $request->ip();
    }

    private function getTokenAbilities(User $user): array
    {
        $abilities = ['*']; // Default abilities

        // Add role-specific abilities
        if ($user->hasRole('super_admin')) {
            $abilities = ['*'];
        } elseif ($user->hasRole('farm_manager')) {
            $abilities = ['farm:*', 'house:*', 'flock:*', 'production:*'];
        } elseif ($user->hasRole('farm_worker')) {
            $abilities = ['production:read', 'production:create', 'flock:read'];
        }

        return $abilities;
    }
}
```

### **1.5 User Management Service**

```php
<?php
// Modules/UserManagement/Services/UserManagementService.php

namespace Modules\UserManagement\Services;

use App\Models\User;
use Modules\UserManagement\Entities\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UserManagementService
{
    public function createUser(array $data): User
    {
        return DB::transaction(function () use ($data) {
            $userData = [
                'uuid' => Str::uuid(),
                'employee_id' => (new User)->generateEmployeeId(),
                'name' => $data['name'],
                'email' => $data['email'],
                'phone' => $data['phone'] ?? null,
                'password' => Hash::make($data['password']),
                'status' => $data['status'] ?? 'active',
                'timezone' => $data['timezone'] ?? config('app.timezone'),
                'language' => $data['language'] ?? 'en',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ];

            $user = User::create($userData);

            // Assign role
            if (isset($data['role'])) {
                $user->assignRole($data['role']);
            }

            // Create profile
            if (isset($data['profile'])) {
                $user->profile()->create($data['profile']);
            }

            // Assign farms
            if (isset($data['farms'])) {
                $user->farms()->attach($data['farms']);
            }

            // Assign houses
            if (isset($data['houses'])) {
                $user->assignedHouses()->attach($data['houses']);
            }

            return $user;
        });
    }

    public function updateUser(User $user, array $data): User
    {
        return DB::transaction(function () use ($user, $data) {
            $userData = [
                'name' => $data['name'] ?? $user->name,
                'email' => $data['email'] ?? $user->email,
                'phone' => $data['phone'] ?? $user->phone,
                'status' => $data['status'] ?? $user->status,
                'timezone' => $data['timezone'] ?? $user->timezone,
                'language' => $data['language'] ?? $user->language,
                'updated_by' => auth()->id(),
            ];

            // Update password if provided
            if (isset($data['password'])) {
                $userData['password'] = Hash::make($data['password']);
            }

            $user->update($userData);

            // Update role
            if (isset($data['role'])) {
                $user->syncRoles([$data['role']]);
            }

            // Update profile
            if (isset($data['profile'])) {
                $user->profile()->updateOrCreate([], $data['profile']);
            }

            // Update farms
            if (isset($data['farms'])) {
                $user->farms()->sync($data['farms']);
            }

            // Update houses
            if (isset($data['houses'])) {
                $user->assignedHouses()->sync($data['houses']);
            }

            return $user->fresh();
        });
    }

    public function deleteUser(User $user): bool
    {
        if ($user->hasRole('super_admin') && User::role('super_admin')->count() <= 1) {
            throw new \Exception('Cannot delete the last super admin user.');
        }

        return $user->delete();
    }

    public function getUsersByRole(string $role): \Illuminate\Database\Eloquent\Collection
    {
        return User::role($role)->with(['profile', 'farms'])->get();
    }

    public function getUsersByFarm(int $farmId): \Illuminate\Database\Eloquent\Collection
    {
        return User::byFarm($farmId)->with(['roles', 'profile'])->get();
    }

    public function assignUserToFarm(User $user, int $farmId): void
    {
        if (!$user->farms()->where('farm_id', $farmId)->exists()) {
            $user->farms()->attach($farmId);
        }
    }

    public function removeUserFromFarm(User $user, int $farmId): void
    {
        $user->farms()->detach($farmId);
        
        // Also remove from houses in this farm
        $houseIds = \Modules\PoultryManagement\Entities\House::where('farm_id', $farmId)->pluck('id');
        $user->assignedHouses()->detach($houseIds);
    }

    public function getUserPermissions(User $user): array
    {
        $permissions = $user->getAllPermissions()->pluck('name')->toArray();
        
        // Add farm-specific permissions
        $farmPermissions = [];
        foreach ($user->farms as $farm) {
            $farmPermissions[] = "farm:{$farm->id}:access";
        }

        // Add house-specific permissions
        $housePermissions = [];
        foreach ($user->assignedHouses as $house) {
            $housePermissions[] = "house:{$house->id}:access";
        }

        return array_merge($permissions, $farmPermissions, $housePermissions);
    }

    public function createRole(array $data): Role
    {
        return Role::create([
            'name' => $data['name'],
            'display_name' => $data['display_name'],
            'description' => $data['description'] ?? null,
            'level' => $data['level'] ?? 1,
            'is_system' => false,
            'guard_name' => 'web',
        ]);
    }

    public function getUserAnalytics(): array
    {
        return [
            'total_users' => User::count(),
            'active_users' => User::active()->count(),
            'users_by_role' => User::join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                ->groupBy('roles.name', 'roles.display_name')
                ->selectRaw('roles.name, roles.display_name, COUNT(*) as count')
                ->get(),
            'recent_logins' => User::whereNotNull('last_login_at')
                ->where('last_login_at', '>=', now()->subDays(7))
                ->count(),
            'online_users' => User::where('last_login_at', '>=', now()->subMinutes(5))->count(),
        ];
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Authentication Context**

```typescript
// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authService } from '../services/authService';

interface User {
  id: number;
  name: string;
  email: string;
  roles: string[];
  permissions: string[];
  profile?: any;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const authReducer = (state: AuthState, action: any): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      };
    default:
      return state;
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: false,
    isLoading: false,
  });

  useEffect(() => {
    if (state.token) {
      authService.me()
        .then(response => {
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: {
              user: response.data.user,
              token: state.token,
            },
          });
        })
        .catch(() => {
          dispatch({ type: 'LOGOUT' });
          localStorage.removeItem('token');
        });
    }
  }, [state.token]);

  const login = async (email: string, password: string) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const response = await authService.login(email, password);
      localStorage.setItem('token', response.data.token);
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: response.data,
      });
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE' });
      throw error;
    }
  };

  const logout = () => {
    authService.logout();
    localStorage.removeItem('token');
    dispatch({ type: 'LOGOUT' });
  };

  const hasRole = (role: string): boolean => {
    return state.user?.roles.includes(role) || false;
  };

  const hasPermission = (permission: string): boolean => {
    return state.user?.permissions.includes(permission) || false;
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        login,
        logout,
        hasRole,
        hasPermission,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] User model dengan RBAC working
- [ ] Authentication endpoints working
- [ ] Role dan permission system
- [ ] Activity logging active
- [ ] Password security implemented
- [ ] API token management

### **Frontend**
- [ ] Login/logout functionality
- [ ] User context working
- [ ] Role-based navigation
- [ ] Permission checking
- [ ] User profile management
- [ ] Responsive design

### **Security**
- [ ] Password hashing secure
- [ ] Rate limiting implemented
- [ ] Token expiration working
- [ ] CSRF protection active
- [ ] Input validation complete
- [ ] SQL injection prevention

## 📞 NEXT STEPS

Setelah User Management module selesai:

1. **Test authentication flow** end-to-end
2. **Verify role permissions** working
3. **Test security features**
4. **Setup initial users** dan roles
5. **Commit module** ke repository
6. **Lanjut ke** `03_POULTRY_MANAGEMENT.md`

---

**IMPORTANT**: User Management adalah security foundation. Pastikan authentication robust, authorization granular, dan activity logging comprehensive untuk audit compliance.
