<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flocks', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('flock_number')->unique();
            $table->unsignedBigInteger('house_id');
            $table->unsignedBigInteger('breed_id');
            $table->date('placement_date');
            $table->string('source')->nullable();
            $table->string('supplier')->nullable();
            $table->integer('initial_count');
            $table->integer('current_count');
            $table->enum('production_stage', ['growing', 'pre_laying', 'laying', 'post_laying', 'finished'])->default('growing');
            $table->date('expected_production_start')->nullable();
            $table->date('expected_culling_date')->nullable();
            $table->date('actual_culling_date')->nullable();
            $table->integer('mortality_total')->default(0);
            $table->integer('culled_total')->default(0);
            $table->integer('transferred_out')->default(0);
            $table->integer('transferred_in')->default(0);
            $table->enum('status', ['active', 'finished', 'transferred', 'culled'])->default('active');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->index(['house_id', 'status']);
            $table->index(['breed_id', 'status']);
            $table->index(['production_stage', 'status']);
            $table->index(['placement_date']);
            $table->foreign('house_id')->references('id')->on('houses')->onDelete('cascade');
            $table->foreign('breed_id')->references('id')->on('breeds')->onDelete('restrict');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flocks');
    }
};
