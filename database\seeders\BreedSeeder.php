<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\PoultryManagement\Entities\Breed;
use Illuminate\Support\Str;

class BreedSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $breeds = [
            [
                'breed_code' => 'LSL',
                'breed_name' => 'Lohmann LSL',
                'breed_type' => 'layer',
                'origin_country' => 'Germany',
                'description' => 'High-performance white egg layer with excellent feed conversion',
                'characteristics' => [
                    'egg_color' => 'white',
                    'temperament' => 'calm',
                    'adaptability' => 'high',
                    'disease_resistance' => 'good'
                ],
                'performance_standards' => [
                    'peak_production' => '95%',
                    'production_period' => '72 weeks',
                    'feed_efficiency' => 'excellent'
                ],
                'mature_weight_female' => 1.65,
                'mature_weight_male' => 2.10,
                'egg_production_peak' => 95.0,
                'egg_weight_average' => 62.5,
                'production_period_weeks' => 72,
                'feed_conversion_ratio' => 2.1,
                'mortality_rate_standard' => 3.5,
                'housing_density_recommended' => 7.0,
                'temperature_range_optimal' => [
                    'min' => 18,
                    'max' => 24,
                    'ideal' => 21
                ],
            ],
            [
                'breed_code' => 'HYL',
                'breed_name' => 'Hy-Line Brown',
                'breed_type' => 'layer',
                'origin_country' => 'USA',
                'description' => 'Brown egg layer known for excellent production and adaptability',
                'characteristics' => [
                    'egg_color' => 'brown',
                    'temperament' => 'docile',
                    'adaptability' => 'very high',
                    'disease_resistance' => 'excellent'
                ],
                'performance_standards' => [
                    'peak_production' => '93%',
                    'production_period' => '74 weeks',
                    'feed_efficiency' => 'very good'
                ],
                'mature_weight_female' => 2.0,
                'mature_weight_male' => 2.5,
                'egg_production_peak' => 93.0,
                'egg_weight_average' => 64.0,
                'production_period_weeks' => 74,
                'feed_conversion_ratio' => 2.2,
                'mortality_rate_standard' => 4.0,
                'housing_density_recommended' => 6.5,
                'temperature_range_optimal' => [
                    'min' => 16,
                    'max' => 26,
                    'ideal' => 20
                ],
            ],
            [
                'breed_code' => 'ISA',
                'breed_name' => 'ISA Brown',
                'breed_type' => 'layer',
                'origin_country' => 'France',
                'description' => 'Robust brown egg layer with consistent performance',
                'characteristics' => [
                    'egg_color' => 'brown',
                    'temperament' => 'calm',
                    'adaptability' => 'high',
                    'disease_resistance' => 'good'
                ],
                'performance_standards' => [
                    'peak_production' => '92%',
                    'production_period' => '72 weeks',
                    'feed_efficiency' => 'good'
                ],
                'mature_weight_female' => 1.9,
                'mature_weight_male' => 2.4,
                'egg_production_peak' => 92.0,
                'egg_weight_average' => 63.0,
                'production_period_weeks' => 72,
                'feed_conversion_ratio' => 2.3,
                'mortality_rate_standard' => 4.5,
                'housing_density_recommended' => 6.8,
                'temperature_range_optimal' => [
                    'min' => 17,
                    'max' => 25,
                    'ideal' => 21
                ],
            ],
            [
                'breed_code' => 'NOV',
                'breed_name' => 'Novogen Brown',
                'breed_type' => 'layer',
                'origin_country' => 'France',
                'description' => 'High-quality brown egg layer with excellent shell quality',
                'characteristics' => [
                    'egg_color' => 'brown',
                    'temperament' => 'docile',
                    'adaptability' => 'good',
                    'disease_resistance' => 'good'
                ],
                'performance_standards' => [
                    'peak_production' => '91%',
                    'production_period' => '70 weeks',
                    'feed_efficiency' => 'good'
                ],
                'mature_weight_female' => 1.85,
                'mature_weight_male' => 2.3,
                'egg_production_peak' => 91.0,
                'egg_weight_average' => 63.5,
                'production_period_weeks' => 70,
                'feed_conversion_ratio' => 2.25,
                'mortality_rate_standard' => 4.2,
                'housing_density_recommended' => 7.0,
                'temperature_range_optimal' => [
                    'min' => 18,
                    'max' => 24,
                    'ideal' => 21
                ],
            ],
            [
                'breed_code' => 'BOV',
                'breed_name' => 'Bovans Brown',
                'breed_type' => 'layer',
                'origin_country' => 'Netherlands',
                'description' => 'Reliable brown egg layer with good feed conversion',
                'characteristics' => [
                    'egg_color' => 'brown',
                    'temperament' => 'calm',
                    'adaptability' => 'high',
                    'disease_resistance' => 'very good'
                ],
                'performance_standards' => [
                    'peak_production' => '90%',
                    'production_period' => '72 weeks',
                    'feed_efficiency' => 'very good'
                ],
                'mature_weight_female' => 1.95,
                'mature_weight_male' => 2.45,
                'egg_production_peak' => 90.0,
                'egg_weight_average' => 64.5,
                'production_period_weeks' => 72,
                'feed_conversion_ratio' => 2.15,
                'mortality_rate_standard' => 3.8,
                'housing_density_recommended' => 6.8,
                'temperature_range_optimal' => [
                    'min' => 16,
                    'max' => 25,
                    'ideal' => 20
                ],
            ],
            [
                'breed_code' => 'DOM',
                'breed_name' => 'Dominant CZ',
                'breed_type' => 'layer',
                'origin_country' => 'Czech Republic',
                'description' => 'Hardy brown egg layer suitable for various climates',
                'characteristics' => [
                    'egg_color' => 'brown',
                    'temperament' => 'active',
                    'adaptability' => 'very high',
                    'disease_resistance' => 'excellent'
                ],
                'performance_standards' => [
                    'peak_production' => '88%',
                    'production_period' => '74 weeks',
                    'feed_efficiency' => 'good'
                ],
                'mature_weight_female' => 2.1,
                'mature_weight_male' => 2.6,
                'egg_production_peak' => 88.0,
                'egg_weight_average' => 65.0,
                'production_period_weeks' => 74,
                'feed_conversion_ratio' => 2.4,
                'mortality_rate_standard' => 3.2,
                'housing_density_recommended' => 6.5,
                'temperature_range_optimal' => [
                    'min' => 15,
                    'max' => 28,
                    'ideal' => 22
                ],
            ],
        ];

        foreach ($breeds as $breedData) {
            Breed::create(array_merge($breedData, [
                'uuid' => Str::uuid(),
                'is_active' => true,
            ]));
        }

        $this->command->info('Breed data seeded successfully!');
        $this->command->info('Created ' . count($breeds) . ' breed records.');
    }
}
