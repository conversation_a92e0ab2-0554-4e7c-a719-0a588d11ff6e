# 08 - POULTRY MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Poultry Management adalah modul khusus untuk industri peternakan ayam yang mengelola farms, houses (kandang), flocks (kelompok ayam), breeds, dan lifecycle management. Modul ini terintegrasi dengan inventory dan production modules.

## 🎯 TUJUAN

- Manajemen farms dan houses dengan detail teknis
- Tracking flocks dari placement hingga disposal
- Breed management dengan performance data
- Capacity management dan utilization tracking
- Environmental monitoring integration
- Biosecurity compliance tracking
- Integration dengan egg production dan feed consumption

## ⏱️ ESTIMASI WAKTU

**Total**: 18-22 jam
- Backend implementation: 12-14 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Industry Expert** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Core poultry tables
poultry_breeds
farms
houses
flocks

-- Supporting tables
companies
regions
users (untuk manager assignments)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Poultry Management module
php artisan module:make PoultryManagement

# Create module components
php artisan module:make-controller PoultryManagement FarmController --api
php artisan module:make-controller PoultryManagement HouseController --api
php artisan module:make-controller PoultryManagement FlockController --api
php artisan module:make-controller PoultryManagement BreedController --api
php artisan module:make-model PoultryManagement Farm
php artisan module:make-model PoultryManagement House
php artisan module:make-model PoultryManagement Flock
php artisan module:make-model PoultryManagement PoultryBreed
php artisan module:make-request PoultryManagement FarmStoreRequest
php artisan module:make-request PoultryManagement FlockStoreRequest
php artisan module:make-resource PoultryManagement FarmResource
php artisan module:make-resource PoultryManagement FlockResource
php artisan module:make-policy PoultryManagement FarmPolicy
php artisan module:make-seeder PoultryManagement PoultrySeeder
```

### **1.2 Farm Model**

```php
<?php
// Modules/PoultryManagement/Entities/Farm.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Farm extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'code',
        'name',
        'description',
        'company_id',
        'region_id',
        'address',
        'coordinates',
        'total_area',
        'area_unit',
        'manager_id',
        'established_date',
        'license_number',
        'license_expiry',
        'biosecurity_level',
        'capacity',
        'status',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'total_area' => 'decimal:2',
        'established_date' => 'date',
        'license_expiry' => 'date',
        'capacity' => 'integer',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'status', 'manager_id', 'biosecurity_level'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function company()
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    public function region()
    {
        return $this->belongsTo(\App\Models\Region::class);
    }

    public function manager()
    {
        return $this->belongsTo(\App\Models\User::class, 'manager_id');
    }

    public function houses()
    {
        return $this->hasMany(House::class);
    }

    public function flocks()
    {
        return $this->hasManyThrough(Flock::class, House::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%")
              ->orWhere('license_number', 'like', "%{$search}%");
        });
    }

    public function scopeByRegion($query, $regionId)
    {
        return $query->where('region_id', $regionId);
    }

    public function scopeByBiosecurityLevel($query, $level)
    {
        return $query->where('biosecurity_level', $level);
    }

    // Accessors & Mutators
    public function getCoordinatesAttribute($value)
    {
        return $value ? json_decode($value, true) : null;
    }

    public function setCoordinatesAttribute($value)
    {
        $this->attributes['coordinates'] = $value ? json_encode($value) : null;
    }

    public function getTotalHousesAttribute(): int
    {
        return $this->houses()->count();
    }

    public function getActiveHousesAttribute(): int
    {
        return $this->houses()->where('status', 'active')->count();
    }

    public function getTotalCapacityAttribute(): int
    {
        return $this->houses()->sum('capacity_birds');
    }

    public function getCurrentPopulationAttribute(): int
    {
        return $this->houses()->sum('current_population');
    }

    public function getUtilizationPercentageAttribute(): float
    {
        $totalCapacity = $this->total_capacity;
        return $totalCapacity > 0 ? ($this->current_population / $totalCapacity) * 100 : 0;
    }

    // Methods
    public function getPerformanceMetrics(string $period = '30d'): array
    {
        $startDate = now()->sub($period);
        
        return [
            'total_houses' => $this->total_houses,
            'active_houses' => $this->active_houses,
            'total_capacity' => $this->total_capacity,
            'current_population' => $this->current_population,
            'utilization_percentage' => $this->utilization_percentage,
            'active_flocks' => $this->flocks()->where('status', 'active')->count(),
            'avg_flock_age' => $this->flocks()
                ->where('status', 'active')
                ->avg(\DB::raw('DATEDIFF(CURDATE(), placement_date) / 7')),
        ];
    }
}
```

### **1.3 House Model**

```php
<?php
// Modules/PoultryManagement/Entities/House.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class House extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'code',
        'name',
        'description',
        'farm_id',
        'house_type',
        'construction_type',
        'length',
        'width',
        'height',
        'capacity_birds',
        'current_population',
        'ventilation_type',
        'lighting_type',
        'feeding_system',
        'watering_system',
        'climate_control',
        'biosecurity_features',
        'equipment_list',
        'status',
        'built_date',
        'last_renovation',
    ];

    protected $casts = [
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'capacity_birds' => 'integer',
        'current_population' => 'integer',
        'climate_control' => 'boolean',
        'biosecurity_features' => 'array',
        'equipment_list' => 'array',
        'built_date' => 'date',
        'last_renovation' => 'date',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'status', 'current_population', 'capacity_birds'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function farm()
    {
        return $this->belongsTo(Farm::class);
    }

    public function flocks()
    {
        return $this->hasMany(Flock::class);
    }

    public function activeFlocks()
    {
        return $this->hasMany(Flock::class)->where('status', 'active');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByFarm($query, $farmId)
    {
        return $query->where('farm_id', $farmId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('house_type', $type);
    }

    public function scopeAvailable($query)
    {
        return $query->whereRaw('current_population < capacity_birds');
    }

    // Accessors
    public function getAreaAttribute(): float
    {
        return $this->length && $this->width ? $this->length * $this->width : 0;
    }

    public function getAvailableCapacityAttribute(): int
    {
        return $this->capacity_birds - $this->current_population;
    }

    public function getUtilizationPercentageAttribute(): float
    {
        return $this->capacity_birds > 0 ? ($this->current_population / $this->capacity_birds) * 100 : 0;
    }

    public function getDensityPerSqmAttribute(): float
    {
        return $this->area > 0 ? $this->current_population / $this->area : 0;
    }

    // Methods
    public function canAccommodate(int $birds): bool
    {
        return $this->available_capacity >= $birds;
    }

    public function addBirds(int $count): void
    {
        $this->increment('current_population', $count);
    }

    public function removeBirds(int $count): void
    {
        $this->decrement('current_population', $count);
    }

    public function getEnvironmentalData(string $period = '24h'): array
    {
        // This would integrate with IoT sensors
        // For now, return mock data
        return [
            'current_temperature' => rand(22, 28),
            'current_humidity' => rand(60, 75),
            'air_quality_index' => rand(80, 95),
            'temperature_data' => $this->generateMockSensorData('temperature', $period),
            'humidity_data' => $this->generateMockSensorData('humidity', $period),
        ];
    }

    private function generateMockSensorData(string $type, string $period): array
    {
        $hours = $period === '24h' ? 24 : ($period === '7d' ? 168 : 720);
        $data = [];
        
        for ($i = 0; $i < $hours; $i++) {
            $timestamp = now()->subHours($hours - $i);
            $value = $type === 'temperature' ? rand(20, 30) : rand(55, 80);
            
            $data[] = [
                'timestamp' => $timestamp->toISOString(),
                'value' => $value
            ];
        }
        
        return $data;
    }
}
```

### **1.4 Flock Model**

```php
<?php
// Modules/PoultryManagement/Entities/Flock.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Flock extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'flock_number',
        'house_id',
        'breed_id',
        'supplier_id',
        'placement_date',
        'initial_count',
        'current_count',
        'mortality_count',
        'culled_count',
        'sold_count',
        'production_stage',
        'expected_production_start',
        'actual_production_start',
        'expected_production_end',
        'vaccination_program_id',
        'feed_program_id',
        'notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'placement_date' => 'date',
        'expected_production_start' => 'date',
        'actual_production_start' => 'date',
        'expected_production_end' => 'date',
        'initial_count' => 'integer',
        'current_count' => 'integer',
        'mortality_count' => 'integer',
        'culled_count' => 'integer',
        'sold_count' => 'integer',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['current_count', 'mortality_count', 'production_stage', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function house()
    {
        return $this->belongsTo(House::class);
    }

    public function breed()
    {
        return $this->belongsTo(PoultryBreed::class, 'breed_id');
    }

    public function supplier()
    {
        return $this->belongsTo(\App\Models\BusinessPartner::class, 'supplier_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    public function eggProductionRecords()
    {
        return $this->hasMany(\Modules\EggProduction\Entities\EggProductionRecord::class);
    }

    public function feedConsumptionRecords()
    {
        return $this->hasMany(\Modules\FeedManagement\Entities\FeedConsumptionRecord::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeByBreed($query, $breedId)
    {
        return $query->where('breed_id', $breedId);
    }

    public function scopeByStage($query, $stage)
    {
        return $query->where('production_stage', $stage);
    }

    public function scopeInProduction($query)
    {
        return $query->whereIn('production_stage', ['layer', 'breeder']);
    }

    // Accessors
    public function getAgeWeeksAttribute(): float
    {
        return $this->placement_date ? $this->placement_date->diffInDays(now()) / 7 : 0;
    }

    public function getAgeDaysAttribute(): int
    {
        return $this->placement_date ? $this->placement_date->diffInDays(now()) : 0;
    }

    public function getMortalityRateAttribute(): float
    {
        return $this->initial_count > 0 ? ($this->mortality_count / $this->initial_count) * 100 : 0;
    }

    public function getSurvivalRateAttribute(): float
    {
        return 100 - $this->mortality_rate;
    }

    public function getIsInProductionAttribute(): bool
    {
        return in_array($this->production_stage, ['layer', 'breeder']) && 
               $this->actual_production_start && 
               $this->actual_production_start <= now();
    }

    // Methods
    public function updatePopulation(int $mortality = 0, int $culled = 0, int $sold = 0): void
    {
        $this->increment('mortality_count', $mortality);
        $this->increment('culled_count', $culled);
        $this->increment('sold_count', $sold);
        
        $totalReduction = $mortality + $culled + $sold;
        $this->decrement('current_count', $totalReduction);
        
        // Update house population
        $this->house->removeBirds($totalReduction);
    }

    public function getProductionSummary(string $period = '30d'): array
    {
        $startDate = now()->sub($period);
        
        $eggRecords = $this->eggProductionRecords()
            ->where('record_date', '>=', $startDate)
            ->get();
            
        $feedRecords = $this->feedConsumptionRecords()
            ->where('record_date', '>=', $startDate)
            ->get();

        return [
            'total_eggs' => $eggRecords->sum('eggs_collected'),
            'avg_laying_rate' => $eggRecords->avg('laying_percentage'),
            'total_feed_consumed' => $feedRecords->sum('quantity_kg'),
            'avg_fcr' => $feedRecords->avg('feed_conversion_ratio'),
            'mortality_count' => $eggRecords->sum('mortality_count'),
            'avg_temperature' => $eggRecords->avg('temperature_max'),
            'avg_humidity' => $eggRecords->avg('humidity_percentage'),
        ];
    }

    public function updateProductionStage(): void
    {
        $ageWeeks = $this->age_weeks;
        
        if ($ageWeeks < 6) {
            $stage = 'chick';
        } elseif ($ageWeeks < 18) {
            $stage = 'grower';
        } elseif ($ageWeeks < 72) {
            $stage = 'layer';
        } else {
            $stage = 'spent';
        }
        
        if ($this->production_stage !== $stage) {
            $this->update(['production_stage' => $stage]);
        }
    }

    public function canStartProduction(): bool
    {
        return $this->age_weeks >= 18 && 
               $this->production_stage === 'grower' && 
               !$this->actual_production_start;
    }

    public function startProduction(): void
    {
        if ($this->canStartProduction()) {
            $this->update([
                'production_stage' => 'layer',
                'actual_production_start' => now()->toDateString()
            ]);
        }
    }
}
```

### **1.5 Flock Controller**

```php
<?php
// Modules/PoultryManagement/Http/Controllers/FlockController.php

namespace Modules\PoultryManagement\Http\Controllers;

use App\Core\API\Controllers\BaseApiController;
use Modules\PoultryManagement\Entities\Flock;
use Modules\PoultryManagement\Http\Requests\FlockStoreRequest;
use Modules\PoultryManagement\Http\Requests\FlockUpdateRequest;
use Modules\PoultryManagement\Http\Resources\FlockResource;
use Modules\PoultryManagement\Events\FlockCreated;
use Modules\PoultryManagement\Events\FlockUpdated;
use Modules\PoultryManagement\Events\FlockDeleted;
use Modules\PoultryManagement\Services\FlockService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class FlockController extends BaseApiController
{
    protected FlockService $flockService;

    public function __construct(FlockService $flockService)
    {
        $this->flockService = $flockService;
    }

    protected array $allowedIncludes = ['house', 'breed', 'supplier', 'house.farm'];
    protected array $allowedFilters = ['search', 'house_id', 'breed_id', 'production_stage', 'status'];
    protected array $allowedSorts = ['flock_number', 'placement_date', 'current_count', 'age_weeks'];

    protected function getModelClass(): string
    {
        return Flock::class;
    }

    protected function getResourceClass(): string
    {
        return FlockResource::class;
    }

    protected function getStoreRules(): array
    {
        return [
            'flock_number' => 'required|string|max:50|unique:flocks',
            'house_id' => 'required|exists:houses,id',
            'breed_id' => 'required|exists:poultry_breeds,id',
            'supplier_id' => 'nullable|exists:business_partners,id',
            'placement_date' => 'required|date',
            'initial_count' => 'required|integer|min:1',
            'expected_production_start' => 'nullable|date|after:placement_date',
            'expected_production_end' => 'nullable|date|after:expected_production_start',
            'vaccination_program_id' => 'nullable|integer',
            'feed_program_id' => 'nullable|integer',
            'notes' => 'nullable|string',
        ];
    }

    protected function getUpdateRules($flock): array
    {
        return [
            'flock_number' => "required|string|max:50|unique:flocks,flock_number,{$flock->id}",
            'house_id' => 'required|exists:houses,id',
            'breed_id' => 'required|exists:poultry_breeds,id',
            'supplier_id' => 'nullable|exists:business_partners,id',
            'placement_date' => 'required|date',
            'current_count' => 'required|integer|min:0',
            'mortality_count' => 'integer|min:0',
            'culled_count' => 'integer|min:0',
            'sold_count' => 'integer|min:0',
            'production_stage' => 'in:chick,grower,layer,breeder,spent',
            'actual_production_start' => 'nullable|date',
            'expected_production_end' => 'nullable|date',
            'status' => 'in:active,completed,sold,culled',
            'notes' => 'nullable|string',
        ];
    }

    protected function performStore(array $data): Flock
    {
        $flock = Flock::create(array_merge($data, [
            'uuid' => Str::uuid(),
            'current_count' => $data['initial_count'],
            'production_stage' => 'chick',
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]));

        // Update house population
        $flock->house->addBirds($data['initial_count']);

        event(new FlockCreated($flock));
        
        return $flock->load('house', 'breed');
    }

    protected function performUpdate($flock, array $data): Flock
    {
        $originalCount = $flock->current_count;
        
        $flock->update(array_merge($data, [
            'updated_by' => auth()->id(),
        ]));

        // Update house population if count changed
        if (isset($data['current_count']) && $data['current_count'] !== $originalCount) {
            $difference = $data['current_count'] - $originalCount;
            if ($difference > 0) {
                $flock->house->addBirds($difference);
            } else {
                $flock->house->removeBirds(abs($difference));
            }
        }

        event(new FlockUpdated($flock));
        
        return $flock->load('house', 'breed');
    }

    protected function performDestroy($flock): void
    {
        // Remove birds from house
        $flock->house->removeBirds($flock->current_count);
        
        $flock->delete();
        event(new FlockDeleted($flock));
    }

    public function updatePopulation(Request $request, Flock $flock)
    {
        $this->authorize('update', $flock);
        
        $validated = $request->validate([
            'mortality_count' => 'integer|min:0',
            'culled_count' => 'integer|min:0',
            'sold_count' => 'integer|min:0',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $flock->updatePopulation(
            $validated['mortality_count'] ?? 0,
            $validated['culled_count'] ?? 0,
            $validated['sold_count'] ?? 0
        );

        // Log the population update
        activity()
            ->performedOn($flock)
            ->withProperties($validated)
            ->log('Population updated');

        return $this->successResponse([
            'data' => new FlockResource($flock->load('house', 'breed')),
            'message' => 'Population updated successfully'
        ]);
    }

    public function startProduction(Flock $flock)
    {
        $this->authorize('update', $flock);
        
        if (!$flock->canStartProduction()) {
            return $this->errorResponse('Flock is not ready for production', 400);
        }

        $flock->startProduction();

        return $this->successResponse([
            'data' => new FlockResource($flock->load('house', 'breed')),
            'message' => 'Production started successfully'
        ]);
    }

    public function productionSummary(Request $request, Flock $flock)
    {
        $this->authorize('view', $flock);
        
        $period = $request->get('period', '30d');
        $summary = $flock->getProductionSummary($period);
        
        return $this->successResponse([
            'data' => $summary
        ]);
    }

    public function performanceMetrics(Request $request)
    {
        $this->authorize('viewAny', Flock::class);
        
        $filters = $request->only(['house_id', 'breed_id', 'production_stage']);
        $period = $request->get('period', '30d');
        
        $metrics = $this->flockService->getPerformanceMetrics($filters, $period);
        
        return $this->successResponse([
            'data' => $metrics
        ]);
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create poultry management structure
mkdir -p frontend/src/modules/poultry-management/{components,pages,hooks,services,types}
```

### **2.2 Poultry Service**

```typescript
// frontend/src/modules/poultry-management/services/poultryService.ts
import { apiClient } from '@/core/api/apiClient';

export interface Farm {
  id: number;
  uuid: string;
  code: string;
  name: string;
  description?: string;
  company?: any;
  region?: any;
  address?: string;
  coordinates?: { lat: number; lng: number };
  total_area?: number;
  area_unit: string;
  manager?: any;
  established_date?: string;
  license_number?: string;
  license_expiry?: string;
  biosecurity_level: 'low' | 'medium' | 'high' | 'maximum';
  capacity?: number;
  status: 'active' | 'inactive' | 'under_construction' | 'closed';
  total_houses?: number;
  active_houses?: number;
  total_capacity?: number;
  current_population?: number;
  utilization_percentage?: number;
  created_at: string;
  updated_at: string;
}

export interface House {
  id: number;
  uuid: string;
  code: string;
  name: string;
  description?: string;
  farm: Farm;
  house_type: 'open' | 'semi_closed' | 'closed' | 'cage' | 'free_range';
  construction_type: 'concrete' | 'wood' | 'metal' | 'mixed';
  length?: number;
  width?: number;
  height?: number;
  area?: number;
  capacity_birds: number;
  current_population: number;
  available_capacity?: number;
  utilization_percentage?: number;
  ventilation_type: 'natural' | 'mechanical' | 'hybrid';
  lighting_type: 'natural' | 'artificial' | 'hybrid';
  feeding_system: 'manual' | 'automatic' | 'semi_automatic';
  watering_system: 'manual' | 'automatic' | 'nipple' | 'bell';
  climate_control: boolean;
  biosecurity_features?: any;
  equipment_list?: any;
  status: 'active' | 'inactive' | 'maintenance' | 'renovation';
  built_date?: string;
  last_renovation?: string;
  flocks?: Flock[];
  created_at: string;
  updated_at: string;
}

export interface Flock {
  id: number;
  uuid: string;
  flock_number: string;
  house: House;
  breed: PoultryBreed;
  supplier?: any;
  placement_date: string;
  initial_count: number;
  current_count: number;
  mortality_count: number;
  culled_count: number;
  sold_count: number;
  age_weeks?: number;
  age_days?: number;
  production_stage: 'chick' | 'grower' | 'layer' | 'breeder' | 'spent';
  expected_production_start?: string;
  actual_production_start?: string;
  expected_production_end?: string;
  mortality_rate?: number;
  survival_rate?: number;
  is_in_production?: boolean;
  notes?: string;
  status: 'active' | 'completed' | 'sold' | 'culled';
  created_at: string;
  updated_at: string;
}

export interface PoultryBreed {
  id: number;
  uuid: string;
  code: string;
  name: string;
  type: 'layer' | 'broiler' | 'breeder' | 'dual_purpose';
  origin_country?: string;
  description?: string;
  characteristics?: any;
  performance_data?: any;
  is_active: boolean;
}

class PoultryService {
  // Farms
  async getFarms(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/farms?${params.toString()}`);
  }

  async getFarm(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/farms/${id}${params}`);
  }

  async createFarm(data: any) {
    return apiClient.post('/farms', data);
  }

  async updateFarm(id: number, data: any) {
    return apiClient.put(`/farms/${id}`, data);
  }

  async deleteFarm(id: number) {
    return apiClient.delete(`/farms/${id}`);
  }

  // Houses
  async getHouses(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/houses?${params.toString()}`);
  }

  async getHouse(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/houses/${id}${params}`);
  }

  async createHouse(data: any) {
    return apiClient.post('/houses', data);
  }

  async updateHouse(id: number, data: any) {
    return apiClient.put(`/houses/${id}`, data);
  }

  async deleteHouse(id: number) {
    return apiClient.delete(`/houses/${id}`);
  }

  async getHouseEnvironmentalData(houseId: number, period: string = '24h') {
    return apiClient.get(`/houses/${houseId}/environmental-data?period=${period}`);
  }

  // Flocks
  async getFlocks(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/flocks?${params.toString()}`);
  }

  async getFlock(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/flocks/${id}${params}`);
  }

  async createFlock(data: any) {
    return apiClient.post('/flocks', data);
  }

  async updateFlock(id: number, data: any) {
    return apiClient.put(`/flocks/${id}`, data);
  }

  async deleteFlock(id: number) {
    return apiClient.delete(`/flocks/${id}`);
  }

  async updateFlockPopulation(flockId: number, data: {
    mortality_count?: number;
    culled_count?: number;
    sold_count?: number;
    reason: string;
    notes?: string;
  }) {
    return apiClient.post(`/flocks/${flockId}/update-population`, data);
  }

  async startFlockProduction(flockId: number) {
    return apiClient.post(`/flocks/${flockId}/start-production`);
  }

  async getFlockProductionSummary(flockId: number, period: string = '30d') {
    return apiClient.get(`/flocks/${flockId}/production-summary?period=${period}`);
  }

  async getFlockPerformanceMetrics(filters: any = {}, period: string = '30d') {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });
    params.append('period', period);

    return apiClient.get(`/flocks/performance-metrics?${params.toString()}`);
  }

  // Breeds
  async getBreeds() {
    return apiClient.get('/breeds');
  }

  async getBreed(id: number) {
    return apiClient.get(`/breeds/${id}`);
  }

  async createBreed(data: any) {
    return apiClient.post('/breeds', data);
  }

  async updateBreed(id: number, data: any) {
    return apiClient.put(`/breeds/${id}`, data);
  }

  async deleteBreed(id: number) {
    return apiClient.delete(`/breeds/${id}`);
  }
}

export const poultryService = new PoultryService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/PoultryManagement/Routes/api.php

use Modules\PoultryManagement\Http\Controllers\FarmController;
use Modules\PoultryManagement\Http\Controllers\HouseController;
use Modules\PoultryManagement\Http\Controllers\FlockController;
use Modules\PoultryManagement\Http\Controllers\BreedController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Farms
    Route::apiResource('farms', FarmController::class);
    Route::get('farms/{farm}/performance', [FarmController::class, 'performanceMetrics']);
    
    // Houses
    Route::apiResource('houses', HouseController::class);
    Route::get('houses/{house}/environmental-data', [HouseController::class, 'environmentalData']);
    Route::get('houses/{house}/utilization', [HouseController::class, 'utilizationHistory']);
    
    // Flocks
    Route::apiResource('flocks', FlockController::class);
    Route::post('flocks/{flock}/update-population', [FlockController::class, 'updatePopulation']);
    Route::post('flocks/{flock}/start-production', [FlockController::class, 'startProduction']);
    Route::get('flocks/{flock}/production-summary', [FlockController::class, 'productionSummary']);
    Route::get('flocks/performance-metrics', [FlockController::class, 'performanceMetrics']);
    
    // Breeds
    Route::apiResource('breeds', BreedController::class);
});
```

### **3.2 Module Configuration**

```json
{
    "name": "PoultryManagement",
    "alias": "poultrymanagement",
    "description": "Poultry farm and flock management module",
    "keywords": ["poultry", "farms", "houses", "flocks", "breeds"],
    "priority": 8,
    "providers": [
        "Modules\\PoultryManagement\\Providers\\PoultryManagementServiceProvider"
    ],
    "aliases": {},
    "files": [],
    "requires": ["Inventory"]
}
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Farm CRUD operations working
- [ ] House management functional
- [ ] Flock lifecycle tracking working
- [ ] Population updates accurate
- [ ] Production stage transitions working
- [ ] Performance metrics calculation correct

### **Frontend**
- [ ] Farm management interface working
- [ ] House dashboard functional
- [ ] Flock management working
- [ ] Environmental monitoring display
- [ ] Performance metrics visualization
- [ ] Real-time data updates

### **Integration**
- [ ] Integration dengan inventory module
- [ ] Population tracking accurate
- [ ] Environmental data integration
- [ ] Performance calculations correct

## 📞 NEXT STEPS

Setelah Poultry Management module selesai:

1. **Test farm dan house management** end-to-end
2. **Verify flock lifecycle tracking** accuracy
3. **Test population updates** dan calculations
4. **Commit module** ke repository
5. **Lanjut ke** `09_EGG_PRODUCTION.md` untuk implementasi egg production module

---

**IMPORTANT**: Poultry Management adalah core module untuk industri peternakan. Pastikan semua tracking dan calculations akurat sebelum melanjutkan ke production modules.
