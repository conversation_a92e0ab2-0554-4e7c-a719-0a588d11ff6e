# 12 - FINANCIAL REPORTING MODULE

## 📋 OVERVIEW

Modul Financial Reporting menyediakan comprehensive financial analysis dan reporting untuk peternakan ayam. Modul ini mengintegrasikan data dari accounting, sales, purchasing, production, dan operational modules untuk mengh<PERSON>lk<PERSON> insights finansial yang mendalam.

## 🎯 TUJUAN

- Comprehensive financial dashboard dengan real-time metrics
- Profit & Loss analysis per flock, house, dan farm
- Cost center analysis dan cost allocation
- Production cost calculation dan profitability analysis
- Cash flow forecasting dan budget variance analysis
- ROI calculation untuk investment decisions
- Regulatory compliance reporting

## ⏱️ ESTIMASI WAKTU

**Total**: 16-20 jam
- Backend implementation: 10-12 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Financial Analyst** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Financial reporting
financial_reports
budget_plans
budget_variances
cost_centers
cost_allocations

-- Performance metrics
profitability_analysis
cash_flow_projections
financial_kpis

-- Supporting tables
chart_of_accounts (dari Accounting)
journal_entries (dari Accounting)
sales_orders (dari Sales)
purchase_orders (dari Purchasing)
production_records (dari Production)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Financial Reporting module
php artisan module:make FinancialReporting

# Create module components
php artisan module:make-controller FinancialReporting FinancialDashboardController --api
php artisan module:make-controller FinancialReporting ProfitabilityController --api
php artisan module:make-controller FinancialReporting CostAnalysisController --api
php artisan module:make-controller FinancialReporting BudgetController --api
php artisan module:make-controller FinancialReporting CashFlowController --api
php artisan module:make-model FinancialReporting FinancialReport
php artisan module:make-model FinancialReporting BudgetPlan
php artisan module:make-model FinancialReporting CostCenter
php artisan module:make-model FinancialReporting ProfitabilityAnalysis
php artisan module:make-request FinancialReporting BudgetPlanStoreRequest
php artisan module:make-resource FinancialReporting FinancialReportResource
php artisan module:make-resource FinancialReporting ProfitabilityResource
php artisan module:make-policy FinancialReporting FinancialReportingPolicy
php artisan module:make-seeder FinancialReporting FinancialReportingSeeder
```

### **1.2 Financial Reporting Service**

```php
<?php
// Modules/FinancialReporting/Services/FinancialReportingService.php

namespace Modules\FinancialReporting\Services;

use Modules\Accounting\Entities\ChartOfAccount;
use Modules\Accounting\Entities\JournalEntry;
use Modules\Sales\Entities\SalesOrder;
use Modules\Purchasing\Entities\PurchaseOrder;
use Modules\EggProduction\Entities\EggProductionRecord;
use Modules\FeedManagement\Entities\FeedConsumptionRecord;
use Modules\PoultryManagement\Entities\Flock;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinancialReportingService
{
    public function getFinancialDashboard(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();
        $farmId = $filters['farm_id'] ?? null;

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'revenue_metrics' => $this->getRevenueMetrics($startDate, $endDate, $farmId),
            'cost_metrics' => $this->getCostMetrics($startDate, $endDate, $farmId),
            'profitability_metrics' => $this->getProfitabilityMetrics($startDate, $endDate, $farmId),
            'production_metrics' => $this->getProductionMetrics($startDate, $endDate, $farmId),
            'cash_flow_summary' => $this->getCashFlowSummary($startDate, $endDate, $farmId),
            'kpi_summary' => $this->getKPISummary($startDate, $endDate, $farmId),
        ];
    }

    private function getRevenueMetrics(string $startDate, string $endDate, ?int $farmId): array
    {
        $salesQuery = SalesOrder::whereBetween('order_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled');

        if ($farmId) {
            // Filter by farm through customer relationship or other logic
        }

        $sales = $salesQuery->get();
        $totalRevenue = $sales->sum('total_amount');
        $previousPeriodStart = Carbon::parse($startDate)->subDays(Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate)))->toDateString();
        $previousPeriodEnd = Carbon::parse($startDate)->subDay()->toDateString();
        
        $previousSales = SalesOrder::whereBetween('order_date', [$previousPeriodStart, $previousPeriodEnd])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        $revenueGrowth = $previousSales > 0 ? (($totalRevenue - $previousSales) / $previousSales) * 100 : 0;

        return [
            'total_revenue' => $totalRevenue,
            'average_order_value' => $sales->count() > 0 ? $totalRevenue / $sales->count() : 0,
            'revenue_growth_percentage' => $revenueGrowth,
            'total_orders' => $sales->count(),
            'revenue_by_customer' => $this->getRevenueByCustomer($sales),
            'revenue_trend' => $this->getRevenueTrend($startDate, $endDate, $farmId),
        ];
    }

    private function getCostMetrics(string $startDate, string $endDate, ?int $farmId): array
    {
        // Feed costs
        $feedCosts = FeedConsumptionRecord::whereBetween('consumption_date', [$startDate, $endDate])
            ->sum(DB::raw('quantity_kg * (SELECT cost_per_kg FROM feed_formulations WHERE id = formulation_id)'));

        // Purchase costs
        $purchaseCosts = PurchaseOrder::whereBetween('order_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        // Operating expenses from accounting
        $operatingExpenses = $this->getOperatingExpenses($startDate, $endDate);

        $totalCosts = $feedCosts + $purchaseCosts + $operatingExpenses;

        return [
            'total_costs' => $totalCosts,
            'feed_costs' => $feedCosts,
            'purchase_costs' => $purchaseCosts,
            'operating_expenses' => $operatingExpenses,
            'cost_breakdown' => [
                'feed_percentage' => $totalCosts > 0 ? ($feedCosts / $totalCosts) * 100 : 0,
                'purchase_percentage' => $totalCosts > 0 ? ($purchaseCosts / $totalCosts) * 100 : 0,
                'operating_percentage' => $totalCosts > 0 ? ($operatingExpenses / $totalCosts) * 100 : 0,
            ],
            'cost_per_bird' => $this->getCostPerBird($startDate, $endDate, $farmId),
            'cost_trend' => $this->getCostTrend($startDate, $endDate, $farmId),
        ];
    }

    private function getProfitabilityMetrics(string $startDate, string $endDate, ?int $farmId): array
    {
        $revenue = $this->getRevenueMetrics($startDate, $endDate, $farmId)['total_revenue'];
        $costs = $this->getCostMetrics($startDate, $endDate, $farmId)['total_costs'];
        
        $grossProfit = $revenue - $costs;
        $grossMargin = $revenue > 0 ? ($grossProfit / $revenue) * 100 : 0;

        return [
            'gross_profit' => $grossProfit,
            'gross_margin_percentage' => $grossMargin,
            'net_profit' => $this->getNetProfit($startDate, $endDate, $farmId),
            'ebitda' => $this->getEBITDA($startDate, $endDate, $farmId),
            'roi_percentage' => $this->getROI($startDate, $endDate, $farmId),
            'profit_per_bird' => $this->getProfitPerBird($startDate, $endDate, $farmId),
            'profitability_by_flock' => $this->getProfitabilityByFlock($startDate, $endDate, $farmId),
        ];
    }

    private function getProductionMetrics(string $startDate, string $endDate, ?int $farmId): array
    {
        $productionQuery = EggProductionRecord::whereBetween('record_date', [$startDate, $endDate]);
        
        if ($farmId) {
            $productionQuery->whereHas('house', function ($query) use ($farmId) {
                $query->where('farm_id', $farmId);
            });
        }

        $production = $productionQuery->get();
        $totalEggs = $production->sum('eggs_collected');
        $totalFeedConsumed = $production->sum('feed_consumed_kg');

        return [
            'total_eggs_produced' => $totalEggs,
            'average_daily_production' => $this->getAverageDailyProduction($production),
            'feed_conversion_ratio' => $totalEggs > 0 ? $totalFeedConsumed / $totalEggs : 0,
            'production_efficiency' => $this->getProductionEfficiency($startDate, $endDate, $farmId),
            'mortality_rate' => $this->getMortalityRate($startDate, $endDate, $farmId),
            'production_cost_per_egg' => $this->getProductionCostPerEgg($startDate, $endDate, $farmId),
        ];
    }

    public function getFlockProfitability(int $flockId, string $startDate, string $endDate): array
    {
        $flock = Flock::findOrFail($flockId);
        
        // Revenue from egg sales
        $eggProduction = EggProductionRecord::where('flock_id', $flockId)
            ->whereBetween('record_date', [$startDate, $endDate])
            ->sum('eggs_collected');

        $averageEggPrice = $this->getAverageEggPrice($startDate, $endDate);
        $revenue = $eggProduction * $averageEggPrice;

        // Costs
        $feedCosts = FeedConsumptionRecord::where('flock_id', $flockId)
            ->whereBetween('consumption_date', [$startDate, $endDate])
            ->sum('feed_cost');

        $healthCosts = $this->getFlockHealthCosts($flockId, $startDate, $endDate);
        $laborCosts = $this->getFlockLaborCosts($flockId, $startDate, $endDate);
        $overheadCosts = $this->getFlockOverheadCosts($flockId, $startDate, $endDate);

        $totalCosts = $feedCosts + $healthCosts + $laborCosts + $overheadCosts;
        $profit = $revenue - $totalCosts;

        return [
            'flock_number' => $flock->flock_number,
            'period' => ['start' => $startDate, 'end' => $endDate],
            'revenue' => [
                'total_revenue' => $revenue,
                'eggs_produced' => $eggProduction,
                'average_egg_price' => $averageEggPrice,
                'revenue_per_bird' => $flock->current_count > 0 ? $revenue / $flock->current_count : 0,
            ],
            'costs' => [
                'total_costs' => $totalCosts,
                'feed_costs' => $feedCosts,
                'health_costs' => $healthCosts,
                'labor_costs' => $laborCosts,
                'overhead_costs' => $overheadCosts,
                'cost_per_bird' => $flock->current_count > 0 ? $totalCosts / $flock->current_count : 0,
                'cost_per_egg' => $eggProduction > 0 ? $totalCosts / $eggProduction : 0,
            ],
            'profitability' => [
                'gross_profit' => $profit,
                'profit_margin' => $revenue > 0 ? ($profit / $revenue) * 100 : 0,
                'profit_per_bird' => $flock->current_count > 0 ? $profit / $flock->current_count : 0,
                'profit_per_egg' => $eggProduction > 0 ? $profit / $eggProduction : 0,
            ],
            'performance_metrics' => [
                'feed_conversion_ratio' => $this->getFlockFCR($flockId, $startDate, $endDate),
                'laying_percentage' => $this->getFlockLayingPercentage($flockId, $startDate, $endDate),
                'mortality_rate' => $this->getFlockMortalityRate($flockId, $startDate, $endDate),
            ],
        ];
    }

    public function getCostCenterAnalysis(string $startDate, string $endDate): array
    {
        $costCenters = [
            'feed_management' => $this->getFeedManagementCosts($startDate, $endDate),
            'health_management' => $this->getHealthManagementCosts($startDate, $endDate),
            'labor' => $this->getLaborCosts($startDate, $endDate),
            'utilities' => $this->getUtilityCosts($startDate, $endDate),
            'maintenance' => $this->getMaintenanceCosts($startDate, $endDate),
            'administration' => $this->getAdministrationCosts($startDate, $endDate),
        ];

        $totalCosts = array_sum($costCenters);

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'cost_centers' => array_map(function ($cost) use ($totalCosts) {
                return [
                    'amount' => $cost,
                    'percentage' => $totalCosts > 0 ? ($cost / $totalCosts) * 100 : 0,
                ];
            }, $costCenters),
            'total_costs' => $totalCosts,
            'cost_per_bird' => $this->getTotalBirdCount() > 0 ? $totalCosts / $this->getTotalBirdCount() : 0,
        ];
    }

    public function getCashFlowProjection(int $months = 12): array
    {
        $projections = [];
        $currentDate = now()->startOfMonth();

        for ($i = 0; $i < $months; $i++) {
            $monthStart = $currentDate->copy()->addMonths($i);
            $monthEnd = $monthStart->copy()->endOfMonth();

            $projections[] = [
                'month' => $monthStart->format('Y-m'),
                'projected_revenue' => $this->getProjectedRevenue($monthStart, $monthEnd),
                'projected_costs' => $this->getProjectedCosts($monthStart, $monthEnd),
                'projected_cash_flow' => $this->getProjectedCashFlow($monthStart, $monthEnd),
                'cumulative_cash_flow' => $this->getCumulativeCashFlow($monthStart),
            ];
        }

        return [
            'projections' => $projections,
            'assumptions' => $this->getCashFlowAssumptions(),
            'risk_factors' => $this->getCashFlowRiskFactors(),
        ];
    }

    // Helper methods
    private function getOperatingExpenses(string $startDate, string $endDate): float
    {
        return JournalEntry::whereBetween('transaction_date', [$startDate, $endDate])
            ->whereHas('lines.account', function ($query) {
                $query->where('account_type_id', 5); // Expense accounts
            })
            ->sum('total_debit');
    }

    private function getAverageEggPrice(string $startDate, string $endDate): float
    {
        // This would typically come from sales data or market prices
        // For now, return a default value
        return 0.15; // $0.15 per egg
    }

    private function getFlockHealthCosts(int $flockId, string $startDate, string $endDate): float
    {
        // Calculate health-related costs for the flock
        return 0; // Placeholder
    }

    private function getFlockLaborCosts(int $flockId, string $startDate, string $endDate): float
    {
        // Calculate labor costs allocated to the flock
        return 0; // Placeholder
    }

    private function getFlockOverheadCosts(int $flockId, string $startDate, string $endDate): float
    {
        // Calculate overhead costs allocated to the flock
        return 0; // Placeholder
    }

    private function getFlockFCR(int $flockId, string $startDate, string $endDate): float
    {
        $feedRecords = FeedConsumptionRecord::where('flock_id', $flockId)
            ->whereBetween('consumption_date', [$startDate, $endDate])
            ->get();

        return $feedRecords->avg('feed_conversion_ratio') ?: 0;
    }

    private function getFlockLayingPercentage(int $flockId, string $startDate, string $endDate): float
    {
        $productionRecords = EggProductionRecord::where('flock_id', $flockId)
            ->whereBetween('record_date', [$startDate, $endDate])
            ->get();

        return $productionRecords->avg('laying_percentage') ?: 0;
    }

    private function getFlockMortalityRate(int $flockId, string $startDate, string $endDate): float
    {
        $productionRecords = EggProductionRecord::where('flock_id', $flockId)
            ->whereBetween('record_date', [$startDate, $endDate])
            ->get();

        $totalMortality = $productionRecords->sum('mortality_count');
        $flock = Flock::find($flockId);
        
        return $flock && $flock->initial_count > 0 ? ($totalMortality / $flock->initial_count) * 100 : 0;
    }

    // Additional helper methods would be implemented here...
    private function getRevenueByCustomer($sales): array { return []; }
    private function getRevenueTrend(string $startDate, string $endDate, ?int $farmId): array { return []; }
    private function getCostPerBird(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getCostTrend(string $startDate, string $endDate, ?int $farmId): array { return []; }
    private function getNetProfit(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getEBITDA(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getROI(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getProfitPerBird(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getProfitabilityByFlock(string $startDate, string $endDate, ?int $farmId): array { return []; }
    private function getAverageDailyProduction($production): float { return 0; }
    private function getProductionEfficiency(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getMortalityRate(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getProductionCostPerEgg(string $startDate, string $endDate, ?int $farmId): float { return 0; }
    private function getCashFlowSummary(string $startDate, string $endDate, ?int $farmId): array { return []; }
    private function getKPISummary(string $startDate, string $endDate, ?int $farmId): array { return []; }
    private function getFeedManagementCosts(string $startDate, string $endDate): float { return 0; }
    private function getHealthManagementCosts(string $startDate, string $endDate): float { return 0; }
    private function getLaborCosts(string $startDate, string $endDate): float { return 0; }
    private function getUtilityCosts(string $startDate, string $endDate): float { return 0; }
    private function getMaintenanceCosts(string $startDate, string $endDate): float { return 0; }
    private function getAdministrationCosts(string $startDate, string $endDate): float { return 0; }
    private function getTotalBirdCount(): int { return 0; }
    private function getProjectedRevenue($monthStart, $monthEnd): float { return 0; }
    private function getProjectedCosts($monthStart, $monthEnd): float { return 0; }
    private function getProjectedCashFlow($monthStart, $monthEnd): float { return 0; }
    private function getCumulativeCashFlow($monthStart): float { return 0; }
    private function getCashFlowAssumptions(): array { return []; }
    private function getCashFlowRiskFactors(): array { return []; }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create financial reporting structure
mkdir -p frontend/src/modules/financial-reporting/{components,pages,hooks,services,types}
```

### **2.2 Financial Reporting Service**

```typescript
// frontend/src/modules/financial-reporting/services/financialReportingService.ts
import { apiClient } from '@/core/api/apiClient';

export interface FinancialDashboard {
  period: { start: string; end: string };
  revenue_metrics: RevenueMetrics;
  cost_metrics: CostMetrics;
  profitability_metrics: ProfitabilityMetrics;
  production_metrics: ProductionMetrics;
  cash_flow_summary: CashFlowSummary;
  kpi_summary: KPISummary;
}

export interface RevenueMetrics {
  total_revenue: number;
  average_order_value: number;
  revenue_growth_percentage: number;
  total_orders: number;
  revenue_by_customer: any[];
  revenue_trend: any[];
}

export interface CostMetrics {
  total_costs: number;
  feed_costs: number;
  purchase_costs: number;
  operating_expenses: number;
  cost_breakdown: {
    feed_percentage: number;
    purchase_percentage: number;
    operating_percentage: number;
  };
  cost_per_bird: number;
  cost_trend: any[];
}

export interface ProfitabilityMetrics {
  gross_profit: number;
  gross_margin_percentage: number;
  net_profit: number;
  ebitda: number;
  roi_percentage: number;
  profit_per_bird: number;
  profitability_by_flock: any[];
}

export interface FlockProfitability {
  flock_number: string;
  period: { start: string; end: string };
  revenue: {
    total_revenue: number;
    eggs_produced: number;
    average_egg_price: number;
    revenue_per_bird: number;
  };
  costs: {
    total_costs: number;
    feed_costs: number;
    health_costs: number;
    labor_costs: number;
    overhead_costs: number;
    cost_per_bird: number;
    cost_per_egg: number;
  };
  profitability: {
    gross_profit: number;
    profit_margin: number;
    profit_per_bird: number;
    profit_per_egg: number;
  };
  performance_metrics: {
    feed_conversion_ratio: number;
    laying_percentage: number;
    mortality_rate: number;
  };
}

class FinancialReportingService {
  // Financial Dashboard
  async getFinancialDashboard(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/financial-dashboard?${params.toString()}`);
  }

  // Profitability Analysis
  async getFlockProfitability(flockId: number, startDate: string, endDate: string) {
    return apiClient.get(`/flock-profitability/${flockId}?start_date=${startDate}&end_date=${endDate}`);
  }

  async getFarmProfitability(farmId: number, startDate: string, endDate: string) {
    return apiClient.get(`/farm-profitability/${farmId}?start_date=${startDate}&end_date=${endDate}`);
  }

  async getHouseProfitability(houseId: number, startDate: string, endDate: string) {
    return apiClient.get(`/house-profitability/${houseId}?start_date=${startDate}&end_date=${endDate}`);
  }

  // Cost Analysis
  async getCostCenterAnalysis(startDate: string, endDate: string) {
    return apiClient.get(`/cost-center-analysis?start_date=${startDate}&end_date=${endDate}`);
  }

  async getCostBreakdown(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/cost-breakdown?${params.toString()}`);
  }

  async getFeedCostAnalysis(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/feed-cost-analysis?${params.toString()}`);
  }

  // Budget Management
  async getBudgetPlans(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/budget-plans?${params.toString()}`);
  }

  async createBudgetPlan(data: {
    name: string;
    description?: string;
    budget_year: number;
    farm_id?: number;
    budget_items: Array<{
      category: string;
      subcategory?: string;
      budgeted_amount: number;
      notes?: string;
    }>;
  }) {
    return apiClient.post('/budget-plans', data);
  }

  async getBudgetVariance(budgetId: number, period: string) {
    return apiClient.get(`/budget-variance/${budgetId}?period=${period}`);
  }

  // Cash Flow
  async getCashFlowProjection(months: number = 12) {
    return apiClient.get(`/cash-flow-projection?months=${months}`);
  }

  async getCashFlowStatement(startDate: string, endDate: string) {
    return apiClient.get(`/cash-flow-statement?start_date=${startDate}&end_date=${endDate}`);
  }

  async getCashFlowForecast(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/cash-flow-forecast?${params.toString()}`);
  }

  // Financial Reports
  async getIncomeStatement(startDate: string, endDate: string, farmId?: number) {
    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);
    if (farmId) params.append('farm_id', farmId.toString());

    return apiClient.get(`/income-statement?${params.toString()}`);
  }

  async getBalanceSheet(asOfDate: string, farmId?: number) {
    const params = new URLSearchParams();
    params.append('as_of_date', asOfDate);
    if (farmId) params.append('farm_id', farmId.toString());

    return apiClient.get(`/balance-sheet?${params.toString()}`);
  }

  async getTrialBalance(asOfDate: string) {
    return apiClient.get(`/trial-balance?as_of_date=${asOfDate}`);
  }

  // KPI Analysis
  async getFinancialKPIs(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/financial-kpis?${params.toString()}`);
  }

  async getProductionKPIs(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/production-kpis?${params.toString()}`);
  }

  async getROIAnalysis(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/roi-analysis?${params.toString()}`);
  }

  // Comparative Analysis
  async getComparativeAnalysis(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/comparative-analysis?${params.toString()}`);
  }

  async getBenchmarkAnalysis(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/benchmark-analysis?${params.toString()}`);
  }

  // Export Functions
  async exportFinancialReport(reportType: string, filters: any = {}, format: 'pdf' | 'excel' = 'pdf') {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });
    params.append('format', format);

    return apiClient.get(`/export/${reportType}?${params.toString()}`, {
      responseType: 'blob'
    });
  }
}

export const financialReportingService = new FinancialReportingService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/FinancialReporting/Routes/api.php

use Modules\FinancialReporting\Http\Controllers\FinancialDashboardController;
use Modules\FinancialReporting\Http\Controllers\ProfitabilityController;
use Modules\FinancialReporting\Http\Controllers\CostAnalysisController;
use Modules\FinancialReporting\Http\Controllers\BudgetController;
use Modules\FinancialReporting\Http\Controllers\CashFlowController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Financial Dashboard
    Route::get('financial-dashboard', [FinancialDashboardController::class, 'dashboard']);
    Route::get('financial-kpis', [FinancialDashboardController::class, 'kpis']);
    Route::get('production-kpis', [FinancialDashboardController::class, 'productionKpis']);
    
    // Profitability Analysis
    Route::get('flock-profitability/{flock}', [ProfitabilityController::class, 'flockProfitability']);
    Route::get('farm-profitability/{farm}', [ProfitabilityController::class, 'farmProfitability']);
    Route::get('house-profitability/{house}', [ProfitabilityController::class, 'houseProfitability']);
    Route::get('roi-analysis', [ProfitabilityController::class, 'roiAnalysis']);
    
    // Cost Analysis
    Route::get('cost-center-analysis', [CostAnalysisController::class, 'costCenterAnalysis']);
    Route::get('cost-breakdown', [CostAnalysisController::class, 'costBreakdown']);
    Route::get('feed-cost-analysis', [CostAnalysisController::class, 'feedCostAnalysis']);
    
    // Budget Management
    Route::apiResource('budget-plans', BudgetController::class);
    Route::get('budget-variance/{budget}', [BudgetController::class, 'variance']);
    
    // Cash Flow
    Route::get('cash-flow-projection', [CashFlowController::class, 'projection']);
    Route::get('cash-flow-statement', [CashFlowController::class, 'statement']);
    Route::get('cash-flow-forecast', [CashFlowController::class, 'forecast']);
    
    // Financial Reports
    Route::get('income-statement', [FinancialDashboardController::class, 'incomeStatement']);
    Route::get('balance-sheet', [FinancialDashboardController::class, 'balanceSheet']);
    Route::get('trial-balance', [FinancialDashboardController::class, 'trialBalance']);
    
    // Comparative Analysis
    Route::get('comparative-analysis', [ProfitabilityController::class, 'comparativeAnalysis']);
    Route::get('benchmark-analysis', [ProfitabilityController::class, 'benchmarkAnalysis']);
    
    // Export
    Route::get('export/{reportType}', [FinancialDashboardController::class, 'exportReport']);
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Financial dashboard working
- [ ] Profitability calculations accurate
- [ ] Cost center analysis functional
- [ ] Budget variance tracking
- [ ] Cash flow projections working
- [ ] Integration dengan all modules

### **Frontend**
- [ ] Financial dashboard interface
- [ ] Profitability analysis charts
- [ ] Cost breakdown visualization
- [ ] Budget management interface
- [ ] Cash flow projections
- [ ] Export functionality working

### **Integration**
- [ ] Data aggregation accurate
- [ ] Real-time calculations
- [ ] Cross-module data consistency
- [ ] Performance optimization
- [ ] Report generation working

## 📞 NEXT STEPS

Setelah Financial Reporting module selesai:

1. **Test financial calculations** accuracy
2. **Verify data integration** dari semua modules
3. **Test report generation**
4. **Validate KPI calculations**
5. **Commit module** ke repository
6. **Lanjut ke** `13_MOBILE_APP.md`

---

**IMPORTANT**: Financial Reporting adalah decision-making tool utama. Pastikan semua calculations akurat dan data integration sempurna untuk business intelligence yang reliable.
