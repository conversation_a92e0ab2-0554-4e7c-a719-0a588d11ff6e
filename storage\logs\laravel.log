[2025-07-17 22:10:51] local.ERROR: rename(C:\laragon\www\neomuria2025_augment\bootstrap\cache\ser11F5.tmp,C:\laragon\www\neomuria2025_augment\bootstrap\cache/services.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(C:\\laragon\\www\\neomuria2025_augment\\bootstrap\\cache\\ser11F5.tmp,C:\\laragon\\www\\neomuria2025_augment\\bootstrap\\cache/services.php): Access is denied (code: 5) at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(C:\\\\larag...', 'C:\\\\laragon\\\\www\\\\...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(C:\\\\larag...', 'C:\\\\laragon\\\\www\\\\...', 233)
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('C:\\\\laragon\\\\www\\\\...', 'C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(192): Illuminate\\Filesystem\\Filesystem->replace('C:\\\\laragon\\\\www\\\\...', '<?php return ar...')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(163): Illuminate\\Foundation\\ProviderRepository->writeManifest(Array)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load(Array)
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#11 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-07-17 22:10:51] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(34): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(112): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(91): Illuminate\\Redis\\RedisManager->resolve('cache')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(330): Illuminate\\Redis\\RedisManager->connection('cache')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(65): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(98): Illuminate\\Cache\\RedisStore->get('telescope:dump-...')
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->get('telescope:dump-...')
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Watchers\\DumpWatcher.php(47): Illuminate\\Cache\\CacheManager->__call('get', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\RegistersWatchers.php(48): Laravel\\Telescope\\Watchers\\DumpWatcher->register(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(142): Laravel\\Telescope\\Telescope::registerWatchers(Object(Illuminate\\Foundation\\Application))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\TelescopeServiceProvider.php(33): Laravel\\Telescope\\Telescope::start(Object(Illuminate\\Foundation\\Application))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\TelescopeServiceProvider->boot()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Laravel\\Telescope\\TelescopeServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Laravel\\Telescope\\TelescopeServiceProvider), 20)
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-17 22:16:19] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(34): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(112): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(91): Illuminate\\Redis\\RedisManager->resolve('cache')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(330): Illuminate\\Redis\\RedisManager->connection('cache')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(65): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(98): Illuminate\\Cache\\RedisStore->get('telescope:dump-...')
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->get('telescope:dump-...')
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Watchers\\DumpWatcher.php(47): Illuminate\\Cache\\CacheManager->__call('get', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\RegistersWatchers.php(48): Laravel\\Telescope\\Watchers\\DumpWatcher->register(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(142): Laravel\\Telescope\\Telescope::registerWatchers(Object(Illuminate\\Foundation\\Application))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\TelescopeServiceProvider.php(33): Laravel\\Telescope\\Telescope::start(Object(Illuminate\\Foundation\\Application))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\TelescopeServiceProvider->boot()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Laravel\\Telescope\\TelescopeServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Laravel\\Telescope\\TelescopeServiceProvider), 20)
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-17 22:16:38] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(34): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(112): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(91): Illuminate\\Redis\\RedisManager->resolve('cache')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(330): Illuminate\\Redis\\RedisManager->connection('cache')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(65): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(98): Illuminate\\Cache\\RedisStore->get('telescope:dump-...')
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->get('telescope:dump-...')
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Watchers\\DumpWatcher.php(47): Illuminate\\Cache\\CacheManager->__call('get', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\RegistersWatchers.php(48): Laravel\\Telescope\\Watchers\\DumpWatcher->register(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(142): Laravel\\Telescope\\Telescope::registerWatchers(Object(Illuminate\\Foundation\\Application))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\TelescopeServiceProvider.php(33): Laravel\\Telescope\\Telescope::start(Object(Illuminate\\Foundation\\Application))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\TelescopeServiceProvider->boot()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Laravel\\Telescope\\TelescopeServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Laravel\\Telescope\\TelescopeServiceProvider), 20)
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-17 22:17:03] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(34): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(112): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(91): Illuminate\\Redis\\RedisManager->resolve('cache')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(330): Illuminate\\Redis\\RedisManager->connection('cache')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(65): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(98): Illuminate\\Cache\\RedisStore->get('telescope:dump-...')
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->get('telescope:dump-...')
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Watchers\\DumpWatcher.php(47): Illuminate\\Cache\\CacheManager->__call('get', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\RegistersWatchers.php(48): Laravel\\Telescope\\Watchers\\DumpWatcher->register(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(142): Laravel\\Telescope\\Telescope::registerWatchers(Object(Illuminate\\Foundation\\Application))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\TelescopeServiceProvider.php(33): Laravel\\Telescope\\Telescope::start(Object(Illuminate\\Foundation\\Application))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\TelescopeServiceProvider->boot()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Laravel\\Telescope\\TelescopeServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Laravel\\Telescope\\TelescopeServiceProvider), 20)
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-07-17 22:17:33] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f6a566c-e93a-44d4-ab6d-695b6332053b, {"name":"cache:clearing","payload":[null,[]],"listeners":[],"broadcast":false,"hostname":"BIEGYE"}, 2025-07-17 22:17:33, ?, event, 9f6a566c-dbdc-423f-800c-eda7ec5bac23), (9f6a566c-e93a-44d4-ab6d-695b6332053b, {"name":"cache:cleared","payload":[null,[]],"listeners":[],"broadcast":false,"hostname":"BIEGYE"}, 2025-07-17 22:17:33, ?, event, 9f6a566c-e286-4fc6-bb07-59599f412a52), (9f6a566c-e93a-44d4-ab6d-695b6332053b, {"command":"cache:clear","exit_code":0,"arguments":{"command":"cache:clear","store":null},"options":{"tags":null,"help":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"BIEGYE"}, 2025-07-17 22:17:33, ?, command, 9f6a566c-e82e-4b9e-8e68-ed0934694097)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f6a566c-e93a-44d4-ab6d-695b6332053b, {\"name\":\"cache:clearing\",\"payload\":[null,[]],\"listeners\":[],\"broadcast\":false,\"hostname\":\"BIEGYE\"}, 2025-07-17 22:17:33, ?, event, 9f6a566c-dbdc-423f-800c-eda7ec5bac23), (9f6a566c-e93a-44d4-ab6d-695b6332053b, {\"name\":\"cache:cleared\",\"payload\":[null,[]],\"listeners\":[],\"broadcast\":false,\"hostname\":\"BIEGYE\"}, 2025-07-17 22:17:33, ?, event, 9f6a566c-e286-4fc6-bb07-59599f412a52), (9f6a566c-e93a-44d4-ab6d-695b6332053b, {\"command\":\"cache:clear\",\"exit_code\":0,\"arguments\":{\"command\":\"cache:clear\",\"store\":null},\"options\":{\"tags\":null,\"help\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"BIEGYE\"}, 2025-07-17 22:17:33, ?, command, 9f6a566c-e82e-4b9e-8e68-ed0934694097)) at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(50): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3500): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(149): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(150): Illuminate\\Support\\Collection->each(Object(Closure))
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(668): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(293): call_user_func(Object(Closure))
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(686): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1303): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#18 C:\\laragon\\www\\neomuria2025_augment\\artisan(51): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `te...')
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(50): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3500): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(149): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(150): Illuminate\\Support\\Collection->each(Object(Closure))
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(668): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(293): call_user_func(Object(Closure))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(686): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1303): Illuminate\\Container\\Container->call(Object(Closure))
#19 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#20 C:\\laragon\\www\\neomuria2025_augment\\artisan(51): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 {main}
"} 
[2025-07-17 22:17:38] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f6a5674-d35b-47a0-9d81-a3a553dda024, {"command":"config:clear","exit_code":0,"arguments":{"command":"config:clear"},"options":{"help":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"BIEGYE"}, 2025-07-17 22:17:38, ?, command, 9f6a5674-cead-434a-a39d-e361f998bbc2)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f6a5674-d35b-47a0-9d81-a3a553dda024, {\"command\":\"config:clear\",\"exit_code\":0,\"arguments\":{\"command\":\"config:clear\"},\"options\":{\"help\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"BIEGYE\"}, 2025-07-17 22:17:38, ?, command, 9f6a5674-cead-434a-a39d-e361f998bbc2)) at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(50): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3500): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(149): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(150): Illuminate\\Support\\Collection->each(Object(Closure))
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(668): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(293): call_user_func(Object(Closure))
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(686): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1303): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#18 C:\\laragon\\www\\neomuria2025_augment\\artisan(51): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `te...')
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(50): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3500): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(149): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(150): Illuminate\\Support\\Collection->each(Object(Closure))
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(668): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(293): call_user_func(Object(Closure))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(686): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1303): Illuminate\\Container\\Container->call(Object(Closure))
#19 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#20 C:\\laragon\\www\\neomuria2025_augment\\artisan(51): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 {main}
"} 
[2025-07-17 22:17:56] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f6a568f-d82e-407b-9efd-30d5c5f1066e, {"connection":"mysql","bindings":[],"sql":"select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'neomuria2025' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name","time":"33.55","slow":false,"file":"C:\\laragon\\www\\neomuria2025_augment\\artisan","line":37,"hash":"82e23b3a393dac7c78ee8e12c0c189b5","hostname":"BIEGYE"}, 2025-07-17 22:17:56, ?, query, 9f6a568f-ce34-4a4f-8659-f63f661a019a), (9f6a568f-d82e-407b-9efd-30d5c5f1066e, {"command":"migrate:status","exit_code":1,"arguments":{"command":"migrate:status"},"options":{"database":null,"pending":false,"path":[],"realpath":false,"help":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"BIEGYE"}, 2025-07-17 22:17:56, ?, command, 9f6a568f-d6ff-44b3-8b9d-633c081069b4)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f6a568f-d82e-407b-9efd-30d5c5f1066e, {\"connection\":\"mysql\",\"bindings\":[],\"sql\":\"select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'neomuria2025' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name\",\"time\":\"33.55\",\"slow\":false,\"file\":\"C:\\\\laragon\\\\www\\\\neomuria2025_augment\\\\artisan\",\"line\":37,\"hash\":\"82e23b3a393dac7c78ee8e12c0c189b5\",\"hostname\":\"BIEGYE\"}, 2025-07-17 22:17:56, ?, query, 9f6a568f-ce34-4a4f-8659-f63f661a019a), (9f6a568f-d82e-407b-9efd-30d5c5f1066e, {\"command\":\"migrate:status\",\"exit_code\":1,\"arguments\":{\"command\":\"migrate:status\"},\"options\":{\"database\":null,\"pending\":false,\"path\":[],\"realpath\":false,\"help\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"BIEGYE\"}, 2025-07-17 22:17:56, ?, command, 9f6a568f-d6ff-44b3-8b9d-633c081069b4)) at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(50): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3500): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(149): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(150): Illuminate\\Support\\Collection->each(Object(Closure))
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(668): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(293): call_user_func(Object(Closure))
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(686): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1303): Illuminate\\Container\\Container->call(Object(Closure))
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#18 C:\\laragon\\www\\neomuria2025_augment\\artisan(51): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'neomuria2025.telescope_entries' doesn't exist at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `te...')
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(50): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3500): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(149): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(150): Illuminate\\Support\\Collection->each(Object(Closure))
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(668): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(293): call_user_func(Object(Closure))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(686): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1303): Illuminate\\Container\\Container->call(Object(Closure))
#19 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#20 C:\\laragon\\www\\neomuria2025_augment\\artisan(51): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#21 {main}
"} 
[2025-07-17 22:18:02] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: mysql, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `uuid` char(36) not null, `employee_id` varchar(255) not null, `name` varchar(255) not null, `email` varchar(255) not null, `phone` varchar(255) null, `email_verified_at` timestamp null, `phone_verified_at` timestamp null, `password` varchar(255) not null, `avatar` varchar(255) null, `status` enum('active', 'inactive', 'suspended') not null default 'active', `last_login_at` timestamp null, `two_factor_enabled` tinyint(1) not null default '0', `two_factor_secret` text null, `two_factor_recovery_codes` text null, `timezone` varchar(255) not null default 'Asia/Jakarta', `language` varchar(5) not null default 'id', `created_by` bigint unsigned null, `updated_by` bigint unsigned null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null, `deleted_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: mysql, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `uuid` char(36) not null, `employee_id` varchar(255) not null, `name` varchar(255) not null, `email` varchar(255) not null, `phone` varchar(255) null, `email_verified_at` timestamp null, `phone_verified_at` timestamp null, `password` varchar(255) not null, `avatar` varchar(255) null, `status` enum('active', 'inactive', 'suspended') not null default 'active', `last_login_at` timestamp null, `two_factor_enabled` tinyint(1) not null default '0', `two_factor_secret` text null, `two_factor_recovery_codes` text null, `timezone` varchar(255) not null default 'Asia/Jakarta', `language` varchar(5) not null default 'id', `created_by` bigint unsigned null, `updated_by` bigint unsigned null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null, `deleted_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `u...')
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 C:\\laragon\\www\\neomuria2025_augment\\database\\migrations\\2024_01_01_000001_create_users_table.php(42): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 1, false)
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `u...', Array)
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `u...')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 C:\\laragon\\www\\neomuria2025_augment\\database\\migrations\\2024_01_01_000001_create_users_table.php(42): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 1, false)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-07-17 22:19:11] local.ERROR: Command "module:list" is not defined.

Did you mean one of these?
    backup:list
    channel:list
    event:list
    horizon:list
    make:listener
    model:prune
    model:show
    module:delete
    module:disable
    module:dump
    module:enable
    module:make
    module:make-command
    module:make-component
    module:make-component-view
    module:make-controller
    module:make-event
    module:make-job
    module:make-listener
    module:make-mail
    module:make-middleware
    module:make-migration
    module:make-model
    module:make-notification
    module:make-policy
    module:make-provider
    module:make-request
    module:make-resource
    module:make-rule
    module:make-seed
    module:make-test
    module:migrate
    module:migrate-refresh
    module:migrate-reset
    module:migrate-rollback
    module:migrate-status
    module:publish
    module:publish-config
    module:publish-migration
    module:publish-translation
    module:route-provider
    module:seed
    module:setup
    module:unuse
    module:update
    module:use
    module:v6:migrate
    queue:listen
    route:list
    schedule:list {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"module:list\" is not defined.

Did you mean one of these?
    backup:list
    channel:list
    event:list
    horizon:list
    make:listener
    model:prune
    model:show
    module:delete
    module:disable
    module:dump
    module:enable
    module:make
    module:make-command
    module:make-component
    module:make-component-view
    module:make-controller
    module:make-event
    module:make-job
    module:make-listener
    module:make-mail
    module:make-middleware
    module:make-migration
    module:make-model
    module:make-notification
    module:make-policy
    module:make-provider
    module:make-request
    module:make-resource
    module:make-rule
    module:make-seed
    module:make-test
    module:migrate
    module:migrate-refresh
    module:migrate-reset
    module:migrate-rollback
    module:migrate-status
    module:publish
    module:publish-config
    module:publish-migration
    module:publish-translation
    module:route-provider
    module:seed
    module:setup
    module:unuse
    module:update
    module:use
    module:v6:migrate
    queue:listen
    route:list
    schedule:list at C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('module:list')
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-17 22:20:09] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(34): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(112): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(91): Illuminate\\Redis\\RedisManager->resolve('cache')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(330): Illuminate\\Redis\\RedisManager->connection('cache')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(65): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(98): Illuminate\\Cache\\RedisStore->get('telescope:dump-...')
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->get('telescope:dump-...')
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Watchers\\DumpWatcher.php(47): Illuminate\\Cache\\CacheManager->__call('get', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\RegistersWatchers.php(48): Laravel\\Telescope\\Watchers\\DumpWatcher->register(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(142): Laravel\\Telescope\\Telescope::registerWatchers(Object(Illuminate\\Foundation\\Application))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\TelescopeServiceProvider.php(33): Laravel\\Telescope\\Telescope::start(Object(Illuminate\\Foundation\\Application))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\TelescopeServiceProvider->boot()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Laravel\\Telescope\\TelescopeServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Laravel\\Telescope\\TelescopeServiceProvider), 20)
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
