[2025-07-17 22:10:51] local.ERROR: rename(C:\laragon\www\neomuria2025_augment\bootstrap\cache\ser11F5.tmp,C:\laragon\www\neomuria2025_augment\bootstrap\cache/services.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(C:\\laragon\\www\\neomuria2025_augment\\bootstrap\\cache\\ser11F5.tmp,C:\\laragon\\www\\neomuria2025_augment\\bootstrap\\cache/services.php): Access is denied (code: 5) at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(C:\\\\larag...', 'C:\\\\laragon\\\\www\\\\...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(C:\\\\larag...', 'C:\\\\laragon\\\\www\\\\...', 233)
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('C:\\\\laragon\\\\www\\\\...', 'C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(192): Illuminate\\Filesystem\\Filesystem->replace('C:\\\\laragon\\\\www\\\\...', '<?php return ar...')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(163): Illuminate\\Foundation\\ProviderRepository->writeManifest(Array)
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load(Array)
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#11 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-07-17 22:10:51] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(34): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#1 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(112): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#3 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(91): Illuminate\\Redis\\RedisManager->resolve('cache')
#4 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(330): Illuminate\\Redis\\RedisManager->connection('cache')
#5 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(65): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(98): Illuminate\\Cache\\RedisStore->get('telescope:dump-...')
#7 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->get('telescope:dump-...')
#8 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Watchers\\DumpWatcher.php(47): Illuminate\\Cache\\CacheManager->__call('get', Array)
#9 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\RegistersWatchers.php(48): Laravel\\Telescope\\Watchers\\DumpWatcher->register(Object(Illuminate\\Foundation\\Application))
#10 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\Telescope.php(142): Laravel\\Telescope\\Telescope::registerWatchers(Object(Illuminate\\Foundation\\Application))
#11 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\telescope\\src\\TelescopeServiceProvider.php(33): Laravel\\Telescope\\Telescope::start(Object(Illuminate\\Foundation\\Application))
#12 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\TelescopeServiceProvider->boot()
#13 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Laravel\\Telescope\\TelescopeServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Laravel\\Telescope\\TelescopeServiceProvider), 20)
#20 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1041): array_walk(Array, Object(Closure))
#21 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\laragon\\www\\neomuria2025_augment\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\laragon\\www\\neomuria2025_augment\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
