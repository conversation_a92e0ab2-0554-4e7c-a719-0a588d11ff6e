# ERP Poultry Management System - Git Ignore File
# ================================================

# Laravel Backend
# ---------------

# Environment files
.env
.env.backup
.env.production
.env.local
.env.testing

# Laravel specific
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
/storage/app/public/*
!/storage/app/public/.gitkeep
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitkeep
/storage/framework/sessions/*
!/storage/framework/sessions/.gitkeep
/storage/framework/testing/*
!/storage/framework/testing/.gitkeep
/storage/framework/views/*
!/storage/framework/views/.gitkeep
/storage/logs/*
!/storage/logs/.gitkeep

# Laravel Mix
/public/css/
/public/js/
/public/mix-manifest.json

# Laravel Telescope
/storage/telescope/

# Laravel Horizon
/storage/horizon/

# Composer
composer.phar
composer.lock

# PHP
*.log
*.cache
*.tmp

# Frontend (React)
# ----------------

# Dependencies
frontend/node_modules/
frontend/.pnp
frontend/.pnp.js

# Production builds
frontend/build/
frontend/dist/

# Environment files
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Logs
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/lerna-debug.log*

# Runtime data
frontend/pids
frontend/*.pid
frontend/*.seed
frontend/*.pid.lock

# Coverage directory used by tools like istanbul
frontend/coverage/
frontend/*.lcov

# nyc test coverage
frontend/.nyc_output

# ESLint cache
frontend/.eslintcache

# Optional npm cache directory
frontend/.npm

# Optional REPL history
frontend/.node_repl_history

# Output of 'npm pack'
frontend/*.tgz

# Yarn Integrity file
frontend/.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
frontend/.cache
frontend/.parcel-cache

# Next.js build output
frontend/.next

# Nuxt.js build / generate output
frontend/.nuxt
frontend/dist

# Storybook build outputs
frontend/.out
frontend/.storybook-out

# Temporary folders
frontend/tmp/
frontend/temp/

# Mobile App (React Native)
# --------------------------

# React Native
mobile/node_modules/
mobile/.expo/
mobile/dist/
mobile/npm-debug.*
mobile/*.jks
mobile/*.p8
mobile/*.p12
mobile/*.key
mobile/*.mobileprovision
mobile/*.orig.*
mobile/web-build/

# macOS
mobile/.DS_Store

# Xcode
mobile/ios/build/
mobile/ios/Pods/
mobile/ios/*.xcworkspace/xcuserdata/
mobile/ios/*.xcodeproj/xcuserdata/

# Android
mobile/android/app/build/
mobile/android/.gradle/
mobile/android/captures/
mobile/android/gradlew.bat
mobile/android/local.properties
mobile/android/*.iml

# Database
# --------

# SQLite
*.sqlite
*.sqlite3
*.db

# MySQL dumps
*.sql

# PostgreSQL dumps
*.dump

# Development Tools
# -----------------

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Docker
# ------

# Docker volumes
docker/mysql/data/
docker/redis/data/
docker/minio/data/

# Docker Compose override
docker-compose.override.yml

# Logs
# ----

# Application logs
logs/
*.log

# Laravel logs
storage/logs/*.log

# npm logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
# -------

# PHPUnit
.phpunit.result.cache
/coverage/
/tests/coverage/

# Jest
/coverage/
/frontend/coverage/

# Cypress
cypress/videos/
cypress/screenshots/

# Documentation
# -------------

# Generated documentation
/docs/build/
/docs/dist/

# API documentation
/public/docs/

# Deployment
# ----------

# Deployment scripts (sensitive)
deploy.sh
deploy.bat
.deploy/

# SSL certificates
*.pem
*.crt
*.key
*.csr

# Backup files
# ------------

*.backup
*.bak
*.old
*.orig

# Cache files
# -----------

# PHP OPcache
*.opcache

# Laravel caches
bootstrap/cache/*.php
storage/framework/cache/
storage/framework/sessions/
storage/framework/views/

# Node.js cache
.npm/
.yarn/

# Configuration
# -------------

# Local configuration overrides
config/local.php
config/development.php

# Sensitive configuration
config/secrets.php

# Media files (large files)
# -------------------------

# Uploaded files (development)
/public/uploads/
/storage/app/uploads/

# Generated reports
/storage/app/reports/
/public/reports/

# Exports
*.xlsx
*.csv
*.pdf

# Modules (if using Laravel Modules)
# ----------------------------------

# Module specific ignores
Modules/*/node_modules/
Modules/*/vendor/

# Package development
# ------------------

# Composer packages
/packages/

# NPM packages
/npm-packages/

# Miscellaneous
# -------------

# Vagrant
.vagrant/

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Ansible
*.retry

# Local development helpers
start-dev.sh
start-dev.bat
local-setup.sh
local-setup.bat

# Performance profiling
*.prof
*.trace

# Security scanning results
security-report.*

# Load testing results
load-test-results/

# Monitoring and metrics
metrics/
monitoring/

# Third-party integrations
integrations/config/

# Custom scripts (potentially sensitive)
scripts/production/
scripts/staging/

# End of .gitignore
