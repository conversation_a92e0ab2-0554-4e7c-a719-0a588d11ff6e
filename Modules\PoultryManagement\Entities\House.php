<?php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

class House extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'farm_id',
        'house_number',
        'house_name',
        'house_type',
        'capacity',
        'length_meters',
        'width_meters',
        'height_meters',
        'construction_date',
        'last_renovation_date',
        'ventilation_type',
        'lighting_type',
        'feeding_system',
        'watering_system',
        'waste_management',
        'climate_control',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'construction_date' => 'date',
        'last_renovation_date' => 'date',
        'capacity' => 'integer',
        'length_meters' => 'decimal:2',
        'width_meters' => 'decimal:2',
        'height_meters' => 'decimal:2',
        'climate_control' => 'array',
    ];

    // Boot method to auto-generate UUID
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($house) {
            if (empty($house->uuid)) {
                $house->uuid = Str::uuid();
            }
        });
    }

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['house_name', 'status', 'capacity'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function farm()
    {
        return $this->belongsTo(Farm::class);
    }

    public function flocks()
    {
        return $this->hasMany(Flock::class);
    }

    public function currentFlock()
    {
        return $this->hasOne(Flock::class)->where('status', 'active');
    }

    public function assignedUsers()
    {
        return $this->belongsToMany(\App\Models\User::class, 'user_houses');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeAvailable($query)
    {
        return $query->where('status', 'active')
            ->whereDoesntHave('currentFlock');
    }

    public function scopeOccupied($query)
    {
        return $query->where('status', 'active')
            ->whereHas('currentFlock');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('house_type', $type);
    }

    // Accessors
    public function getIsAvailableAttribute(): bool
    {
        return $this->status === 'active' && !$this->currentFlock;
    }

    public function getIsOccupiedAttribute(): bool
    {
        return $this->status === 'active' && $this->currentFlock;
    }

    public function getCurrentPopulationAttribute(): int
    {
        return $this->currentFlock ? $this->currentFlock->current_count : 0;
    }

    public function getOccupancyRateAttribute(): float
    {
        return $this->capacity > 0 ? ($this->current_population / $this->capacity) * 100 : 0;
    }

    public function getFloorAreaAttribute(): float
    {
        return $this->length_meters * $this->width_meters;
    }

    public function getDensityPerSquareMeterAttribute(): float
    {
        return $this->floor_area > 0 ? $this->current_population / $this->floor_area : 0;
    }

    public function getAgeInYearsAttribute(): float
    {
        return $this->construction_date ? $this->construction_date->diffInYears(now()) : 0;
    }

    // Methods
    public function placeFlock(Flock $flock): bool
    {
        if (!$this->is_available) {
            return false;
        }

        if ($flock->initial_count > $this->capacity) {
            return false;
        }

        $flock->update(['house_id' => $this->id]);
        return true;
    }

    public function calculateMaintenanceSchedule(): array
    {
        $lastRenovation = $this->last_renovation_date ?: $this->construction_date;
        $nextMaintenance = $lastRenovation ? $lastRenovation->addYear() : now()->addYear();
        
        return [
            'last_maintenance' => $lastRenovation,
            'next_maintenance' => $nextMaintenance,
            'days_until_maintenance' => now()->diffInDays($nextMaintenance, false),
            'maintenance_overdue' => $nextMaintenance->isPast(),
        ];
    }
}
