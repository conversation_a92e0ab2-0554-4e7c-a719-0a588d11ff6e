<?php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

class Flock extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'flock_number',
        'house_id',
        'breed_id',
        'placement_date',
        'source',
        'supplier',
        'initial_count',
        'current_count',
        'production_stage',
        'expected_production_start',
        'expected_culling_date',
        'actual_culling_date',
        'mortality_total',
        'culled_total',
        'transferred_out',
        'transferred_in',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'placement_date' => 'date',
        'expected_production_start' => 'date',
        'expected_culling_date' => 'date',
        'actual_culling_date' => 'date',
        'initial_count' => 'integer',
        'current_count' => 'integer',
        'mortality_total' => 'integer',
        'culled_total' => 'integer',
        'transferred_out' => 'integer',
        'transferred_in' => 'integer',
    ];

    // Boot method to auto-generate UUID and flock number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($flock) {
            if (empty($flock->uuid)) {
                $flock->uuid = Str::uuid();
            }
            if (empty($flock->flock_number)) {
                $flock->flock_number = $flock->generateFlockNumber();
            }
        });
    }

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['current_count', 'status', 'production_stage'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function house()
    {
        return $this->belongsTo(House::class);
    }

    public function breed()
    {
        return $this->belongsTo(Breed::class);
    }

    public function farm()
    {
        return $this->hasOneThrough(Farm::class, House::class, 'id', 'id', 'house_id', 'farm_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInProduction($query)
    {
        return $query->where('production_stage', 'laying');
    }

    public function scopeByStage($query, $stage)
    {
        return $query->where('production_stage', $stage);
    }

    public function scopeByBreed($query, $breedId)
    {
        return $query->where('breed_id', $breedId);
    }

    // Accessors
    public function getAgeWeeksAttribute(): int
    {
        return $this->placement_date ? $this->placement_date->diffInWeeks(now()) : 0;
    }

    public function getAgeDaysAttribute(): int
    {
        return $this->placement_date ? $this->placement_date->diffInDays(now()) : 0;
    }

    public function getIsInProductionAttribute(): bool
    {
        return $this->production_stage === 'laying';
    }

    public function getSurvivalRateAttribute(): float
    {
        return $this->initial_count > 0 ? ($this->current_count / $this->initial_count) * 100 : 0;
    }

    public function getMortalityRateAttribute(): float
    {
        return $this->initial_count > 0 ? ($this->mortality_total / $this->initial_count) * 100 : 0;
    }

    public function getProductionWeeksAttribute(): int
    {
        if (!$this->expected_production_start) return 0;
        
        $startDate = $this->expected_production_start;
        $endDate = $this->actual_culling_date ?: $this->expected_culling_date ?: now();
        
        return $startDate->diffInWeeks($endDate);
    }

    public function getCurrentProductionWeekAttribute(): int
    {
        if (!$this->expected_production_start || now() < $this->expected_production_start) {
            return 0;
        }
        
        return $this->expected_production_start->diffInWeeks(now()) + 1;
    }

    // Methods
    public function updatePopulation(int $mortality = 0, int $culled = 0, int $transferred = 0): void
    {
        $this->increment('mortality_total', $mortality);
        $this->increment('culled_total', $culled);
        
        if ($transferred > 0) {
            $this->increment('transferred_out', $transferred);
        } elseif ($transferred < 0) {
            $this->increment('transferred_in', abs($transferred));
        }
        
        $newCount = $this->initial_count + $this->transferred_in - $this->mortality_total - $this->culled_total - $this->transferred_out;
        $this->update(['current_count' => max(0, $newCount)]);
    }

    public function updateProductionStage(): void
    {
        $ageWeeks = $this->age_weeks;
        
        if ($ageWeeks < 18) {
            $stage = 'growing';
        } elseif ($ageWeeks < 20) {
            $stage = 'pre_laying';
        } elseif ($ageWeeks < 72) {
            $stage = 'laying';
        } else {
            $stage = 'post_laying';
        }
        
        if ($this->production_stage !== $stage) {
            $this->update(['production_stage' => $stage]);
        }
    }

    public function calculatePerformanceMetrics(): array
    {
        // This will be enhanced when production records are implemented
        return [
            'total_eggs_30_days' => 0,
            'avg_daily_eggs' => 0,
            'hen_day_production' => 0,
            'survival_rate' => $this->survival_rate,
            'mortality_rate' => $this->mortality_rate,
            'current_age_weeks' => $this->age_weeks,
            'production_week' => $this->current_production_week,
        ];
    }

    public function generateFlockNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "FL{$year}{$month}";
        
        $lastFlock = static::where('flock_number', 'like', $prefix . '%')
            ->orderBy('flock_number', 'desc')
            ->first();
        
        if ($lastFlock) {
            $lastNumber = (int) substr($lastFlock->flock_number, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }
        
        return $prefix . $newNumber;
    }
}
