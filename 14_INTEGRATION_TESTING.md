# 14 - INTEGRATION TESTING MODULE

## 📋 OVERVIEW

Modul Integration Testing menyediakan comprehensive testing framework untuk memastikan semua modules bekerja dengan baik secara individual dan terintegrasi. Testing mencakup unit tests, integration tests, API tests, dan end-to-end testing.

## 🎯 TUJUAN

- Comprehensive test coverage untuk semua modules
- Automated testing pipeline dengan CI/CD integration
- API testing untuk semua endpoints
- Database integrity testing
- Performance testing dan load testing
- Security testing dan vulnerability assessment
- Mobile app testing (unit dan integration)
- End-to-end workflow testing

## ⏱️ ESTIMASI WAKTU

**Total**: 20-24 jam
- Backend testing setup: 8-10 jam
- Frontend testing setup: 6-8 jam
- Integration tests: 4-6 jam
- Performance & security tests: 2-4 jam

## 👥 TIM YANG TERLIBAT

- **QA Engineer** (Lead)
- **Backend Developer**
- **Frontend Developer**
- **DevOps Engineer**

## 🧪 TESTING ARCHITECTURE

```
┌─────────────────────────────────────────────────────────────┐
│                    TESTING ARCHITECTURE                     │
├─────────────────────────────────────────────────────────────┤
│  Unit Tests                                                │
│  ├── Backend (PHPUnit)                                     │
│  ├── Frontend (Jest + React Testing Library)               │
│  └── Mobile (Jest + React Native Testing Library)          │
├─────────────────────────────────────────────────────────────┤
│  Integration Tests                                         │
│  ├── API Tests (Postman/Newman)                           │
│  ├── Database Tests (Laravel Dusk)                        │
│  └── Module Integration Tests                             │
├─────────────────────────────────────────────────────────────┤
│  End-to-End Tests                                         │
│  ├── Web App (Cypress)                                    │
│  ├── Mobile App (Detox)                                   │
│  └── Workflow Tests                                       │
├─────────────────────────────────────────────────────────────┤
│  Performance Tests                                        │
│  ├── Load Testing (Artillery)                             │
│  ├── Stress Testing                                       │
│  └── Database Performance                                 │
├─────────────────────────────────────────────────────────────┤
│  Security Tests                                           │
│  ├── Vulnerability Scanning                               │
│  ├── Authentication Tests                                 │
│  └── Authorization Tests                                  │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 STEP 1: BACKEND TESTING SETUP

### **1.1 PHPUnit Configuration**

```xml
<!-- phpunit.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
        <testsuite name="Modules">
            <directory suffix="Test.php">./Modules/*/Tests</directory>
        </testsuite>
    </testsuites>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">./app</directory>
            <directory suffix=".php">./Modules</directory>
        </include>
        <exclude>
            <directory suffix=".php">./vendor</directory>
        </exclude>
    </coverage>
    <php>
        <server name="APP_ENV" value="testing"/>
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="CACHE_DRIVER" value="array"/>
        <server name="DB_CONNECTION" value="sqlite"/>
        <server name="DB_DATABASE" value=":memory:"/>
        <server name="MAIL_MAILER" value="array"/>
        <server name="QUEUE_CONNECTION" value="sync"/>
        <server name="SESSION_DRIVER" value="array"/>
        <server name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

### **1.2 User Management Tests**

```php
<?php
// Modules/UserManagement/Tests/Feature/UserManagementTest.php

namespace Modules\UserManagement\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\User;
use Modules\UserManagement\Entities\Role;
use Modules\UserManagement\Entities\Permission;

class UserManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('module:seed UserManagement');
    }

    /** @test */
    public function admin_can_create_user()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $userData = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'farm_manager',
        ];

        $response = $this->actingAs($admin)
            ->postJson('/api/v1/users', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'email',
                    'roles',
                ]
            ]);

        $this->assertDatabaseHas('users', [
            'email' => $userData['email'],
            'name' => $userData['name'],
        ]);
    }

    /** @test */
    public function user_cannot_access_admin_endpoints()
    {
        $user = User::factory()->create();
        $user->assignRole('farm_worker');

        $response = $this->actingAs($user)
            ->getJson('/api/v1/users');

        $response->assertStatus(403);
    }

    /** @test */
    public function user_can_update_own_profile()
    {
        $user = User::factory()->create();

        $updateData = [
            'name' => 'Updated Name',
            'phone' => '+1234567890',
        ];

        $response = $this->actingAs($user)
            ->putJson("/api/v1/users/{$user->id}", $updateData);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'phone' => '+1234567890',
        ]);
    }

    /** @test */
    public function role_permissions_work_correctly()
    {
        $role = Role::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);

        $permission = Permission::create([
            'name' => 'test_permission',
            'display_name' => 'Test Permission',
        ]);

        $role->givePermissionTo($permission);

        $user = User::factory()->create();
        $user->assignRole($role);

        $this->assertTrue($user->hasPermissionTo('test_permission'));
        $this->assertTrue($user->hasRole('test_role'));
    }
}
```

### **1.3 Poultry Management Tests**

```php
<?php
// Modules/PoultryManagement/Tests/Feature/FlockManagementTest.php

namespace Modules\PoultryManagement\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use Modules\PoultryManagement\Entities\Farm;
use Modules\PoultryManagement\Entities\House;
use Modules\PoultryManagement\Entities\Flock;
use Modules\PoultryManagement\Entities\Breed;

class FlockManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $farm;
    protected $house;
    protected $breed;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->user->assignRole('farm_manager');

        $this->farm = Farm::factory()->create();
        $this->house = House::factory()->create(['farm_id' => $this->farm->id]);
        $this->breed = Breed::factory()->create();
    }

    /** @test */
    public function can_create_flock()
    {
        $flockData = [
            'flock_number' => 'FL001',
            'breed_id' => $this->breed->id,
            'house_id' => $this->house->id,
            'placement_date' => now()->toDateString(),
            'initial_count' => 1000,
            'source' => 'hatchery',
            'production_stage' => 'chick',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/flocks', $flockData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'flock_number',
                    'breed',
                    'house',
                    'current_count',
                    'age_weeks',
                ]
            ]);

        $this->assertDatabaseHas('flocks', [
            'flock_number' => 'FL001',
            'initial_count' => 1000,
            'current_count' => 1000,
        ]);
    }

    /** @test */
    public function flock_age_calculation_is_correct()
    {
        $flock = Flock::factory()->create([
            'placement_date' => now()->subWeeks(10),
        ]);

        $this->assertEquals(10, $flock->age_weeks);
    }

    /** @test */
    public function can_update_flock_population()
    {
        $flock = Flock::factory()->create([
            'initial_count' => 1000,
            'current_count' => 1000,
        ]);

        $flock->updatePopulation(5, 3, 0); // 5 mortality, 3 culled, 0 transferred

        $this->assertEquals(992, $flock->fresh()->current_count);
    }

    /** @test */
    public function cannot_create_flock_with_invalid_data()
    {
        $flockData = [
            'flock_number' => '', // Invalid: empty
            'breed_id' => 999, // Invalid: non-existent
            'initial_count' => -10, // Invalid: negative
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/flocks', $flockData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['flock_number', 'breed_id', 'initial_count']);
    }

    /** @test */
    public function can_transfer_flock_to_different_house()
    {
        $flock = Flock::factory()->create(['house_id' => $this->house->id]);
        $newHouse = House::factory()->create(['farm_id' => $this->farm->id]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/flocks/{$flock->id}/transfer", [
                'new_house_id' => $newHouse->id,
                'transfer_date' => now()->toDateString(),
                'reason' => 'Better facilities',
            ]);

        $response->assertStatus(200);
        
        $this->assertEquals($newHouse->id, $flock->fresh()->house_id);
    }
}
```

### **1.4 Integration Tests**

```php
<?php
// tests/Integration/ProductionWorkflowTest.php

namespace Tests\Integration;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use Modules\PoultryManagement\Entities\Flock;
use Modules\EggProduction\Entities\EggProductionRecord;
use Modules\FeedManagement\Entities\FeedConsumptionRecord;
use Modules\Accounting\Entities\JournalEntry;

class ProductionWorkflowTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function complete_production_workflow_works()
    {
        // Setup
        $user = User::factory()->create();
        $user->assignRole('farm_manager');
        
        $flock = Flock::factory()->create([
            'initial_count' => 1000,
            'current_count' => 1000,
        ]);

        // Step 1: Record egg production
        $productionData = [
            'record_date' => now()->toDateString(),
            'flock_id' => $flock->id,
            'house_id' => $flock->house_id,
            'eggs_collected' => 850,
            'mortality_count' => 2,
            'feed_consumed_kg' => 120,
        ];

        $productionResponse = $this->actingAs($user)
            ->postJson('/api/v1/egg-production/records', $productionData);

        $productionResponse->assertStatus(201);

        // Step 2: Record feed consumption
        $feedData = [
            'consumption_date' => now()->toDateString(),
            'flock_id' => $flock->id,
            'house_id' => $flock->house_id,
            'quantity_kg' => 120,
            'feed_type' => 'layer',
            'bird_count' => 998, // After mortality
        ];

        $feedResponse = $this->actingAs($user)
            ->postJson('/api/v1/feed-consumption', $feedData);

        $feedResponse->assertStatus(201);

        // Step 3: Verify data consistency
        $flock->refresh();
        $this->assertEquals(998, $flock->current_count);

        $production = EggProductionRecord::where('flock_id', $flock->id)->first();
        $this->assertNotNull($production);
        $this->assertEquals(850, $production->eggs_collected);

        $feedConsumption = FeedConsumptionRecord::where('flock_id', $flock->id)->first();
        $this->assertNotNull($feedConsumption);
        $this->assertEquals(120, $feedConsumption->quantity_kg);

        // Step 4: Verify FCR calculation
        $expectedFCR = 120 / 850; // feed consumed / eggs produced
        $this->assertEquals($expectedFCR, $feedConsumption->feed_conversion_ratio, '', 0.01);
    }

    /** @test */
    public function sales_to_accounting_integration_works()
    {
        $user = User::factory()->create();
        $user->assignRole('sales_manager');

        // Create sales order
        $salesData = [
            'customer_id' => 1,
            'order_date' => now()->toDateString(),
            'payment_terms_id' => 1,
            'items' => [
                [
                    'item_id' => 1,
                    'quantity' => 100,
                    'unit_price' => 2.50,
                ]
            ]
        ];

        $salesResponse = $this->actingAs($user)
            ->postJson('/api/v1/sales-orders', $salesData);

        $salesResponse->assertStatus(201);
        $salesOrder = $salesResponse->json('data');

        // Approve sales order
        $approveResponse = $this->actingAs($user)
            ->postJson("/api/v1/sales-orders/{$salesOrder['id']}/approve");

        $approveResponse->assertStatus(200);

        // Create invoice
        $invoiceResponse = $this->actingAs($user)
            ->postJson('/api/v1/sales-invoices', [
                'sales_order_id' => $salesOrder['id'],
            ]);

        $invoiceResponse->assertStatus(201);

        // Verify accounting entries were created
        $journalEntries = JournalEntry::where('reference_type', 'Modules\Sales\Entities\SalesInvoice')
            ->where('reference_id', $invoiceResponse->json('data.id'))
            ->get();

        $this->assertGreaterThan(0, $journalEntries->count());
        
        // Verify debit and credit balance
        $totalDebits = $journalEntries->sum('total_debit');
        $totalCredits = $journalEntries->sum('total_credit');
        $this->assertEquals($totalDebits, $totalCredits);
    }
}
```

## ⚛️ STEP 2: FRONTEND TESTING SETUP

### **2.1 Jest Configuration**

```json
// frontend/jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx',
    '!src/reportWebVitals.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
};
```

### **2.2 Component Tests**

```typescript
// frontend/src/modules/user-management/__tests__/UserForm.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import UserForm from '../components/UserForm';
import { userManagementService } from '../services/userManagementService';

// Mock the service
jest.mock('../services/userManagementService');
const mockUserService = userManagementService as jest.Mocked<typeof userManagementService>;

const mockStore = configureStore({
  reducer: {
    userManagement: (state = { roles: [], permissions: [] }) => state,
  },
});

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  );
};

describe('UserForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders user form correctly', () => {
    renderWithProvider(<UserForm />);
    
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
  });

  test('validates required fields', async () => {
    renderWithProvider(<UserForm />);
    
    const submitButton = screen.getByRole('button', { name: /create user/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  test('submits form with valid data', async () => {
    mockUserService.createUser.mockResolvedValue({
      data: { id: 1, name: 'Test User', email: '<EMAIL>' }
    });

    renderWithProvider(<UserForm />);
    
    fireEvent.change(screen.getByLabelText(/name/i), {
      target: { value: 'Test User' }
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    });

    const submitButton = screen.getByRole('button', { name: /create user/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUserService.createUser).toHaveBeenCalledWith({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  test('displays error message on submission failure', async () => {
    mockUserService.createUser.mockRejectedValue(new Error('Email already exists'));

    renderWithProvider(<UserForm />);
    
    // Fill form and submit
    fireEvent.change(screen.getByLabelText(/name/i), {
      target: { value: 'Test User' }
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    });

    const submitButton = screen.getByRole('button', { name: /create user/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email already exists/i)).toBeInTheDocument();
    });
  });
});
```

### **2.3 Service Tests**

```typescript
// frontend/src/modules/user-management/__tests__/userManagementService.test.ts
import { userManagementService } from '../services/userManagementService';
import { apiClient } from '@/core/api/apiClient';

jest.mock('@/core/api/apiClient');
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('UserManagementService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    test('fetches users successfully', async () => {
      const mockUsers = [
        { id: 1, name: 'User 1', email: '<EMAIL>' },
        { id: 2, name: 'User 2', email: '<EMAIL>' },
      ];

      mockApiClient.get.mockResolvedValue({ data: mockUsers });

      const result = await userManagementService.getUsers();

      expect(mockApiClient.get).toHaveBeenCalledWith('/users?');
      expect(result.data).toEqual(mockUsers);
    });

    test('applies filters correctly', async () => {
      const filters = { role: 'admin', status: 'active' };
      mockApiClient.get.mockResolvedValue({ data: [] });

      await userManagementService.getUsers(filters);

      expect(mockApiClient.get).toHaveBeenCalledWith('/users?role=admin&status=active');
    });
  });

  describe('createUser', () => {
    test('creates user successfully', async () => {
      const userData = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = { data: { id: 1, ...userData } };
      mockApiClient.post.mockResolvedValue(mockResponse);

      const result = await userManagementService.createUser(userData);

      expect(mockApiClient.post).toHaveBeenCalledWith('/users', userData);
      expect(result.data.name).toBe(userData.name);
    });

    test('handles creation error', async () => {
      const userData = {
        name: 'New User',
        email: 'invalid-email',
        password: 'weak',
      };

      mockApiClient.post.mockRejectedValue(new Error('Validation failed'));

      await expect(userManagementService.createUser(userData))
        .rejects.toThrow('Validation failed');
    });
  });
});
```

## 🔗 STEP 3: API TESTING

### **3.1 Postman Collection**

```json
{
  "info": {
    "name": "ERP Poultry API Tests",
    "description": "Comprehensive API testing for ERP Poultry system",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{auth_token}}",
        "type": "string"
      }
    ]
  },
  "event": [
    {
      "listen": "prerequest",
      "script": {
        "exec": [
          "// Set base URL",
          "pm.globals.set('base_url', 'http://localhost:8000/api/v1');"
        ]
      }
    }
  ],
  "item": [
    {
      "name": "Authentication",
      "item": [
        {
          "name": "Login",
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "pm.test('Status code is 200', function () {",
                  "    pm.response.to.have.status(200);",
                  "});",
                  "",
                  "pm.test('Response has token', function () {",
                  "    const jsonData = pm.response.json();",
                  "    pm.expect(jsonData).to.have.property('token');",
                  "    pm.globals.set('auth_token', jsonData.token);",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/auth/login",
              "host": ["{{base_url}}"],
              "path": ["auth", "login"]
            }
          }
        }
      ]
    },
    {
      "name": "User Management",
      "item": [
        {
          "name": "Get Users",
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "pm.test('Status code is 200', function () {",
                  "    pm.response.to.have.status(200);",
                  "});",
                  "",
                  "pm.test('Response is array', function () {",
                  "    const jsonData = pm.response.json();",
                  "    pm.expect(jsonData.data).to.be.an('array');",
                  "});",
                  "",
                  "pm.test('Users have required fields', function () {",
                  "    const jsonData = pm.response.json();",
                  "    if (jsonData.data.length > 0) {",
                  "        const user = jsonData.data[0];",
                  "        pm.expect(user).to.have.property('id');",
                  "        pm.expect(user).to.have.property('name');",
                  "        pm.expect(user).to.have.property('email');",
                  "    }",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{base_url}}/users",
              "host": ["{{base_url}}"],
              "path": ["users"]
            }
          }
        },
        {
          "name": "Create User",
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "pm.test('Status code is 201', function () {",
                  "    pm.response.to.have.status(201);",
                  "});",
                  "",
                  "pm.test('User created successfully', function () {",
                  "    const jsonData = pm.response.json();",
                  "    pm.expect(jsonData.data).to.have.property('id');",
                  "    pm.expect(jsonData.data.name).to.eql('Test User');",
                  "    pm.globals.set('test_user_id', jsonData.data.id);",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n    \"name\": \"Test User\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"farm_worker\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/users",
              "host": ["{{base_url}}"],
              "path": ["users"]
            }
          }
        }
      ]
    }
  ]
}
```

### **3.2 Newman Test Runner**

```javascript
// tests/api/newman-runner.js
const newman = require('newman');
const path = require('path');

const runAPITests = () => {
  return new Promise((resolve, reject) => {
    newman.run({
      collection: path.join(__dirname, 'postman-collection.json'),
      environment: path.join(__dirname, 'test-environment.json'),
      reporters: ['cli', 'json', 'junit'],
      reporter: {
        junit: {
          export: path.join(__dirname, '../../reports/api-test-results.xml')
        },
        json: {
          export: path.join(__dirname, '../../reports/api-test-results.json')
        }
      }
    }, (err, summary) => {
      if (err) {
        reject(err);
      } else {
        console.log('API Tests completed');
        console.log(`Total tests: ${summary.run.stats.tests.total}`);
        console.log(`Passed: ${summary.run.stats.tests.passed}`);
        console.log(`Failed: ${summary.run.stats.tests.failed}`);
        
        if (summary.run.stats.tests.failed > 0) {
          reject(new Error(`${summary.run.stats.tests.failed} API tests failed`));
        } else {
          resolve(summary);
        }
      }
    });
  });
};

module.exports = { runAPITests };
```

## 🔗 STEP 4: CI/CD INTEGRATION

### **4.1 GitHub Actions Workflow**

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: erp_poultry_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mbstring, dom, fileinfo, mysql
        coverage: xdebug
    
    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.testing', '.env');"
    
    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
    
    - name: Generate key
      run: php artisan key:generate
    
    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache
    
    - name: Run Database Migrations
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: erp_poultry_test
        DB_USERNAME: root
        DB_PASSWORD: password
      run: php artisan migrate
    
    - name: Run PHPUnit Tests
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: erp_poultry_test
        DB_USERNAME: root
        DB_PASSWORD: password
      run: vendor/bin/phpunit --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run tests
      working-directory: ./frontend
      run: npm run test:coverage
    
    - name: Run build
      working-directory: ./frontend
      run: npm run build

  api-tests:
    runs-on: ubuntu-latest
    needs: backend-tests
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install Newman
      run: npm install -g newman newman-reporter-junit
    
    - name: Run API Tests
      run: newman run tests/api/postman-collection.json -e tests/api/test-environment.json --reporters cli,junit --reporter-junit-export reports/api-results.xml

  mobile-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
    
    - name: Install dependencies
      working-directory: ./mobile
      run: npm ci
    
    - name: Run tests
      working-directory: ./mobile
      run: npm test -- --coverage --watchAll=false
```

## ✅ VERIFICATION CHECKLIST

### **Backend Testing**
- [ ] Unit tests for all modules
- [ ] Integration tests working
- [ ] API endpoint tests complete
- [ ] Database integrity tests
- [ ] Authentication/authorization tests
- [ ] Performance tests setup

### **Frontend Testing**
- [ ] Component unit tests
- [ ] Service layer tests
- [ ] Integration tests
- [ ] E2E tests with Cypress
- [ ] Accessibility tests
- [ ] Performance tests

### **Mobile Testing**
- [ ] React Native unit tests
- [ ] Integration tests
- [ ] Offline functionality tests
- [ ] Device-specific tests
- [ ] Performance tests

### **CI/CD Integration**
- [ ] Automated test pipeline
- [ ] Code coverage reporting
- [ ] Test result notifications
- [ ] Performance monitoring
- [ ] Security scanning

## 📞 NEXT STEPS

Setelah Integration Testing module selesai:

1. **Run complete test suite**
2. **Verify test coverage** meets requirements
3. **Setup continuous testing**
4. **Document test procedures**
5. **Commit testing framework** ke repository
6. **Lanjut ke** `15_DEPLOYMENT_PRODUCTION.md`

---

**IMPORTANT**: Testing adalah quality assurance foundation. Pastikan comprehensive coverage dan automated testing pipeline untuk reliable software delivery.
