# 05 - ACCOUNTING MODULE

## 📋 OVERVIEW

Modul Accounting mengelola semua aspek keuangan perusahaan termasuk Chart of Accounts (COA), journal entries, general ledger, accounts payable/receivable, dan financial reporting. Modul ini terintegrasi dengan semua modul bisnis untuk automatic journal posting.

## 🎯 TUJUAN

- Manajemen Chart of Accounts (COA) yang hierarkis
- Automatic journal posting dari transaksi bisnis
- General Ledger dengan real-time balances
- Accounts Payable dan Receivable management
- Multi-currency support dengan exchange rates
- Financial reporting dan analysis
- Integration dengan inventory valuation dan cost accounting

## ⏱️ ESTIMASI WAKTU

**Total**: 24-28 jam
- Backend implementation: 16-20 jam
- Frontend implementation: 8-10 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Accounting Specialist** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Chart of Accounts
chart_of_accounts
account_types

-- Journal & Ledger
journal_entries
journal_entry_lines
general_ledger

-- AP/AR
accounts_payable
accounts_receivable
payment_terms

-- Multi-currency
currencies
exchange_rates

-- Financial periods
fiscal_years
accounting_periods
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Accounting module
php artisan module:make Accounting

# Create module components
php artisan module:make-controller Accounting ChartOfAccountsController --api
php artisan module:make-controller Accounting JournalEntryController --api
php artisan module:make-controller Accounting GeneralLedgerController --api
php artisan module:make-controller Accounting AccountsPayableController --api
php artisan module:make-controller Accounting AccountsReceivableController --api
php artisan module:make-controller Accounting FinancialReportController --api
php artisan module:make-model Accounting ChartOfAccount
php artisan module:make-model Accounting JournalEntry
php artisan module:make-model Accounting JournalEntryLine
php artisan module:make-model Accounting GeneralLedger
php artisan module:make-request Accounting JournalEntryStoreRequest
php artisan module:make-resource Accounting ChartOfAccountResource
php artisan module:make-resource Accounting JournalEntryResource
php artisan module:make-policy Accounting AccountingPolicy
php artisan module:make-seeder Accounting AccountingSeeder
```

### **1.2 Chart of Accounts Model**

```php
<?php
// Modules/Accounting/Entities/ChartOfAccount.php

namespace Modules\Accounting\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class ChartOfAccount extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'account_code',
        'account_name',
        'account_type_id',
        'parent_id',
        'level',
        'is_header',
        'normal_balance',
        'description',
        'is_active',
        'is_system',
        'opening_balance',
        'current_balance',
        'currency_id',
        'tax_code',
        'reconciliation_required',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_header' => 'boolean',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'reconciliation_required' => 'boolean',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'level' => 'integer',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['account_name', 'is_active', 'current_balance'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function accountType()
    {
        return $this->belongsTo(AccountType::class, 'account_type_id');
    }

    public function parent()
    {
        return $this->belongsTo(ChartOfAccount::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(ChartOfAccount::class, 'parent_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function journalEntryLines()
    {
        return $this->hasMany(JournalEntryLine::class, 'account_id');
    }

    public function generalLedgerEntries()
    {
        return $this->hasMany(GeneralLedger::class, 'account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeHeaders($query)
    {
        return $query->where('is_header', true);
    }

    public function scopeDetails($query)
    {
        return $query->where('is_header', false);
    }

    public function scopeByType($query, $typeId)
    {
        return $query->where('account_type_id', $typeId);
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('account_code', 'like', "%{$search}%")
              ->orWhere('account_name', 'like', "%{$search}%");
        });
    }

    // Accessors
    public function getFullCodeAttribute(): string
    {
        $codes = collect([$this->account_code]);
        $parent = $this->parent;
        
        while ($parent) {
            $codes->prepend($parent->account_code);
            $parent = $parent->parent;
        }
        
        return $codes->implode('.');
    }

    public function getFullNameAttribute(): string
    {
        $names = collect([$this->account_name]);
        $parent = $this->parent;
        
        while ($parent) {
            $names->prepend($parent->account_name);
            $parent = $parent->parent;
        }
        
        return $names->implode(' > ');
    }

    // Methods
    public function updateBalance(float $amount, string $type = 'debit'): void
    {
        $normalBalance = $this->normal_balance;
        
        if (($normalBalance === 'debit' && $type === 'debit') || 
            ($normalBalance === 'credit' && $type === 'credit')) {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    public function getBalanceAsOf(string $date): float
    {
        return $this->generalLedgerEntries()
            ->where('transaction_date', '<=', $date)
            ->sum(\DB::raw('CASE WHEN type = "debit" THEN amount ELSE -amount END'));
    }

    public function hasTransactions(): bool
    {
        return $this->journalEntryLines()->exists();
    }

    public function canBeDeleted(): bool
    {
        return !$this->is_system && !$this->hasTransactions() && $this->children()->count() === 0;
    }

    public static function generateAccountCode(int $parentId = null, int $accountTypeId = null): string
    {
        if ($parentId) {
            $parent = static::find($parentId);
            $lastChild = static::where('parent_id', $parentId)
                ->orderBy('account_code', 'desc')
                ->first();
            
            if ($lastChild) {
                $lastCode = (int) substr($lastChild->account_code, -2);
                $newCode = str_pad($lastCode + 1, 2, '0', STR_PAD_LEFT);
            } else {
                $newCode = '01';
            }
            
            return $parent->account_code . $newCode;
        }
        
        // Generate root level code based on account type
        $accountType = AccountType::find($accountTypeId);
        $prefix = $accountType ? $accountType->code_prefix : '1';
        
        $lastAccount = static::where('account_code', 'like', $prefix . '%')
            ->where('level', 1)
            ->orderBy('account_code', 'desc')
            ->first();
        
        if ($lastAccount) {
            $lastCode = (int) substr($lastAccount->account_code, 1);
            $newCode = $prefix . str_pad($lastCode + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newCode = $prefix . '001';
        }
        
        return $newCode;
    }
}
```

### **1.3 Journal Entry Model**

```php
<?php
// Modules/Accounting/Entities/JournalEntry.php

namespace Modules\Accounting\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class JournalEntry extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'entry_number',
        'transaction_date',
        'reference_type',
        'reference_id',
        'reference_number',
        'description',
        'total_debit',
        'total_credit',
        'currency_id',
        'exchange_rate',
        'status',
        'posted_at',
        'posted_by',
        'reversed_at',
        'reversed_by',
        'reversal_entry_id',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'total_debit' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'posted_at' => 'datetime',
        'reversed_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'total_debit', 'total_credit', 'description'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function lines()
    {
        return $this->hasMany(JournalEntryLine::class, 'journal_entry_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function reference()
    {
        return $this->morphTo();
    }

    public function postedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'posted_by');
    }

    public function reversedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'reversed_by');
    }

    public function reversalEntry()
    {
        return $this->belongsTo(JournalEntry::class, 'reversal_entry_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopePosted($query)
    {
        return $query->where('status', 'posted');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeByReference($query, $referenceType, $referenceId = null)
    {
        $query->where('reference_type', $referenceType);
        
        if ($referenceId) {
            $query->where('reference_id', $referenceId);
        }
        
        return $query;
    }

    // Accessors
    public function getIsBalancedAttribute(): bool
    {
        return abs($this->total_debit - $this->total_credit) < 0.01;
    }

    public function getIsPostedAttribute(): bool
    {
        return $this->status === 'posted';
    }

    public function getIsReversedAttribute(): bool
    {
        return !is_null($this->reversed_at);
    }

    public function getCanBePostedAttribute(): bool
    {
        return $this->status === 'draft' && $this->is_balanced && !$this->is_reversed;
    }

    public function getCanBeReversedAttribute(): bool
    {
        return $this->is_posted && !$this->is_reversed;
    }

    // Methods
    public function calculateTotals(): void
    {
        $this->total_debit = $this->lines()->sum('debit_amount');
        $this->total_credit = $this->lines()->sum('credit_amount');
        $this->save();
    }

    public function post(): bool
    {
        if (!$this->can_be_posted) {
            return false;
        }

        \DB::transaction(function () {
            // Update status
            $this->update([
                'status' => 'posted',
                'posted_at' => now(),
                'posted_by' => auth()->id(),
            ]);

            // Post to General Ledger
            foreach ($this->lines as $line) {
                GeneralLedger::create([
                    'uuid' => \Str::uuid(),
                    'account_id' => $line->account_id,
                    'journal_entry_id' => $this->id,
                    'journal_entry_line_id' => $line->id,
                    'transaction_date' => $this->transaction_date,
                    'reference_type' => $this->reference_type,
                    'reference_id' => $this->reference_id,
                    'reference_number' => $this->reference_number,
                    'description' => $line->description ?: $this->description,
                    'debit_amount' => $line->debit_amount,
                    'credit_amount' => $line->credit_amount,
                    'currency_id' => $this->currency_id,
                    'exchange_rate' => $this->exchange_rate,
                ]);

                // Update account balance
                if ($line->debit_amount > 0) {
                    $line->account->updateBalance($line->debit_amount, 'debit');
                }
                if ($line->credit_amount > 0) {
                    $line->account->updateBalance($line->credit_amount, 'credit');
                }
            }
        });

        return true;
    }

    public function reverse(string $reason = null): JournalEntry
    {
        if (!$this->can_be_reversed) {
            throw new \Exception('Journal entry cannot be reversed');
        }

        return \DB::transaction(function () use ($reason) {
            // Create reversal entry
            $reversalEntry = static::create([
                'uuid' => \Str::uuid(),
                'entry_number' => $this->generateEntryNumber(),
                'transaction_date' => now()->toDateString(),
                'reference_type' => $this->reference_type,
                'reference_id' => $this->reference_id,
                'reference_number' => $this->reference_number,
                'description' => "Reversal of {$this->entry_number}" . ($reason ? " - {$reason}" : ""),
                'total_debit' => $this->total_credit,
                'total_credit' => $this->total_debit,
                'currency_id' => $this->currency_id,
                'exchange_rate' => $this->exchange_rate,
                'status' => 'posted',
                'posted_at' => now(),
                'posted_by' => auth()->id(),
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Create reversal lines
            foreach ($this->lines as $line) {
                $reversalEntry->lines()->create([
                    'uuid' => \Str::uuid(),
                    'account_id' => $line->account_id,
                    'description' => $line->description,
                    'debit_amount' => $line->credit_amount,
                    'credit_amount' => $line->debit_amount,
                    'line_number' => $line->line_number,
                ]);
            }

            // Mark original as reversed
            $this->update([
                'reversed_at' => now(),
                'reversed_by' => auth()->id(),
                'reversal_entry_id' => $reversalEntry->id,
            ]);

            // Post reversal entry
            $reversalEntry->post();

            return $reversalEntry;
        });
    }

    public static function generateEntryNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "JE{$year}{$month}";
        
        $lastEntry = static::where('entry_number', 'like', $prefix . '%')
            ->orderBy('entry_number', 'desc')
            ->first();
        
        if ($lastEntry) {
            $lastNumber = (int) substr($lastEntry->entry_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.4 Accounting Service**

```php
<?php
// Modules/Accounting/Services/AccountingService.php

namespace Modules\Accounting\Services;

use Modules\Accounting\Entities\JournalEntry;
use Modules\Accounting\Entities\JournalEntryLine;
use Modules\Accounting\Entities\ChartOfAccount;
use Modules\Accounting\Events\JournalEntryPosted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AccountingService
{
    public function createJournalEntry(array $data): JournalEntry
    {
        return DB::transaction(function () use ($data) {
            $journalEntry = JournalEntry::create([
                'uuid' => Str::uuid(),
                'entry_number' => JournalEntry::generateEntryNumber(),
                'transaction_date' => $data['transaction_date'],
                'reference_type' => $data['reference_type'] ?? null,
                'reference_id' => $data['reference_id'] ?? null,
                'reference_number' => $data['reference_number'] ?? null,
                'description' => $data['description'],
                'currency_id' => $data['currency_id'] ?? 1,
                'exchange_rate' => $data['exchange_rate'] ?? 1.0,
                'status' => 'draft',
                'notes' => $data['notes'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            $totalDebit = 0;
            $totalCredit = 0;

            foreach ($data['lines'] as $index => $lineData) {
                $line = $journalEntry->lines()->create([
                    'uuid' => Str::uuid(),
                    'account_id' => $lineData['account_id'],
                    'description' => $lineData['description'] ?? null,
                    'debit_amount' => $lineData['debit_amount'] ?? 0,
                    'credit_amount' => $lineData['credit_amount'] ?? 0,
                    'line_number' => $index + 1,
                ]);

                $totalDebit += $line->debit_amount;
                $totalCredit += $line->credit_amount;
            }

            $journalEntry->update([
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
            ]);

            return $journalEntry;
        });
    }

    public function postJournalEntry(JournalEntry $journalEntry): bool
    {
        $result = $journalEntry->post();
        
        if ($result) {
            event(new JournalEntryPosted($journalEntry));
        }
        
        return $result;
    }

    public function createAutomaticEntry(string $referenceType, int $referenceId, array $entries): JournalEntry
    {
        $reference = $referenceType::find($referenceId);
        
        $data = [
            'transaction_date' => now()->toDateString(),
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'reference_number' => $reference->number ?? $reference->code ?? null,
            'description' => "Auto entry for {$referenceType} #{$referenceId}",
            'lines' => $entries,
        ];

        $journalEntry = $this->createJournalEntry($data);
        $this->postJournalEntry($journalEntry);

        return $journalEntry;
    }

    public function getTrialBalance(string $asOfDate = null): array
    {
        $asOfDate = $asOfDate ?: now()->toDateString();
        
        $accounts = ChartOfAccount::with('accountType')
            ->where('is_header', false)
            ->where('is_active', true)
            ->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $balance = $account->getBalanceAsOf($asOfDate);
            
            if ($balance != 0) {
                $debitBalance = $account->normal_balance === 'debit' && $balance > 0 ? $balance : 0;
                $creditBalance = $account->normal_balance === 'credit' && $balance > 0 ? $balance : 0;
                
                // Handle negative balances
                if ($balance < 0) {
                    if ($account->normal_balance === 'debit') {
                        $creditBalance = abs($balance);
                    } else {
                        $debitBalance = abs($balance);
                    }
                }

                $trialBalance[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'account_type' => $account->accountType->name,
                    'debit_balance' => $debitBalance,
                    'credit_balance' => $creditBalance,
                ];

                $totalDebits += $debitBalance;
                $totalCredits += $creditBalance;
            }
        }

        return [
            'as_of_date' => $asOfDate,
            'accounts' => $trialBalance,
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
        ];
    }

    public function getIncomeStatement(string $startDate, string $endDate): array
    {
        $revenueAccounts = ChartOfAccount::byType(4) // Revenue
            ->where('is_header', false)
            ->get();
            
        $expenseAccounts = ChartOfAccount::byType(5) // Expense
            ->where('is_header', false)
            ->get();

        $revenues = [];
        $expenses = [];
        $totalRevenue = 0;
        $totalExpense = 0;

        foreach ($revenueAccounts as $account) {
            $balance = $this->getAccountBalanceForPeriod($account, $startDate, $endDate);
            if ($balance != 0) {
                $revenues[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'amount' => abs($balance),
                ];
                $totalRevenue += abs($balance);
            }
        }

        foreach ($expenseAccounts as $account) {
            $balance = $this->getAccountBalanceForPeriod($account, $startDate, $endDate);
            if ($balance != 0) {
                $expenses[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'amount' => abs($balance),
                ];
                $totalExpense += abs($balance);
            }
        }

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'revenues' => $revenues,
            'expenses' => $expenses,
            'total_revenue' => $totalRevenue,
            'total_expense' => $totalExpense,
            'net_income' => $totalRevenue - $totalExpense,
        ];
    }

    private function getAccountBalanceForPeriod(ChartOfAccount $account, string $startDate, string $endDate): float
    {
        return $account->generalLedgerEntries()
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->sum(DB::raw('CASE WHEN debit_amount > 0 THEN debit_amount ELSE -credit_amount END'));
    }

    public function getBalanceSheet(string $asOfDate = null): array
    {
        $asOfDate = $asOfDate ?: now()->toDateString();
        
        $assets = $this->getAccountsByType(1, $asOfDate); // Assets
        $liabilities = $this->getAccountsByType(2, $asOfDate); // Liabilities
        $equity = $this->getAccountsByType(3, $asOfDate); // Equity

        $totalAssets = collect($assets)->sum('balance');
        $totalLiabilities = collect($liabilities)->sum('balance');
        $totalEquity = collect($equity)->sum('balance');

        return [
            'as_of_date' => $asOfDate,
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'total_assets' => $totalAssets,
            'total_liabilities' => $totalLiabilities,
            'total_equity' => $totalEquity,
            'total_liabilities_equity' => $totalLiabilities + $totalEquity,
            'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.01,
        ];
    }

    private function getAccountsByType(int $typeId, string $asOfDate): array
    {
        $accounts = ChartOfAccount::byType($typeId)
            ->where('is_header', false)
            ->where('is_active', true)
            ->get();

        $result = [];
        
        foreach ($accounts as $account) {
            $balance = $account->getBalanceAsOf($asOfDate);
            if ($balance != 0) {
                $result[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'balance' => abs($balance),
                ];
            }
        }

        return $result;
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create accounting structure
mkdir -p frontend/src/modules/accounting/{components,pages,hooks,services,types}
```

### **2.2 Accounting Service**

```typescript
// frontend/src/modules/accounting/services/accountingService.ts
import { apiClient } from '@/core/api/apiClient';

export interface ChartOfAccount {
  id: number;
  uuid: string;
  account_code: string;
  account_name: string;
  full_code: string;
  full_name: string;
  account_type: AccountType;
  parent?: ChartOfAccount;
  children?: ChartOfAccount[];
  level: number;
  is_header: boolean;
  normal_balance: 'debit' | 'credit';
  description?: string;
  is_active: boolean;
  is_system: boolean;
  opening_balance: number;
  current_balance: number;
  currency?: Currency;
  created_at: string;
  updated_at: string;
}

export interface AccountType {
  id: number;
  name: string;
  code_prefix: string;
  normal_balance: 'debit' | 'credit';
  category: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
}

export interface JournalEntry {
  id: number;
  uuid: string;
  entry_number: string;
  transaction_date: string;
  reference_type?: string;
  reference_id?: number;
  reference_number?: string;
  description: string;
  total_debit: number;
  total_credit: number;
  currency: Currency;
  exchange_rate: number;
  status: 'draft' | 'posted' | 'reversed';
  is_balanced: boolean;
  is_posted: boolean;
  is_reversed: boolean;
  can_be_posted: boolean;
  can_be_reversed: boolean;
  posted_at?: string;
  posted_by?: any;
  reversed_at?: string;
  reversed_by?: any;
  reversal_entry?: JournalEntry;
  notes?: string;
  lines: JournalEntryLine[];
  created_at: string;
  updated_at: string;
}

export interface JournalEntryLine {
  id: number;
  uuid: string;
  account: ChartOfAccount;
  description?: string;
  debit_amount: number;
  credit_amount: number;
  line_number: number;
}

export interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  exchange_rate: number;
}

class AccountingService {
  // Chart of Accounts
  async getChartOfAccounts(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/chart-of-accounts?${params.toString()}`);
  }

  async getAccount(id: number) {
    return apiClient.get(`/chart-of-accounts/${id}`);
  }

  async createAccount(data: any) {
    return apiClient.post('/chart-of-accounts', data);
  }

  async updateAccount(id: number, data: any) {
    return apiClient.put(`/chart-of-accounts/${id}`, data);
  }

  async deleteAccount(id: number) {
    return apiClient.delete(`/chart-of-accounts/${id}`);
  }

  async getAccountTypes() {
    return apiClient.get('/account-types');
  }

  // Journal Entries
  async getJournalEntries(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/journal-entries?${params.toString()}`);
  }

  async getJournalEntry(id: number) {
    return apiClient.get(`/journal-entries/${id}?include=lines.account`);
  }

  async createJournalEntry(data: {
    transaction_date: string;
    description: string;
    reference_type?: string;
    reference_id?: number;
    reference_number?: string;
    currency_id?: number;
    exchange_rate?: number;
    notes?: string;
    lines: Array<{
      account_id: number;
      description?: string;
      debit_amount?: number;
      credit_amount?: number;
    }>;
  }) {
    return apiClient.post('/journal-entries', data);
  }

  async updateJournalEntry(id: number, data: any) {
    return apiClient.put(`/journal-entries/${id}`, data);
  }

  async deleteJournalEntry(id: number) {
    return apiClient.delete(`/journal-entries/${id}`);
  }

  async postJournalEntry(id: number) {
    return apiClient.post(`/journal-entries/${id}/post`);
  }

  async reverseJournalEntry(id: number, reason?: string) {
    return apiClient.post(`/journal-entries/${id}/reverse`, { reason });
  }

  // General Ledger
  async getGeneralLedger(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/general-ledger?${params.toString()}`);
  }

  async getAccountLedger(accountId: number, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/general-ledger/account/${accountId}?${params.toString()}`);
  }

  // Financial Reports
  async getTrialBalance(asOfDate?: string) {
    const params = asOfDate ? `?as_of_date=${asOfDate}` : '';
    return apiClient.get(`/reports/trial-balance${params}`);
  }

  async getIncomeStatement(startDate: string, endDate: string) {
    return apiClient.get(`/reports/income-statement?start_date=${startDate}&end_date=${endDate}`);
  }

  async getBalanceSheet(asOfDate?: string) {
    const params = asOfDate ? `?as_of_date=${asOfDate}` : '';
    return apiClient.get(`/reports/balance-sheet${params}`);
  }

  async getCashFlowStatement(startDate: string, endDate: string) {
    return apiClient.get(`/reports/cash-flow?start_date=${startDate}&end_date=${endDate}`);
  }

  // Currencies
  async getCurrencies() {
    return apiClient.get('/currencies');
  }

  async getExchangeRates(date?: string) {
    const params = date ? `?date=${date}` : '';
    return apiClient.get(`/exchange-rates${params}`);
  }
}

export const accountingService = new AccountingService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/Accounting/Routes/api.php

use Modules\Accounting\Http\Controllers\ChartOfAccountsController;
use Modules\Accounting\Http\Controllers\JournalEntryController;
use Modules\Accounting\Http\Controllers\GeneralLedgerController;
use Modules\Accounting\Http\Controllers\FinancialReportController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Chart of Accounts
    Route::apiResource('chart-of-accounts', ChartOfAccountsController::class);
    Route::get('account-types', [ChartOfAccountsController::class, 'accountTypes']);
    
    // Journal Entries
    Route::apiResource('journal-entries', JournalEntryController::class);
    Route::post('journal-entries/{journalEntry}/post', [JournalEntryController::class, 'post']);
    Route::post('journal-entries/{journalEntry}/reverse', [JournalEntryController::class, 'reverse']);
    
    // General Ledger
    Route::get('general-ledger', [GeneralLedgerController::class, 'index']);
    Route::get('general-ledger/account/{account}', [GeneralLedgerController::class, 'accountLedger']);
    
    // Financial Reports
    Route::prefix('reports')->group(function () {
        Route::get('trial-balance', [FinancialReportController::class, 'trialBalance']);
        Route::get('income-statement', [FinancialReportController::class, 'incomeStatement']);
        Route::get('balance-sheet', [FinancialReportController::class, 'balanceSheet']);
        Route::get('cash-flow', [FinancialReportController::class, 'cashFlowStatement']);
    });
    
    // Currencies & Exchange Rates
    Route::get('currencies', [JournalEntryController::class, 'currencies']);
    Route::get('exchange-rates', [JournalEntryController::class, 'exchangeRates']);
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Chart of Accounts CRUD working
- [ ] Journal Entry creation and posting
- [ ] General Ledger posting automatic
- [ ] Account balance calculations accurate
- [ ] Financial reports generating correctly
- [ ] Multi-currency support working

### **Frontend**
- [ ] COA management interface working
- [ ] Journal Entry form functional
- [ ] General Ledger display working
- [ ] Financial reports visualization
- [ ] Real-time balance updates

### **Integration**
- [ ] Automatic journal posting from other modules
- [ ] Inventory valuation integration
- [ ] AP/AR integration working
- [ ] Multi-currency calculations correct

## 📞 NEXT STEPS

Setelah Accounting module selesai:

1. **Test journal posting** accuracy
2. **Verify financial reports** calculations
3. **Test multi-currency** functionality
4. **Integrate dengan inventory** untuk cost accounting
5. **Commit module** ke repository
6. **Lanjut ke** `06_SALES_MODULE.md`

---

**IMPORTANT**: Accounting adalah core financial module. Pastikan semua calculations akurat dan audit trail lengkap sebelum melanjutkan ke modul lainnya.
