# 03 - USER MANAGEMENT MODULE

## 📋 OVERVIEW

Modul User Management adalah modul pertama yang dibangun setelah core system. Modul ini mengelola users, roles, permissions, dan module access. Menggunakan RBAC (Role-Based Access Control) untuk authorization.

## 🎯 TUJUAN

- Implementasi CRUD operations untuk users
- Manajemen roles dan permissions
- Assignment roles ke users
- User profile management
- Activity logging
- Permission-based UI rendering

## ⏱️ ESTIMASI WAKTU

**Total**: 12-16 jam
- Backend implementation: 8-10 jam
- Frontend implementation: 4-6 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Backend Lead** (Review)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Main tables
users
roles  
permissions
user_roles
role_permissions

-- Supporting tables
audit_trails (untuk activity logging)
personal_access_tokens (untuk API tokens)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create User Management module
php artisan module:make UserManagement

# Create module components
php artisan module:make-controller UserManagement UserController --api
php artisan module:make-controller UserManagement RoleController --api
php artisan module:make-model UserManagement User
php artisan module:make-request UserManagement UserStoreRequest
php artisan module:make-request UserManagement UserUpdateRequest
php artisan module:make-resource UserManagement UserResource
php artisan module:make-resource UserManagement RoleResource
php artisan module:make-policy UserManagement UserPolicy
php artisan module:make-seeder UserManagement UserSeeder
```

### **1.2 User Model Enhancement**

```php
<?php
// Modules/UserManagement/Entities/User.php

namespace Modules\UserManagement\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, LogsActivity;

    protected $fillable = [
        'uuid',
        'username',
        'email',
        'password',
        'first_name',
        'last_name',
        'phone',
        'avatar',
        'timezone',
        'language',
        'status',
        'last_login_at',
        'last_login_ip',
        'password_changed_at',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'google_id',
        'facebook_id',
        'github_id',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'password_changed_at' => 'datetime',
        'two_factor_recovery_codes' => 'array',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['username', 'email', 'first_name', 'last_name', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getAvatarUrlAttribute(): ?string
    {
        if ($this->avatar) {
            return asset('storage/avatars/' . $this->avatar);
        }
        
        // Generate avatar from initials
        $initials = substr($this->first_name, 0, 1) . substr($this->last_name, 0, 1);
        return "https://ui-avatars.com/api/?name={$initials}&background=3B82F6&color=fff";
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'like', "%{$search}%")
              ->orWhere('last_name', 'like', "%{$search}%")
              ->orWhere('username', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    // Relationships
    public function sessions()
    {
        return $this->hasMany(UserSession::class);
    }
}
```

### **1.3 User Controller**

```php
<?php
// Modules/UserManagement/Http/Controllers/UserController.php

namespace Modules\UserManagement\Http\Controllers;

use App\Core\API\Controllers\BaseApiController;
use Modules\UserManagement\Entities\User;
use Modules\UserManagement\Http\Requests\UserStoreRequest;
use Modules\UserManagement\Http\Requests\UserUpdateRequest;
use Modules\UserManagement\Http\Resources\UserResource;
use Modules\UserManagement\Events\UserCreated;
use Modules\UserManagement\Events\UserUpdated;
use Modules\UserManagement\Events\UserDeleted;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class UserController extends BaseApiController
{
    protected array $allowedIncludes = ['roles', 'permissions'];
    protected array $allowedFilters = ['search', 'role', 'status', 'created_at'];
    protected array $allowedSorts = ['first_name', 'last_name', 'email', 'created_at', 'last_login_at'];

    protected function getModelClass(): string
    {
        return User::class;
    }

    protected function getResourceClass(): string
    {
        return UserResource::class;
    }

    protected function getStoreRules(): array
    {
        return [
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id'
        ];
    }

    protected function getUpdateRules($user): array
    {
        return [
            'username' => "required|string|max:50|unique:users,username,{$user->id}",
            'email' => "required|email|unique:users,email,{$user->id}",
            'password' => 'nullable|string|min:8|confirmed',
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'status' => 'in:active,inactive,suspended',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id'
        ];
    }

    protected function performStore(array $data): User
    {
        $user = User::create([
            'uuid' => Str::uuid(),
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'phone' => $data['phone'] ?? null,
            'timezone' => $data['timezone'] ?? 'Asia/Jakarta',
            'language' => $data['language'] ?? 'id',
        ]);

        if (isset($data['roles'])) {
            $user->syncRoles($data['roles']);
        }

        event(new UserCreated($user));
        
        return $user->load('roles');
    }

    protected function performUpdate($user, array $data): User
    {
        $updateData = collect($data)->except(['password', 'roles'])->toArray();
        
        if (!empty($data['password'])) {
            $updateData['password'] = Hash::make($data['password']);
            $updateData['password_changed_at'] = now();
        }

        $user->update($updateData);

        if (isset($data['roles'])) {
            $user->syncRoles($data['roles']);
        }

        event(new UserUpdated($user));
        
        return $user->load('roles');
    }

    protected function performDestroy($user): void
    {
        $user->delete();
        event(new UserDeleted($user));
    }

    public function assignRole(Request $request, User $user)
    {
        $this->authorize('assignRole', $user);
        
        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id'
        ]);

        $user->syncRoles($validated['roles']);

        return $this->successResponse([
            'data' => new UserResource($user->load('roles')),
            'message' => 'Roles assigned successfully'
        ]);
    }

    public function revokeRole(Request $request, User $user)
    {
        $this->authorize('revokeRole', $user);
        
        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id'
        ]);

        $roles = Role::whereIn('id', $validated['roles'])->get();
        $user->removeRole($roles);

        return $this->successResponse([
            'data' => new UserResource($user->load('roles')),
            'message' => 'Roles revoked successfully'
        ]);
    }

    public function profile(Request $request)
    {
        $user = $request->user()->load('roles.permissions');
        
        return $this->successResponse([
            'data' => new UserResource($user)
        ]);
    }

    public function updateProfile(Request $request)
    {
        $user = $request->user();
        
        $validated = $request->validate([
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'avatar' => 'nullable|image|max:2048'
        ]);

        if ($request->hasFile('avatar')) {
            $avatar = $request->file('avatar');
            $filename = $user->uuid . '.' . $avatar->getClientOriginalExtension();
            $avatar->storeAs('avatars', $filename, 'public');
            $validated['avatar'] = $filename;
        }

        $user->update($validated);

        return $this->successResponse([
            'data' => new UserResource($user),
            'message' => 'Profile updated successfully'
        ]);
    }
}
```

### **1.4 User Resource**

```php
<?php
// Modules/UserManagement/Http/Resources/UserResource.php

namespace Modules\UserManagement\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'username' => $this->username,
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'phone' => $this->phone,
            'avatar' => $this->avatar,
            'avatar_url' => $this->avatar_url,
            'timezone' => $this->timezone,
            'language' => $this->language,
            'status' => $this->status,
            'last_login_at' => $this->last_login_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Conditional includes
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
        ];
    }
}
```

### **1.5 User Policy**

```php
<?php
// Modules/UserManagement/Policies/UserPolicy.php

namespace Modules\UserManagement\Policies;

use Modules\UserManagement\Entities\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('users.view');
    }

    public function view(User $user, User $model): bool
    {
        return $user->hasPermissionTo('users.view') || $user->id === $model->id;
    }

    public function create(User $user): bool
    {
        return $user->hasPermissionTo('users.create');
    }

    public function update(User $user, User $model): bool
    {
        return $user->hasPermissionTo('users.edit') || $user->id === $model->id;
    }

    public function delete(User $user, User $model): bool
    {
        return $user->hasPermissionTo('users.delete') && $user->id !== $model->id;
    }

    public function assignRole(User $user, User $model): bool
    {
        return $user->hasPermissionTo('users.assign_role') && $user->id !== $model->id;
    }

    public function revokeRole(User $user, User $model): bool
    {
        return $user->hasPermissionTo('users.revoke_role') && $user->id !== $model->id;
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create user management structure
mkdir -p frontend/src/modules/user-management/{components,pages,hooks,services,types}
```

### **2.2 User Service**

```typescript
// frontend/src/modules/user-management/services/userService.ts
import { apiClient } from '@/core/api/apiClient';

export interface User {
  id: number;
  uuid: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone?: string;
  avatar?: string;
  avatar_url: string;
  timezone: string;
  language: string;
  status: 'active' | 'inactive' | 'suspended';
  last_login_at?: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
}

export interface Role {
  id: number;
  name: string;
  display_name: string;
  description?: string;
}

export interface UserFilters {
  search?: string;
  role?: string;
  status?: string;
  page?: number;
  per_page?: number;
  sort?: string;
}

export interface CreateUserData {
  username: string;
  email: string;
  password: string;
  password_confirmation: string;
  first_name: string;
  last_name: string;
  phone?: string;
  timezone?: string;
  language?: string;
  roles?: number[];
}

export interface UpdateUserData {
  username: string;
  email: string;
  password?: string;
  password_confirmation?: string;
  first_name: string;
  last_name: string;
  phone?: string;
  timezone?: string;
  language?: string;
  status?: string;
  roles?: number[];
}

class UserService {
  async getUsers(filters: UserFilters = {}) {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/users?${params.toString()}`);
  }

  async getUser(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/users/${id}${params}`);
  }

  async createUser(data: CreateUserData) {
    return apiClient.post('/users', data);
  }

  async updateUser(id: number, data: UpdateUserData) {
    return apiClient.put(`/users/${id}`, data);
  }

  async deleteUser(id: number) {
    return apiClient.delete(`/users/${id}`);
  }

  async assignRole(userId: number, roleIds: number[]) {
    return apiClient.post(`/users/${userId}/assign-role`, { roles: roleIds });
  }

  async revokeRole(userId: number, roleIds: number[]) {
    return apiClient.post(`/users/${userId}/revoke-role`, { roles: roleIds });
  }

  async getRoles() {
    return apiClient.get('/roles');
  }

  async getProfile() {
    return apiClient.get('/users/profile');
  }

  async updateProfile(data: FormData) {
    return apiClient.post('/users/profile', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}

export const userService = new UserService();
```

### **2.3 User Management Page**

```typescript
// frontend/src/modules/user-management/pages/UsersPage.tsx
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Search, Filter, Edit, Trash, Shield } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { DataTable } from '@/components/ui/DataTable';
import { Modal } from '@/components/ui/Modal';
import { Badge } from '@/components/ui/Badge';
import { Avatar } from '@/components/ui/Avatar';
import { DropdownMenu, DropdownItem } from '@/components/ui/DropdownMenu';
import { useAuth } from '@/core/stores/authStore';
import { userService, User, UserFilters } from '../services/userService';
import { UserForm } from '../components/UserForm';
import { toast } from 'react-hot-toast';

export const UsersPage: React.FC = () => {
  const { hasPermission } = useAuth();
  const queryClient = useQueryClient();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    role: '',
    status: '',
    page: 1,
    per_page: 15
  });

  const { data: usersData, isLoading } = useQuery({
    queryKey: ['users', filters],
    queryFn: () => userService.getUsers(filters),
    keepPreviousData: true
  });

  const { data: rolesData } = useQuery({
    queryKey: ['roles'],
    queryFn: () => userService.getRoles(),
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const deleteUserMutation = useMutation({
    mutationFn: userService.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries(['users']);
      toast.success('User deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete user');
    }
  });

  const columns = [
    {
      key: 'avatar',
      label: '',
      render: (user: User) => (
        <Avatar
          src={user.avatar_url}
          name={user.full_name}
          size="sm"
        />
      )
    },
    {
      key: 'full_name',
      label: 'Name',
      sortable: true,
      render: (user: User) => (
        <div>
          <div className="font-medium text-gray-900">{user.full_name}</div>
          <div className="text-sm text-gray-500">{user.username}</div>
        </div>
      )
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true
    },
    {
      key: 'roles',
      label: 'Roles',
      render: (user: User) => (
        <div className="flex flex-wrap gap-1">
          {user.roles?.map(role => (
            <Badge key={role.id} variant="secondary" size="sm">
              {role.display_name}
            </Badge>
          ))}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (user: User) => (
        <Badge
          variant={getStatusVariant(user.status)}
          text={user.status}
        />
      )
    },
    {
      key: 'last_login_at',
      label: 'Last Login',
      sortable: true,
      render: (user: User) => (
        user.last_login_at ? formatDate(user.last_login_at) : 'Never'
      )
    },
    {
      key: 'actions',
      label: '',
      render: (user: User) => (
        <DropdownMenu>
          {hasPermission('users.edit') && (
            <DropdownItem onClick={() => editUser(user)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </DropdownItem>
          )}
          {hasPermission('users.assign_role') && (
            <DropdownItem onClick={() => manageRoles(user)}>
              <Shield className="w-4 h-4 mr-2" />
              Manage Roles
            </DropdownItem>
          )}
          {hasPermission('users.delete') && (
            <DropdownItem
              variant="danger"
              onClick={() => handleDeleteUser(user)}
            >
              <Trash className="w-4 h-4 mr-2" />
              Delete
            </DropdownItem>
          )}
        </DropdownMenu>
      )
    }
  ];

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handleFilterChange = (key: keyof UserFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleDeleteUser = async (user: User) => {
    if (confirm(`Are you sure you want to delete ${user.full_name}?`)) {
      deleteUserMutation.mutate(user.id);
    }
  };

  const editUser = (user: User) => {
    setSelectedUser(user);
    setShowCreateModal(true);
  };

  const handleUserSaved = () => {
    setShowCreateModal(false);
    setSelectedUser(null);
    queryClient.invalidateQueries(['users']);
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'secondary';
      case 'suspended': return 'danger';
      default: return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users</h1>
          <p className="text-gray-600">Manage system users and permissions</p>
        </div>
        {hasPermission('users.create') && (
          <Button
            variant="primary"
            icon={<Plus className="w-4 h-4" />}
            onClick={() => setShowCreateModal(true)}
          >
            Add User
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            placeholder="Search users..."
            value={filters.search}
            onChange={(e) => handleSearch(e.target.value)}
            icon={<Search className="w-4 h-4" />}
          />
          <Select
            placeholder="Filter by role"
            value={filters.role}
            onChange={(value) => handleFilterChange('role', value)}
            options={rolesData?.data?.map(role => ({
              value: role.id.toString(),
              label: role.display_name
            })) || []}
          />
          <Select
            placeholder="Filter by status"
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            options={[
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'suspended', label: 'Suspended' }
            ]}
          />
          <Button
            variant="outline"
            onClick={() => setFilters({ search: '', role: '', status: '', page: 1, per_page: 15 })}
          >
            Clear Filters
          </Button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          columns={columns}
          data={usersData?.data || []}
          loading={isLoading}
          pagination={{
            current: usersData?.meta?.current_page || 1,
            total: usersData?.meta?.total || 0,
            pageSize: usersData?.meta?.per_page || 15,
            onChange: (page) => handleFilterChange('page', page)
          }}
          onSort={(field, direction) => {
            handleFilterChange('sort', `${field}:${direction}`);
          }}
        />
      </div>

      {/* Create/Edit Modal */}
      <Modal
        open={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setSelectedUser(null);
        }}
        title={selectedUser ? 'Edit User' : 'Create User'}
        size="lg"
      >
        <UserForm
          user={selectedUser}
          roles={rolesData?.data || []}
          onSave={handleUserSaved}
          onCancel={() => {
            setShowCreateModal(false);
            setSelectedUser(null);
          }}
        />
      </Modal>
    </div>
  );
};
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/UserManagement/Routes/api.php

use Modules\UserManagement\Http\Controllers\UserController;
use Modules\UserManagement\Http\Controllers\RoleController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // User management routes
    Route::apiResource('users', UserController::class);
    Route::post('users/{user}/assign-role', [UserController::class, 'assignRole']);
    Route::post('users/{user}/revoke-role', [UserController::class, 'revokeRole']);
    Route::get('users/profile', [UserController::class, 'profile']);
    Route::post('users/profile', [UserController::class, 'updateProfile']);
    
    // Role management routes
    Route::apiResource('roles', RoleController::class);
});
```

### **3.2 Module Configuration**

```json
{
    "name": "UserManagement",
    "alias": "usermanagement",
    "description": "User and role management module",
    "keywords": ["users", "roles", "permissions", "rbac"],
    "priority": 1,
    "providers": [
        "Modules\\UserManagement\\Providers\\UserManagementServiceProvider"
    ],
    "aliases": {},
    "files": [],
    "requires": []
}
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] User CRUD operations working
- [ ] Role assignment/revocation working
- [ ] User policies enforced
- [ ] Activity logging functional
- [ ] API responses consistent
- [ ] Validation working properly

### **Frontend**
- [ ] User list page functional
- [ ] User creation/editing working
- [ ] Role management working
- [ ] Permission-based UI rendering
- [ ] Search and filtering working
- [ ] Pagination working

### **Integration**
- [ ] Frontend-backend integration working
- [ ] Authentication required for all endpoints
- [ ] RBAC permissions enforced
- [ ] Error handling working

## 📞 NEXT STEPS

Setelah User Management module selesai:

1. **Test semua functionality** end-to-end
2. **Verify RBAC permissions** working correctly
3. **Test user creation dan role assignment**
4. **Commit module** ke repository
5. **Lanjut ke** `04_INVENTORY_MANAGEMENT.md` untuk implementasi inventory module

---

**IMPORTANT**: User Management adalah foundation untuk authorization di semua modul lainnya. Pastikan RBAC system bekerja dengan baik sebelum melanjutkan ke modul berikutnya.
