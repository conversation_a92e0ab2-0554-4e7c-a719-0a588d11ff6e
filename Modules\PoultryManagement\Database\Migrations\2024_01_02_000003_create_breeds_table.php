<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('breeds', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('breed_code')->unique();
            $table->string('breed_name');
            $table->enum('breed_type', ['layer', 'broiler', 'dual_purpose', 'ornamental'])->default('layer');
            $table->string('origin_country')->nullable();
            $table->text('description')->nullable();
            $table->json('characteristics')->nullable();
            $table->json('performance_standards')->nullable();
            $table->decimal('mature_weight_female', 8, 2)->nullable();
            $table->decimal('mature_weight_male', 8, 2)->nullable();
            $table->decimal('egg_production_peak', 5, 2)->nullable();
            $table->decimal('egg_weight_average', 5, 2)->nullable();
            $table->integer('production_period_weeks')->nullable();
            $table->decimal('feed_conversion_ratio', 5, 2)->nullable();
            $table->decimal('mortality_rate_standard', 5, 2)->nullable();
            $table->decimal('housing_density_recommended', 8, 2)->nullable();
            $table->json('temperature_range_optimal')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['breed_type', 'is_active']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('breeds');
    }
};
