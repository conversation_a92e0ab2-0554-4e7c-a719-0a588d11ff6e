# 08 - BARTER & TRADE MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Barter & Trade Management mengelola transaksi non-cash berupa pertukaran barang dengan nilai yang setara. Modul ini mengakomodasi praktik umum di industri peternakan seperti pembayaran pakan dengan telur, atau sebaliknya, serta sistem cicilan dengan produk.

## 🎯 TUJUAN

- Manajemen transaksi barter dan trade-in
- Sistem penilaian barang untuk pertukaran
- Tracking cicilan dengan produk (telur/ayam)
- Integration dengan sales dan purchasing untuk mixed payment
- Automatic accounting entries untuk transaksi barter
- Reconciliation dan settlement tracking
- Multi-party barter transactions

## ⏱️ ESTIMASI WAKTU

**Total**: 16-20 jam
- Backend implementation: 10-12 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Business Analyst** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Barter management
barter_agreements
barter_transactions
barter_items
barter_settlements

-- Trade valuations
item_valuations
market_prices
exchange_rates_items

-- Supporting tables
customers (dari Sales)
vendors (dari Purchasing)
items (dari Inventory)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Barter Trade module
php artisan module:make BarterTrade

# Create module components
php artisan module:make-controller BarterTrade BarterAgreementController --api
php artisan module:make-controller BarterTrade BarterTransactionController --api
php artisan module:make-controller BarterTrade ItemValuationController --api
php artisan module:make-controller BarterTrade BarterSettlementController --api
php artisan module:make-model BarterTrade BarterAgreement
php artisan module:make-model BarterTrade BarterTransaction
php artisan module:make-model BarterTrade BarterItem
php artisan module:make-model BarterTrade ItemValuation
php artisan module:make-request BarterTrade BarterAgreementStoreRequest
php artisan module:make-request BarterTrade BarterTransactionStoreRequest
php artisan module:make-resource BarterTrade BarterAgreementResource
php artisan module:make-resource BarterTrade BarterTransactionResource
php artisan module:make-policy BarterTrade BarterTradePolicy
php artisan module:make-seeder BarterTrade BarterTradeSeeder
```

### **1.2 Barter Agreement Model**

```php
<?php
// Modules/BarterTrade/Entities/BarterAgreement.php

namespace Modules\BarterTrade\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class BarterAgreement extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'agreement_number',
        'partner_type',
        'partner_id',
        'agreement_date',
        'start_date',
        'end_date',
        'agreement_type',
        'payment_schedule',
        'total_value',
        'currency_id',
        'exchange_method',
        'terms_conditions',
        'status',
        'approved_by',
        'approved_at',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'agreement_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'total_value' => 'decimal:2',
        'payment_schedule' => 'array',
        'terms_conditions' => 'array',
        'approved_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'total_value', 'partner_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function partner()
    {
        return $this->morphTo();
    }

    public function currency()
    {
        return $this->belongsTo(\Modules\Accounting\Entities\Currency::class);
    }

    public function barterItems()
    {
        return $this->hasMany(BarterItem::class, 'agreement_id');
    }

    public function transactions()
    {
        return $this->hasMany(BarterTransaction::class, 'agreement_id');
    }

    public function settlements()
    {
        return $this->hasMany(BarterSettlement::class, 'agreement_id');
    }

    public function approvedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now());
    }

    public function scopeByPartner($query, $partnerType, $partnerId)
    {
        return $query->where('partner_type', $partnerType)
            ->where('partner_id', $partnerId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('agreement_type', $type);
    }

    // Accessors
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' && 
               $this->start_date <= now() && 
               $this->end_date >= now();
    }

    public function getSettledValueAttribute(): float
    {
        return $this->settlements->where('status', 'completed')->sum('settlement_value');
    }

    public function getOutstandingValueAttribute(): float
    {
        return $this->total_value - $this->settled_value;
    }

    public function getCompletionPercentageAttribute(): float
    {
        return $this->total_value > 0 ? ($this->settled_value / $this->total_value) * 100 : 0;
    }

    // Methods
    public function addBarterItem(array $itemData): BarterItem
    {
        return $this->barterItems()->create([
            'uuid' => \Str::uuid(),
            'item_id' => $itemData['item_id'],
            'item_type' => $itemData['item_type'], // 'give' or 'receive'
            'quantity' => $itemData['quantity'],
            'unit_value' => $itemData['unit_value'],
            'total_value' => $itemData['quantity'] * $itemData['unit_value'],
            'valuation_method' => $itemData['valuation_method'] ?? 'market_price',
            'valuation_date' => $itemData['valuation_date'] ?? now(),
            'notes' => $itemData['notes'] ?? null,
        ]);
    }

    public function calculateTotalValue(): void
    {
        $giveValue = $this->barterItems()->where('item_type', 'give')->sum('total_value');
        $receiveValue = $this->barterItems()->where('item_type', 'receive')->sum('total_value');
        
        // Use the higher value as total agreement value
        $this->update(['total_value' => max($giveValue, $receiveValue)]);
    }

    public function approve(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->update([
            'status' => 'active',
            'approved_by' => auth()->id(),
            'approved_at' => now(),
        ]);

        return true;
    }

    public static function generateAgreementNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "BA{$year}{$month}";
        
        $lastAgreement = static::where('agreement_number', 'like', $prefix . '%')
            ->orderBy('agreement_number', 'desc')
            ->first();
        
        if ($lastAgreement) {
            $lastNumber = (int) substr($lastAgreement->agreement_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.3 Barter Transaction Model**

```php
<?php
// Modules/BarterTrade/Entities/BarterTransaction.php

namespace Modules\BarterTrade\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class BarterTransaction extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'transaction_number',
        'agreement_id',
        'reference_type',
        'reference_id',
        'transaction_date',
        'transaction_type',
        'item_id',
        'quantity',
        'unit_value',
        'total_value',
        'cash_component',
        'barter_component',
        'exchange_rate',
        'settlement_status',
        'settlement_date',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'settlement_date' => 'date',
        'quantity' => 'decimal:3',
        'unit_value' => 'decimal:2',
        'total_value' => 'decimal:2',
        'cash_component' => 'decimal:2',
        'barter_component' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['settlement_status', 'total_value', 'quantity'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function agreement()
    {
        return $this->belongsTo(BarterAgreement::class, 'agreement_id');
    }

    public function reference()
    {
        return $this->morphTo();
    }

    public function item()
    {
        return $this->belongsTo(\Modules\Inventory\Entities\Item::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('settlement_status', 'pending');
    }

    public function scopeSettled($query)
    {
        return $query->where('settlement_status', 'settled');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    // Accessors
    public function getIsSettledAttribute(): bool
    {
        return $this->settlement_status === 'settled';
    }

    public function getBarterPercentageAttribute(): float
    {
        return $this->total_value > 0 ? ($this->barter_component / $this->total_value) * 100 : 0;
    }

    public function getCashPercentageAttribute(): float
    {
        return $this->total_value > 0 ? ($this->cash_component / $this->total_value) * 100 : 0;
    }

    // Methods
    public function settle(): bool
    {
        if ($this->settlement_status === 'settled') {
            return false;
        }

        $this->update([
            'settlement_status' => 'settled',
            'settlement_date' => now(),
        ]);

        // Create settlement record
        BarterSettlement::create([
            'uuid' => \Str::uuid(),
            'agreement_id' => $this->agreement_id,
            'transaction_id' => $this->id,
            'settlement_date' => now(),
            'settlement_value' => $this->total_value,
            'settlement_method' => 'automatic',
            'status' => 'completed',
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);

        return true;
    }

    public static function generateTransactionNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "BT{$year}{$month}";
        
        $lastTransaction = static::where('transaction_number', 'like', $prefix . '%')
            ->orderBy('transaction_number', 'desc')
            ->first();
        
        if ($lastTransaction) {
            $lastNumber = (int) substr($lastTransaction->transaction_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.4 Barter Trade Service**

```php
<?php
// Modules/BarterTrade/Services/BarterTradeService.php

namespace Modules\BarterTrade\Services;

use Modules\BarterTrade\Entities\BarterAgreement;
use Modules\BarterTrade\Entities\BarterTransaction;
use Modules\BarterTrade\Entities\ItemValuation;
use Modules\BarterTrade\Events\BarterAgreementCreated;
use Modules\BarterTrade\Events\BarterTransactionSettled;
use Modules\Sales\Entities\SalesOrder;
use Modules\Purchasing\Entities\PurchaseOrder;
use Modules\Accounting\Services\AccountingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BarterTradeService
{
    protected AccountingService $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    public function createBarterAgreement(array $data): BarterAgreement
    {
        return DB::transaction(function () use ($data) {
            $agreement = BarterAgreement::create([
                'uuid' => Str::uuid(),
                'agreement_number' => BarterAgreement::generateAgreementNumber(),
                'partner_type' => $data['partner_type'],
                'partner_id' => $data['partner_id'],
                'agreement_date' => $data['agreement_date'],
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'agreement_type' => $data['agreement_type'],
                'payment_schedule' => $data['payment_schedule'] ?? null,
                'currency_id' => $data['currency_id'] ?? 1,
                'exchange_method' => $data['exchange_method'],
                'terms_conditions' => $data['terms_conditions'] ?? null,
                'status' => 'draft',
                'notes' => $data['notes'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Add barter items
            foreach ($data['barter_items'] as $itemData) {
                $agreement->addBarterItem($itemData);
            }

            // Calculate total value
            $agreement->calculateTotalValue();

            event(new BarterAgreementCreated($agreement));

            return $agreement;
        });
    }

    public function createMixedPaymentSale(array $salesData, array $barterData): array
    {
        return DB::transaction(function () use ($salesData, $barterData) {
            // Create sales order with mixed payment
            $salesOrder = SalesOrder::create(array_merge($salesData, [
                'transaction_type' => 'mixed',
                'cash_amount' => $barterData['cash_component'],
                'barter_value' => $barterData['barter_component'],
                'total_amount' => $barterData['cash_component'] + $barterData['barter_component'],
            ]));

            // Create barter transaction if barter component exists
            $barterTransaction = null;
            if ($barterData['barter_component'] > 0) {
                $barterTransaction = $this->createBarterTransaction([
                    'agreement_id' => $barterData['agreement_id'],
                    'reference_type' => SalesOrder::class,
                    'reference_id' => $salesOrder->id,
                    'transaction_type' => 'sale_barter',
                    'item_id' => $barterData['barter_item_id'],
                    'quantity' => $barterData['barter_quantity'],
                    'unit_value' => $barterData['barter_unit_value'],
                    'total_value' => $salesData['total_amount'],
                    'cash_component' => $barterData['cash_component'],
                    'barter_component' => $barterData['barter_component'],
                ]);
            }

            // Create accounting entries
            $this->createMixedPaymentAccountingEntries($salesOrder, $barterTransaction);

            return [
                'sales_order' => $salesOrder,
                'barter_transaction' => $barterTransaction,
            ];
        });
    }

    public function createMixedPaymentPurchase(array $purchaseData, array $barterData): array
    {
        return DB::transaction(function () use ($purchaseData, $barterData) {
            // Create purchase order with mixed payment
            $purchaseOrder = PurchaseOrder::create(array_merge($purchaseData, [
                'transaction_type' => 'mixed',
                'cash_amount' => $barterData['cash_component'],
                'barter_value' => $barterData['barter_component'],
                'total_amount' => $barterData['cash_component'] + $barterData['barter_component'],
            ]));

            // Create barter transaction if barter component exists
            $barterTransaction = null;
            if ($barterData['barter_component'] > 0) {
                $barterTransaction = $this->createBarterTransaction([
                    'agreement_id' => $barterData['agreement_id'],
                    'reference_type' => PurchaseOrder::class,
                    'reference_id' => $purchaseOrder->id,
                    'transaction_type' => 'purchase_barter',
                    'item_id' => $barterData['barter_item_id'],
                    'quantity' => $barterData['barter_quantity'],
                    'unit_value' => $barterData['barter_unit_value'],
                    'total_value' => $purchaseData['total_amount'],
                    'cash_component' => $barterData['cash_component'],
                    'barter_component' => $barterData['barter_component'],
                ]);
            }

            // Create accounting entries
            $this->createMixedPaymentAccountingEntries($purchaseOrder, $barterTransaction);

            return [
                'purchase_order' => $purchaseOrder,
                'barter_transaction' => $barterTransaction,
            ];
        });
    }

    private function createBarterTransaction(array $data): BarterTransaction
    {
        return BarterTransaction::create([
            'uuid' => Str::uuid(),
            'transaction_number' => BarterTransaction::generateTransactionNumber(),
            'agreement_id' => $data['agreement_id'],
            'reference_type' => $data['reference_type'],
            'reference_id' => $data['reference_id'],
            'transaction_date' => now()->toDateString(),
            'transaction_type' => $data['transaction_type'],
            'item_id' => $data['item_id'],
            'quantity' => $data['quantity'],
            'unit_value' => $data['unit_value'],
            'total_value' => $data['total_value'],
            'cash_component' => $data['cash_component'],
            'barter_component' => $data['barter_component'],
            'settlement_status' => 'pending',
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);
    }

    private function createMixedPaymentAccountingEntries($order, ?BarterTransaction $barterTransaction): void
    {
        $entries = [];

        // Cash component entries
        if ($order->cash_amount > 0) {
            if ($order instanceof SalesOrder) {
                // Sales with cash
                $entries[] = [
                    'account_id' => config('barter.accounts.accounts_receivable'),
                    'description' => "Cash component - Sale to {$order->customer->name}",
                    'debit_amount' => $order->cash_amount,
                    'credit_amount' => 0,
                ];
                $entries[] = [
                    'account_id' => config('barter.accounts.sales_revenue'),
                    'description' => "Cash component - Sale to {$order->customer->name}",
                    'debit_amount' => 0,
                    'credit_amount' => $order->cash_amount,
                ];
            } else {
                // Purchase with cash
                $entries[] = [
                    'account_id' => config('barter.accounts.inventory'),
                    'description' => "Cash component - Purchase from {$order->vendor->vendor_name}",
                    'debit_amount' => $order->cash_amount,
                    'credit_amount' => 0,
                ];
                $entries[] = [
                    'account_id' => config('barter.accounts.accounts_payable'),
                    'description' => "Cash component - Purchase from {$order->vendor->vendor_name}",
                    'debit_amount' => 0,
                    'credit_amount' => $order->cash_amount,
                ];
            }
        }

        // Barter component entries
        if ($barterTransaction && $barterTransaction->barter_component > 0) {
            if ($order instanceof SalesOrder) {
                // Sales with barter
                $entries[] = [
                    'account_id' => config('barter.accounts.barter_receivable'),
                    'description' => "Barter component - Sale to {$order->customer->name}",
                    'debit_amount' => $barterTransaction->barter_component,
                    'credit_amount' => 0,
                ];
                $entries[] = [
                    'account_id' => config('barter.accounts.sales_revenue'),
                    'description' => "Barter component - Sale to {$order->customer->name}",
                    'debit_amount' => 0,
                    'credit_amount' => $barterTransaction->barter_component,
                ];
            } else {
                // Purchase with barter
                $entries[] = [
                    'account_id' => config('barter.accounts.inventory'),
                    'description' => "Barter component - Purchase from {$order->vendor->vendor_name}",
                    'debit_amount' => $barterTransaction->barter_component,
                    'credit_amount' => 0,
                ];
                $entries[] = [
                    'account_id' => config('barter.accounts.barter_payable'),
                    'description' => "Barter component - Purchase from {$order->vendor->vendor_name}",
                    'debit_amount' => 0,
                    'credit_amount' => $barterTransaction->barter_component,
                ];
            }
        }

        $this->accountingService->createAutomaticEntry(
            get_class($order),
            $order->id,
            $entries
        );
    }

    public function settleBarterTransaction(BarterTransaction $transaction): bool
    {
        $result = $transaction->settle();
        
        if ($result) {
            // Create settlement accounting entries
            $this->createSettlementAccountingEntries($transaction);
            
            event(new BarterTransactionSettled($transaction));
        }
        
        return $result;
    }

    private function createSettlementAccountingEntries(BarterTransaction $transaction): void
    {
        $entries = [];

        if ($transaction->transaction_type === 'sale_barter') {
            // Settlement of barter receivable
            $entries[] = [
                'account_id' => config('barter.accounts.inventory'),
                'description' => "Barter settlement - Received {$transaction->item->name}",
                'debit_amount' => $transaction->barter_component,
                'credit_amount' => 0,
            ];
            $entries[] = [
                'account_id' => config('barter.accounts.barter_receivable'),
                'description' => "Barter settlement - Received {$transaction->item->name}",
                'debit_amount' => 0,
                'credit_amount' => $transaction->barter_component,
            ];
        } else {
            // Settlement of barter payable
            $entries[] = [
                'account_id' => config('barter.accounts.barter_payable'),
                'description' => "Barter settlement - Delivered {$transaction->item->name}",
                'debit_amount' => $transaction->barter_component,
                'credit_amount' => 0,
            ];
            $entries[] = [
                'account_id' => config('barter.accounts.inventory'),
                'description' => "Barter settlement - Delivered {$transaction->item->name}",
                'debit_amount' => 0,
                'credit_amount' => $transaction->barter_component,
            ];
        }

        $this->accountingService->createAutomaticEntry(
            BarterTransaction::class,
            $transaction->id,
            $entries
        );
    }

    public function getBarterAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();

        $transactions = BarterTransaction::byDateRange($startDate, $endDate)->get();
        $agreements = BarterAgreement::with('transactions')->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => [
                'total_barter_value' => $transactions->sum('barter_component'),
                'total_cash_value' => $transactions->sum('cash_component'),
                'total_transactions' => $transactions->count(),
                'settled_transactions' => $transactions->where('settlement_status', 'settled')->count(),
                'pending_settlements' => $transactions->where('settlement_status', 'pending')->sum('barter_component'),
            ],
            'by_partner' => $this->getBarterByPartner($transactions),
            'by_item' => $this->getBarterByItem($transactions),
            'settlement_trend' => $this->getSettlementTrend($transactions),
        ];
    }

    private function getBarterByPartner($transactions): array
    {
        return $transactions->groupBy(function ($transaction) {
            return $transaction->agreement->partner_type . '_' . $transaction->agreement->partner_id;
        })->map(function ($partnerTransactions) {
            $agreement = $partnerTransactions->first()->agreement;
            return [
                'partner_name' => $agreement->partner->name ?? $agreement->partner->vendor_name,
                'partner_type' => $agreement->partner_type,
                'total_barter_value' => $partnerTransactions->sum('barter_component'),
                'total_cash_value' => $partnerTransactions->sum('cash_component'),
                'transaction_count' => $partnerTransactions->count(),
            ];
        })->values()->toArray();
    }

    private function getBarterByItem($transactions): array
    {
        return $transactions->groupBy('item_id')->map(function ($itemTransactions) {
            $item = $itemTransactions->first()->item;
            return [
                'item_name' => $item->name,
                'total_quantity' => $itemTransactions->sum('quantity'),
                'total_value' => $itemTransactions->sum('barter_component'),
                'average_unit_value' => $itemTransactions->avg('unit_value'),
                'transaction_count' => $itemTransactions->count(),
            ];
        })->values()->toArray();
    }

    private function getSettlementTrend($transactions): array
    {
        return $transactions->groupBy(function ($transaction) {
            return $transaction->transaction_date->format('Y-m');
        })->map(function ($monthTransactions, $month) {
            return [
                'month' => $month,
                'total_value' => $monthTransactions->sum('total_value'),
                'barter_value' => $monthTransactions->sum('barter_component'),
                'cash_value' => $monthTransactions->sum('cash_component'),
                'settled_count' => $monthTransactions->where('settlement_status', 'settled')->count(),
                'pending_count' => $monthTransactions->where('settlement_status', 'pending')->count(),
            ];
        })->values()->toArray();
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Barter Agreement Form**

```typescript
// frontend/src/modules/barter-trade/components/BarterAgreementForm.tsx
import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Card,
  Table,
  Space,
  Modal,
  message,
} from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { barterTradeService } from '../services/barterTradeService';

interface BarterItem {
  item_id: number;
  item_type: 'give' | 'receive';
  quantity: number;
  unit_value: number;
  total_value: number;
  valuation_method: string;
  notes?: string;
}

const BarterAgreementForm: React.FC = () => {
  const [form] = Form.useForm();
  const [barterItems, setBarterItems] = useState<BarterItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [partners, setPartners] = useState([]);
  const [items, setItems] = useState([]);

  useEffect(() => {
    loadMasterData();
  }, []);

  const loadMasterData = async () => {
    try {
      const [partnersRes, itemsRes] = await Promise.all([
        barterTradeService.getPartners(),
        barterTradeService.getItems(),
      ]);
      setPartners(partnersRes.data);
      setItems(itemsRes.data);
    } catch (error) {
      message.error('Failed to load master data');
    }
  };

  const addBarterItem = () => {
    const newItem: BarterItem = {
      item_id: 0,
      item_type: 'give',
      quantity: 0,
      unit_value: 0,
      total_value: 0,
      valuation_method: 'market_price',
    };
    setBarterItems([...barterItems, newItem]);
  };

  const updateBarterItem = (index: number, field: keyof BarterItem, value: any) => {
    const updatedItems = [...barterItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate total value
    if (field === 'quantity' || field === 'unit_value') {
      updatedItems[index].total_value = updatedItems[index].quantity * updatedItems[index].unit_value;
    }
    
    setBarterItems(updatedItems);
  };

  const removeBarterItem = (index: number) => {
    const updatedItems = barterItems.filter((_, i) => i !== index);
    setBarterItems(updatedItems);
  };

  const handleSubmit = async (values: any) => {
    if (barterItems.length === 0) {
      message.error('Please add at least one barter item');
      return;
    }

    setLoading(true);
    try {
      const agreementData = {
        ...values,
        barter_items: barterItems,
      };

      await barterTradeService.createBarterAgreement(agreementData);
      message.success('Barter agreement created successfully');
      form.resetFields();
      setBarterItems([]);
    } catch (error) {
      message.error('Failed to create barter agreement');
    } finally {
      setLoading(false);
    }
  };

  const barterItemColumns = [
    {
      title: 'Item',
      dataIndex: 'item_id',
      render: (value: number, record: BarterItem, index: number) => (
        <Select
          value={value}
          onChange={(val) => updateBarterItem(index, 'item_id', val)}
          style={{ width: '100%' }}
          placeholder="Select item"
        >
          {items.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'item_type',
      render: (value: string, record: BarterItem, index: number) => (
        <Select
          value={value}
          onChange={(val) => updateBarterItem(index, 'item_type', val)}
          style={{ width: '100%' }}
        >
          <Select.Option value="give">Give</Select.Option>
          <Select.Option value="receive">Receive</Select.Option>
        </Select>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      render: (value: number, record: BarterItem, index: number) => (
        <InputNumber
          value={value}
          onChange={(val) => updateBarterItem(index, 'quantity', val || 0)}
          style={{ width: '100%' }}
          min={0}
        />
      ),
    },
    {
      title: 'Unit Value',
      dataIndex: 'unit_value',
      render: (value: number, record: BarterItem, index: number) => (
        <InputNumber
          value={value}
          onChange={(val) => updateBarterItem(index, 'unit_value', val || 0)}
          style={{ width: '100%' }}
          min={0}
          precision={2}
        />
      ),
    },
    {
      title: 'Total Value',
      dataIndex: 'total_value',
      render: (value: number) => `Rp ${value.toLocaleString()}`,
    },
    {
      title: 'Action',
      render: (_: any, record: BarterItem, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeBarterItem(index)}
        />
      ),
    },
  ];

  const totalGiveValue = barterItems
    .filter(item => item.item_type === 'give')
    .reduce((sum, item) => sum + item.total_value, 0);

  const totalReceiveValue = barterItems
    .filter(item => item.item_type === 'receive')
    .reduce((sum, item) => sum + item.total_value, 0);

  return (
    <Card title="Create Barter Agreement">
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
          <Form.Item
            name="partner_type"
            label="Partner Type"
            rules={[{ required: true, message: 'Please select partner type' }]}
          >
            <Select placeholder="Select partner type">
              <Select.Option value="customer">Customer</Select.Option>
              <Select.Option value="vendor">Vendor</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="partner_id"
            label="Partner"
            rules={[{ required: true, message: 'Please select partner' }]}
          >
            <Select placeholder="Select partner">
              {partners.map((partner: any) => (
                <Select.Option key={partner.id} value={partner.id}>
                  {partner.name || partner.vendor_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="agreement_type"
            label="Agreement Type"
            rules={[{ required: true, message: 'Please select agreement type' }]}
          >
            <Select placeholder="Select agreement type">
              <Select.Option value="feed_for_eggs">Feed for Eggs</Select.Option>
              <Select.Option value="eggs_for_feed">Eggs for Feed</Select.Option>
              <Select.Option value="mixed_payment">Mixed Payment</Select.Option>
              <Select.Option value="installment">Installment Payment</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="exchange_method"
            label="Exchange Method"
            rules={[{ required: true, message: 'Please select exchange method' }]}
          >
            <Select placeholder="Select exchange method">
              <Select.Option value="immediate">Immediate Exchange</Select.Option>
              <Select.Option value="scheduled">Scheduled Exchange</Select.Option>
              <Select.Option value="on_demand">On Demand</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="start_date"
            label="Start Date"
            rules={[{ required: true, message: 'Please select start date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="end_date"
            label="End Date"
            rules={[{ required: true, message: 'Please select end date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        </div>

        <Form.Item name="notes" label="Notes">
          <Input.TextArea rows={3} placeholder="Additional notes..." />
        </Form.Item>

        <Card
          title="Barter Items"
          extra={
            <Button type="primary" icon={<PlusOutlined />} onClick={addBarterItem}>
              Add Item
            </Button>
          }
        >
          <Table
            dataSource={barterItems}
            columns={barterItemColumns}
            pagination={false}
            rowKey={(record, index) => index!}
          />

          {barterItems.length > 0 && (
            <div style={{ marginTop: '16px', padding: '16px', backgroundColor: '#f5f5f5' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
                <div>
                  <strong>Total Give Value: Rp {totalGiveValue.toLocaleString()}</strong>
                </div>
                <div>
                  <strong>Total Receive Value: Rp {totalReceiveValue.toLocaleString()}</strong>
                </div>
                <div>
                  <strong>
                    Balance: Rp {Math.abs(totalGiveValue - totalReceiveValue).toLocaleString()}
                    {totalGiveValue !== totalReceiveValue && (
                      <span style={{ color: totalGiveValue > totalReceiveValue ? 'red' : 'green' }}>
                        {' '}({totalGiveValue > totalReceiveValue ? 'Deficit' : 'Surplus'})
                      </span>
                    )}
                  </strong>
                </div>
              </div>
            </div>
          )}
        </Card>

        <Form.Item style={{ marginTop: '24px' }}>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              Create Agreement
            </Button>
            <Button onClick={() => form.resetFields()}>
              Reset
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default BarterAgreementForm;
```

## 🔗 STEP 3: INTEGRATION WITH EXISTING MODULES

### **3.1 Update Sales Module untuk Mixed Payment**

```php
// Update Modules/Sales/Entities/SalesOrder.php
protected $fillable = [
    // ... existing fields
    'transaction_type', // 'cash', 'barter', 'mixed'
    'barter_agreement_id',
    'cash_amount',
    'barter_value',
    'barter_settlement_status',
];

protected $casts = [
    // ... existing casts
    'cash_amount' => 'decimal:2',
    'barter_value' => 'decimal:2',
];

// Add relationship
public function barterAgreement()
{
    return $this->belongsTo(\Modules\BarterTrade\Entities\BarterAgreement::class, 'barter_agreement_id');
}

public function barterTransactions()
{
    return $this->morphMany(\Modules\BarterTrade\Entities\BarterTransaction::class, 'reference');
}
```

### **3.2 Update Purchasing Module untuk Mixed Payment**

```php
// Update Modules/Purchasing/Entities/PurchaseOrder.php
protected $fillable = [
    // ... existing fields
    'transaction_type', // 'cash', 'barter', 'mixed'
    'barter_agreement_id',
    'cash_amount',
    'barter_value',
    'barter_settlement_status',
];

// Add same relationships as Sales
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Barter agreement CRUD working
- [ ] Mixed payment transactions
- [ ] Automatic accounting entries
- [ ] Settlement tracking
- [ ] Integration dengan sales/purchasing
- [ ] Valuation management

### **Frontend**
- [ ] Barter agreement form
- [ ] Mixed payment interface
- [ ] Settlement tracking
- [ ] Analytics dashboard
- [ ] Integration dengan existing forms

### **Integration**
- [ ] Sales dengan barter component
- [ ] Purchasing dengan barter component
- [ ] Accounting entries accurate
- [ ] Settlement reconciliation
- [ ] Reporting integration

## 📞 NEXT STEPS

Setelah Barter Trade module selesai:

1. **Test barter workflows** end-to-end
2. **Verify accounting integration**
3. **Test mixed payment scenarios**
4. **Validate settlement tracking**
5. **Update existing modules** untuk support barter
6. **Integrate dengan mobile app**

---

**IMPORTANT**: Barter trade adalah fitur critical untuk industri peternakan. Pastikan valuation accurate, accounting entries correct, dan settlement tracking reliable untuk business operations yang smooth.
