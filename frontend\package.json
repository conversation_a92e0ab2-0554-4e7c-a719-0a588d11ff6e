{"name": "erp-poultry-frontend", "version": "1.0.0", "description": "Frontend application for ERP Poultry Management System", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.34", "@types/react": "^18.2.7", "@types/react-dom": "^18.2.4", "antd": "^5.6.1", "axios": "^1.4.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.44.3", "react-query": "^3.39.3", "react-redux": "^8.1.0", "react-router-dom": "^6.12.1", "react-scripts": "^5.0.1", "recharts": "^2.6.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.8"}, "proxy": "http://localhost:8000"}