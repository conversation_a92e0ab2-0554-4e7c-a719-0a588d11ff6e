# 03 - POULTRY MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Poultry Management adalah core module yang mengelola semua aspek peternakan ayam petelur, mulai dari farm setup, house management, flock lifecycle, breed management, hingga environmental monitoring. Modul ini menjadi foundation untuk semua module lainnya.

## 🎯 TUJUAN

- Farm dan house management yang comprehensive
- Flock lifecycle tracking dari DOC hingga afkir
- Breed management dan genetic tracking
- Environmental monitoring dan control
- Capacity planning dan optimization
- Multi-farm operations support
- Integration dengan production modules

## ⏱️ ESTIMASI WAKTU

**Total**: 20-24 jam
- Backend implementation: 12-16 jam
- Frontend implementation: 8-10 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Poultry Specialist** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Farm management
farms
farm_locations
farm_contacts

-- House management
houses
house_specifications
house_equipment

-- Flock management
flocks
flock_movements
flock_transfers

-- Breed management
breeds
breed_characteristics
breed_performance_standards

-- Environmental monitoring
environmental_readings
environmental_alerts
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Poultry Management module
php artisan module:make PoultryManagement

# Create module components
php artisan module:make-controller PoultryManagement FarmController --api
php artisan module:make-controller PoultryManagement HouseController --api
php artisan module:make-controller PoultryManagement FlockController --api
php artisan module:make-controller PoultryManagement BreedController --api
php artisan module:make-model PoultryManagement Farm
php artisan module:make-model PoultryManagement House
php artisan module:make-model PoultryManagement Flock
php artisan module:make-model PoultryManagement Breed
php artisan module:make-request PoultryManagement FarmStoreRequest
php artisan module:make-request PoultryManagement FlockStoreRequest
php artisan module:make-resource PoultryManagement FarmResource
php artisan module:make-resource PoultryManagement FlockResource
php artisan module:make-policy PoultryManagement PoultryPolicy
php artisan module:make-seeder PoultryManagement PoultryManagementSeeder
```

### **1.2 Farm Model**

```php
<?php
// Modules/PoultryManagement/Entities/Farm.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Farm extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'farm_code',
        'farm_name',
        'farm_type',
        'owner_name',
        'manager_id',
        'address',
        'city',
        'province',
        'postal_code',
        'country',
        'latitude',
        'longitude',
        'total_area_hectares',
        'established_date',
        'license_number',
        'certification',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'established_date' => 'date',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'total_area_hectares' => 'decimal:2',
        'certification' => 'array',
        'deleted_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['farm_name', 'status', 'manager_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function manager()
    {
        return $this->belongsTo(\App\Models\User::class, 'manager_id');
    }

    public function houses()
    {
        return $this->hasMany(House::class);
    }

    public function flocks()
    {
        return $this->hasManyThrough(Flock::class, House::class);
    }

    public function users()
    {
        return $this->belongsToMany(\App\Models\User::class, 'user_farms');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('farm_type', $type);
    }

    public function scopeByManager($query, $managerId)
    {
        return $query->where('manager_id', $managerId);
    }

    // Accessors
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getTotalHousesAttribute(): int
    {
        return $this->houses()->count();
    }

    public function getTotalCapacityAttribute(): int
    {
        return $this->houses()->sum('capacity');
    }

    public function getCurrentPopulationAttribute(): int
    {
        return $this->flocks()->where('status', 'active')->sum('current_count');
    }

    public function getOccupancyRateAttribute(): float
    {
        return $this->total_capacity > 0 ? ($this->current_population / $this->total_capacity) * 100 : 0;
    }

    public function getFullAddressAttribute(): string
    {
        return trim("{$this->address}, {$this->city}, {$this->province} {$this->postal_code}");
    }

    // Methods
    public function addHouse(array $houseData): House
    {
        return $this->houses()->create(array_merge($houseData, [
            'uuid' => \Str::uuid(),
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]));
    }

    public function calculateUtilization(): array
    {
        $houses = $this->houses()->with('currentFlock')->get();
        
        return [
            'total_houses' => $houses->count(),
            'occupied_houses' => $houses->where('currentFlock')->count(),
            'total_capacity' => $houses->sum('capacity'),
            'current_population' => $houses->sum('currentFlock.current_count'),
            'utilization_percentage' => $this->occupancy_rate,
        ];
    }

    public static function generateFarmCode(): string
    {
        $year = now()->year;
        $prefix = "FARM{$year}";
        
        $lastFarm = static::where('farm_code', 'like', $prefix . '%')
            ->orderBy('farm_code', 'desc')
            ->first();
        
        if ($lastFarm) {
            $lastNumber = (int) substr($lastFarm->farm_code, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.3 House Model**

```php
<?php
// Modules/PoultryManagement/Entities/House.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class House extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'farm_id',
        'house_number',
        'house_name',
        'house_type',
        'capacity',
        'length_meters',
        'width_meters',
        'height_meters',
        'construction_date',
        'last_renovation_date',
        'ventilation_type',
        'lighting_type',
        'feeding_system',
        'watering_system',
        'waste_management',
        'climate_control',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'construction_date' => 'date',
        'last_renovation_date' => 'date',
        'capacity' => 'integer',
        'length_meters' => 'decimal:2',
        'width_meters' => 'decimal:2',
        'height_meters' => 'decimal:2',
        'climate_control' => 'array',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['house_name', 'status', 'capacity'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function farm()
    {
        return $this->belongsTo(Farm::class);
    }

    public function flocks()
    {
        return $this->hasMany(Flock::class);
    }

    public function currentFlock()
    {
        return $this->hasOne(Flock::class)->where('status', 'active');
    }

    public function assignedUsers()
    {
        return $this->belongsToMany(\App\Models\User::class, 'user_houses');
    }

    public function environmentalReadings()
    {
        return $this->hasMany(\Modules\PoultryManagement\Entities\EnvironmentalReading::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeAvailable($query)
    {
        return $query->where('status', 'active')
            ->whereDoesntHave('currentFlock');
    }

    public function scopeOccupied($query)
    {
        return $query->where('status', 'active')
            ->whereHas('currentFlock');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('house_type', $type);
    }

    // Accessors
    public function getIsAvailableAttribute(): bool
    {
        return $this->status === 'active' && !$this->currentFlock;
    }

    public function getIsOccupiedAttribute(): bool
    {
        return $this->status === 'active' && $this->currentFlock;
    }

    public function getCurrentPopulationAttribute(): int
    {
        return $this->currentFlock ? $this->currentFlock->current_count : 0;
    }

    public function getOccupancyRateAttribute(): float
    {
        return $this->capacity > 0 ? ($this->current_population / $this->capacity) * 100 : 0;
    }

    public function getFloorAreaAttribute(): float
    {
        return $this->length_meters * $this->width_meters;
    }

    public function getDensityPerSquareMeterAttribute(): float
    {
        return $this->floor_area > 0 ? $this->current_population / $this->floor_area : 0;
    }

    public function getAgeInYearsAttribute(): float
    {
        return $this->construction_date ? $this->construction_date->diffInYears(now()) : 0;
    }

    // Methods
    public function placeFlock(Flock $flock): bool
    {
        if (!$this->is_available) {
            return false;
        }

        if ($flock->initial_count > $this->capacity) {
            return false;
        }

        $flock->update(['house_id' => $this->id]);
        return true;
    }

    public function getLatestEnvironmentalReading(): ?array
    {
        $latest = $this->environmentalReadings()
            ->latest('reading_datetime')
            ->first();

        return $latest ? [
            'temperature' => $latest->temperature,
            'humidity' => $latest->humidity,
            'ammonia_level' => $latest->ammonia_level,
            'reading_time' => $latest->reading_datetime,
        ] : null;
    }

    public function calculateMaintenanceSchedule(): array
    {
        $lastRenovation = $this->last_renovation_date ?: $this->construction_date;
        $nextMaintenance = $lastRenovation->addYear();
        
        return [
            'last_maintenance' => $lastRenovation,
            'next_maintenance' => $nextMaintenance,
            'days_until_maintenance' => now()->diffInDays($nextMaintenance, false),
            'maintenance_overdue' => $nextMaintenance->isPast(),
        ];
    }
}
```

### **1.4 Flock Model**

```php
<?php
// Modules/PoultryManagement/Entities/Flock.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Flock extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'flock_number',
        'house_id',
        'breed_id',
        'placement_date',
        'source',
        'supplier',
        'initial_count',
        'current_count',
        'production_stage',
        'expected_production_start',
        'expected_culling_date',
        'actual_culling_date',
        'mortality_total',
        'culled_total',
        'transferred_out',
        'transferred_in',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'placement_date' => 'date',
        'expected_production_start' => 'date',
        'expected_culling_date' => 'date',
        'actual_culling_date' => 'date',
        'initial_count' => 'integer',
        'current_count' => 'integer',
        'mortality_total' => 'integer',
        'culled_total' => 'integer',
        'transferred_out' => 'integer',
        'transferred_in' => 'integer',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['current_count', 'status', 'production_stage'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function house()
    {
        return $this->belongsTo(House::class);
    }

    public function breed()
    {
        return $this->belongsTo(Breed::class);
    }

    public function farm()
    {
        return $this->hasOneThrough(Farm::class, House::class, 'id', 'id', 'house_id', 'farm_id');
    }

    public function productionRecords()
    {
        return $this->hasMany(\Modules\EggProduction\Entities\EggProductionRecord::class);
    }

    public function feedConsumptionRecords()
    {
        return $this->hasMany(\Modules\FeedManagement\Entities\FeedConsumptionRecord::class);
    }

    public function healthRecords()
    {
        return $this->hasMany(\Modules\HealthManagement\Entities\HealthRecord::class);
    }

    public function movements()
    {
        return $this->hasMany(FlockMovement::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInProduction($query)
    {
        return $query->where('production_stage', 'laying');
    }

    public function scopeByStage($query, $stage)
    {
        return $query->where('production_stage', $stage);
    }

    public function scopeByBreed($query, $breedId)
    {
        return $query->where('breed_id', $breedId);
    }

    // Accessors
    public function getAgeWeeksAttribute(): int
    {
        return $this->placement_date ? $this->placement_date->diffInWeeks(now()) : 0;
    }

    public function getAgeDaysAttribute(): int
    {
        return $this->placement_date ? $this->placement_date->diffInDays(now()) : 0;
    }

    public function getIsInProductionAttribute(): bool
    {
        return $this->production_stage === 'laying';
    }

    public function getSurvivalRateAttribute(): float
    {
        return $this->initial_count > 0 ? ($this->current_count / $this->initial_count) * 100 : 0;
    }

    public function getMortalityRateAttribute(): float
    {
        return $this->initial_count > 0 ? ($this->mortality_total / $this->initial_count) * 100 : 0;
    }

    public function getProductionWeeksAttribute(): int
    {
        if (!$this->expected_production_start) return 0;
        
        $startDate = $this->expected_production_start;
        $endDate = $this->actual_culling_date ?: $this->expected_culling_date ?: now();
        
        return $startDate->diffInWeeks($endDate);
    }

    public function getCurrentProductionWeekAttribute(): int
    {
        if (!$this->expected_production_start || now() < $this->expected_production_start) {
            return 0;
        }
        
        return $this->expected_production_start->diffInWeeks(now()) + 1;
    }

    // Methods
    public function updatePopulation(int $mortality = 0, int $culled = 0, int $transferred = 0): void
    {
        $this->increment('mortality_total', $mortality);
        $this->increment('culled_total', $culled);
        
        if ($transferred > 0) {
            $this->increment('transferred_out', $transferred);
        } elseif ($transferred < 0) {
            $this->increment('transferred_in', abs($transferred));
        }
        
        $newCount = $this->initial_count + $this->transferred_in - $this->mortality_total - $this->culled_total - $this->transferred_out;
        $this->update(['current_count' => max(0, $newCount)]);
        
        // Record movement
        $this->movements()->create([
            'uuid' => \Str::uuid(),
            'movement_date' => now()->toDateString(),
            'movement_type' => $mortality > 0 ? 'mortality' : ($culled > 0 ? 'culling' : 'transfer'),
            'quantity' => $mortality + $culled + abs($transferred),
            'reason' => $mortality > 0 ? 'Natural mortality' : ($culled > 0 ? 'Culling' : 'Transfer'),
            'created_by' => auth()->id(),
        ]);
    }

    public function transferToHouse(House $newHouse): bool
    {
        if (!$newHouse->is_available) {
            return false;
        }

        if ($this->current_count > $newHouse->capacity) {
            return false;
        }

        $oldHouseId = $this->house_id;
        $this->update(['house_id' => $newHouse->id]);

        // Record transfer movement
        $this->movements()->create([
            'uuid' => \Str::uuid(),
            'movement_date' => now()->toDateString(),
            'movement_type' => 'transfer',
            'from_house_id' => $oldHouseId,
            'to_house_id' => $newHouse->id,
            'quantity' => $this->current_count,
            'reason' => 'House transfer',
            'created_by' => auth()->id(),
        ]);

        return true;
    }

    public function updateProductionStage(): void
    {
        $ageWeeks = $this->age_weeks;
        
        if ($ageWeeks < 18) {
            $stage = 'growing';
        } elseif ($ageWeeks < 20) {
            $stage = 'pre_laying';
        } elseif ($ageWeeks < 72) {
            $stage = 'laying';
        } else {
            $stage = 'post_laying';
        }
        
        if ($this->production_stage !== $stage) {
            $this->update(['production_stage' => $stage]);
        }
    }

    public function calculatePerformanceMetrics(): array
    {
        $productionRecords = $this->productionRecords()
            ->where('record_date', '>=', now()->subDays(30))
            ->get();

        $totalEggs = $productionRecords->sum('eggs_collected');
        $avgDailyEggs = $productionRecords->avg('eggs_collected');
        $henDayProduction = $this->current_count > 0 ? ($avgDailyEggs / $this->current_count) * 100 : 0;

        return [
            'total_eggs_30_days' => $totalEggs,
            'avg_daily_eggs' => round($avgDailyEggs, 2),
            'hen_day_production' => round($henDayProduction, 2),
            'survival_rate' => $this->survival_rate,
            'mortality_rate' => $this->mortality_rate,
            'current_age_weeks' => $this->age_weeks,
            'production_week' => $this->current_production_week,
        ];
    }

    public static function generateFlockNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "FL{$year}{$month}";
        
        $lastFlock = static::where('flock_number', 'like', $prefix . '%')
            ->orderBy('flock_number', 'desc')
            ->first();
        
        if ($lastFlock) {
            $lastNumber = (int) substr($lastFlock->flock_number, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.5 Breed Model**

```php
<?php
// Modules/PoultryManagement/Entities/Breed.php

namespace Modules\PoultryManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Breed extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'breed_code',
        'breed_name',
        'breed_type',
        'origin_country',
        'description',
        'characteristics',
        'performance_standards',
        'mature_weight_female',
        'mature_weight_male',
        'egg_production_peak',
        'egg_weight_average',
        'production_period_weeks',
        'feed_conversion_ratio',
        'mortality_rate_standard',
        'housing_density_recommended',
        'temperature_range_optimal',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'characteristics' => 'array',
        'performance_standards' => 'array',
        'mature_weight_female' => 'decimal:2',
        'mature_weight_male' => 'decimal:2',
        'egg_production_peak' => 'decimal:2',
        'egg_weight_average' => 'decimal:2',
        'production_period_weeks' => 'integer',
        'feed_conversion_ratio' => 'decimal:2',
        'mortality_rate_standard' => 'decimal:2',
        'housing_density_recommended' => 'decimal:2',
        'temperature_range_optimal' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function flocks()
    {
        return $this->hasMany(Flock::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('breed_type', $type);
    }

    // Accessors
    public function getActiveFlockCountAttribute(): int
    {
        return $this->flocks()->where('status', 'active')->count();
    }

    public function getTotalPopulationAttribute(): int
    {
        return $this->flocks()->where('status', 'active')->sum('current_count');
    }

    // Methods
    public function getPerformanceComparison(Flock $flock): array
    {
        $metrics = $flock->calculatePerformanceMetrics();
        
        return [
            'hen_day_production' => [
                'actual' => $metrics['hen_day_production'],
                'standard' => $this->egg_production_peak,
                'variance' => $metrics['hen_day_production'] - $this->egg_production_peak,
            ],
            'mortality_rate' => [
                'actual' => $metrics['mortality_rate'],
                'standard' => $this->mortality_rate_standard,
                'variance' => $metrics['mortality_rate'] - $this->mortality_rate_standard,
            ],
            'survival_rate' => [
                'actual' => $metrics['survival_rate'],
                'standard' => 100 - $this->mortality_rate_standard,
                'variance' => $metrics['survival_rate'] - (100 - $this->mortality_rate_standard),
            ],
        ];
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Farm Management Component**

```typescript
// frontend/src/modules/poultry/components/FarmManagement.tsx
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  message,
} from 'antd';
import { PlusOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { poultryService } from '../services/poultryService';

const FarmManagement: React.FC = () => {
  const [farms, setFarms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingFarm, setEditingFarm] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadFarms();
  }, []);

  const loadFarms = async () => {
    setLoading(true);
    try {
      const response = await poultryService.getFarms();
      setFarms(response.data);
    } catch (error) {
      message.error('Failed to load farms');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingFarm) {
        await poultryService.updateFarm(editingFarm.id, values);
        message.success('Farm updated successfully');
      } else {
        await poultryService.createFarm(values);
        message.success('Farm created successfully');
      }
      setModalVisible(false);
      setEditingFarm(null);
      form.resetFields();
      loadFarms();
    } catch (error) {
      message.error('Failed to save farm');
    }
  };

  const columns = [
    {
      title: 'Farm Code',
      dataIndex: 'farm_code',
      key: 'farm_code',
    },
    {
      title: 'Farm Name',
      dataIndex: 'farm_name',
      key: 'farm_name',
    },
    {
      title: 'Type',
      dataIndex: 'farm_type',
      key: 'farm_type',
      render: (type: string) => (
        <Tag color={type === 'commercial' ? 'blue' : 'green'}>
          {type.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Manager',
      dataIndex: ['manager', 'name'],
      key: 'manager',
    },
    {
      title: 'Houses',
      dataIndex: 'total_houses',
      key: 'total_houses',
    },
    {
      title: 'Capacity',
      dataIndex: 'total_capacity',
      key: 'total_capacity',
      render: (capacity: number) => capacity?.toLocaleString(),
    },
    {
      title: 'Population',
      dataIndex: 'current_population',
      key: 'current_population',
      render: (population: number) => population?.toLocaleString(),
    },
    {
      title: 'Occupancy',
      dataIndex: 'occupancy_rate',
      key: 'occupancy_rate',
      render: (rate: number) => `${rate?.toFixed(1)}%`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {/* View details */}}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingFarm(record);
              form.setFieldsValue(record);
              setModalVisible(true);
            }}
          />
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Farm Management"
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setModalVisible(true)}
        >
          Add Farm
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={farms}
        loading={loading}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editingFarm ? 'Edit Farm' : 'Add Farm'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingFarm(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <Form.Item
              name="farm_name"
              label="Farm Name"
              rules={[{ required: true, message: 'Please enter farm name' }]}
            >
              <Input placeholder="Enter farm name" />
            </Form.Item>

            <Form.Item
              name="farm_type"
              label="Farm Type"
              rules={[{ required: true, message: 'Please select farm type' }]}
            >
              <Select placeholder="Select farm type">
                <Select.Option value="commercial">Commercial</Select.Option>
                <Select.Option value="smallholder">Smallholder</Select.Option>
                <Select.Option value="breeding">Breeding</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="owner_name"
              label="Owner Name"
              rules={[{ required: true, message: 'Please enter owner name' }]}
            >
              <Input placeholder="Enter owner name" />
            </Form.Item>

            <Form.Item
              name="manager_id"
              label="Farm Manager"
            >
              <Select placeholder="Select farm manager">
                {/* Load managers from API */}
              </Select>
            </Form.Item>

            <Form.Item
              name="total_area_hectares"
              label="Total Area (Hectares)"
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="Enter total area"
                min={0}
                precision={2}
              />
            </Form.Item>

            <Form.Item
              name="established_date"
              label="Established Date"
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <Form.Item
            name="address"
            label="Address"
            rules={[{ required: true, message: 'Please enter address' }]}
          >
            <Input.TextArea rows={3} placeholder="Enter full address" />
          </Form.Item>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
            <Form.Item
              name="city"
              label="City"
              rules={[{ required: true, message: 'Please enter city' }]}
            >
              <Input placeholder="Enter city" />
            </Form.Item>

            <Form.Item
              name="province"
              label="Province"
              rules={[{ required: true, message: 'Please enter province' }]}
            >
              <Input placeholder="Enter province" />
            </Form.Item>

            <Form.Item
              name="postal_code"
              label="Postal Code"
            >
              <Input placeholder="Enter postal code" />
            </Form.Item>
          </div>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea rows={3} placeholder="Additional notes..." />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default FarmManagement;
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Farm CRUD operations working
- [ ] House management functional
- [ ] Flock lifecycle tracking
- [ ] Breed management system
- [ ] Environmental monitoring
- [ ] Population calculations accurate

### **Frontend**
- [ ] Farm management interface
- [ ] House assignment working
- [ ] Flock tracking dashboard
- [ ] Performance metrics display
- [ ] Responsive design
- [ ] Data validation working

### **Integration**
- [ ] User access control working
- [ ] Multi-farm support
- [ ] Capacity calculations accurate
- [ ] Status tracking functional
- [ ] Activity logging working

## 📞 NEXT STEPS

Setelah Poultry Management module selesai:

1. **Test farm operations** end-to-end
2. **Verify capacity calculations**
3. **Test flock lifecycle** tracking
4. **Validate performance metrics**
5. **Commit module** ke repository
6. **Lanjut ke** `04_INVENTORY_MANAGEMENT.md`

---

**IMPORTANT**: Poultry Management adalah core foundation. Pastikan semua calculations accurate, lifecycle tracking complete, dan integration dengan user management working properly untuk operational excellence.
