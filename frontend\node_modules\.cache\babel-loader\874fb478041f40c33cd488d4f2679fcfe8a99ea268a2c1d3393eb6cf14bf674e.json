{"ast": null, "code": "// This icon file is generated automatically.\nvar FieldNumberOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"style\",\n        \"attrs\": {}\n      }]\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M508 280h-63.3c-3.3 0-6 2.7-6 6v340.2H433L197.4 282.6c-1.1-1.6-3-2.6-4.9-2.6H126c-3.3 0-6 2.7-6 6v464c0 3.3 2.7 6 6 6h62.7c3.3 0 6-2.7 6-6V405.1h5.7l238.2 348.3c1.1 1.6 3 2.6 5 2.6H508c3.3 0 6-2.7 6-6V286c0-3.3-2.7-6-6-6zm378 413H582c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-152.2-63c52.9 0 95.2-17.2 126.2-51.7 29.4-32.9 44-75.8 44-128.8 0-53.1-14.6-96.5-44-129.3-30.9-34.8-73.2-52.2-126.2-52.2-53.7 0-95.9 17.5-126.3 52.8-29.2 33.1-43.4 75.9-43.4 128.7 0 52.4 14.3 95.2 43.5 128.3 30.6 34.7 73 52.2 126.2 52.2zm-71.5-263.7c16.9-20.6 40.3-30.9 71.4-30.9 31.5 0 54.8 9.6 71 29.1 16.4 20.3 24.9 48.6 24.9 84.9 0 36.3-8.4 64.1-24.8 83.9-16.5 19.4-40 29.2-71.1 29.2-31.2 0-55-10.3-71.4-30.4-16.3-20.1-24.5-47.3-24.5-82.6.1-35.8 8.2-63 24.5-83.2z\"\n      }\n    }]\n  },\n  \"name\": \"field-number\",\n  \"theme\": \"outlined\"\n};\nexport default FieldNumberOutlined;", "map": {"version": 3, "names": ["FieldNumberOutlined"], "sources": ["C:/laragon/www/neomuria2025_augment/frontend/node_modules/@ant-design/icons-svg/es/asn/FieldNumberOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar FieldNumberOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M508 280h-63.3c-3.3 0-6 2.7-6 6v340.2H433L197.4 282.6c-1.1-1.6-3-2.6-4.9-2.6H126c-3.3 0-6 2.7-6 6v464c0 3.3 2.7 6 6 6h62.7c3.3 0 6-2.7 6-6V405.1h5.7l238.2 348.3c1.1 1.6 3 2.6 5 2.6H508c3.3 0 6-2.7 6-6V286c0-3.3-2.7-6-6-6zm378 413H582c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-152.2-63c52.9 0 95.2-17.2 126.2-51.7 29.4-32.9 44-75.8 44-128.8 0-53.1-14.6-96.5-44-129.3-30.9-34.8-73.2-52.2-126.2-52.2-53.7 0-95.9 17.5-126.3 52.8-29.2 33.1-43.4 75.9-43.4 128.7 0 52.4 14.3 95.2 43.5 128.3 30.6 34.7 73 52.2 126.2 52.2zm-71.5-263.7c16.9-20.6 40.3-30.9 71.4-30.9 31.5 0 54.8 9.6 71 29.1 16.4 20.3 24.9 48.6 24.9 84.9 0 36.3-8.4 64.1-24.8 83.9-16.5 19.4-40 29.2-71.1 29.2-31.2 0-55-10.3-71.4-30.4-16.3-20.1-24.5-47.3-24.5-82.6.1-35.8 8.2-63 24.5-83.2z\" } }] }, \"name\": \"field-number\", \"theme\": \"outlined\" };\nexport default FieldNumberOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,mBAAmB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,OAAO;QAAE,OAAO,EAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA8wB;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,cAAc;EAAE,OAAO,EAAE;AAAW,CAAC;AACnjC,eAAeA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}