# 01 - SETUP ENVIRONMENT

## 📋 OVERVIEW

File ini berisi panduan lengkap untuk setup development environment ERP Peternakan Ayam dengan Docker, <PERSON><PERSON> 10, dan <PERSON><PERSON> 18. Environment ini akan digunakan oleh seluruh tim developer.

## 🎯 TUJUAN

- Setup development environment yang konsisten untuk semua developer
- Konfigurasi Docker untuk backend dan frontend
- Setup database dengan schema modular
- Konfigurasi tools development (IDE, debugging, testing)

## ⏱️ ESTIMASI WAKTU

**Total**: 4-6 jam
- Docker setup: 1-2 jam
- Laravel setup: 1-2 jam  
- React setup: 1-2 jam
- Database setup: 1 jam

## 👥 TIM YANG TERLIBAT

- **DevOps Engineer** (Lead)
- **Backend Lead**
- **Frontend Lead**
- **All Developers** (untuk setup local)

## 🛠️ PREREQUISITES

### **System Requirements**
- **OS**: Windows 10/11, macOS, atau Linux
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: Minimum 20GB free space
- **Docker**: Docker Desktop 4.0+
- **Git**: Git 2.30+

### **Development Tools**
- **IDE**: VS Code dengan extensions
- **API Testing**: Postman atau Insomnia
- **Database Client**: TablePlus, DBeaver, atau phpMyAdmin

## 📁 PROJECT STRUCTURE

```
erp-poultry/
├── docker/                     # Docker configuration
│   ├── nginx/
│   │   ├── nginx.conf
│   │   └── default.conf
│   ├── php/
│   │   ├── Dockerfile
│   │   ├── php.ini
│   │   └── opcache.ini
│   ├── mysql/
│   │   └── init.sql
│   └── supervisor/
│       └── supervisord.conf
├── backend/                    # Laravel application
│   ├── app/
│   ├── config/
│   ├── database/
│   ├── routes/
│   └── ...
├── frontend/                   # React application
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── ...
├── docker-compose.yml          # Docker services
├── docker-compose.override.yml # Development overrides
├── .env.example               # Environment template
└── README.md                  # Project documentation
```

## 🐳 STEP 1: DOCKER SETUP

### **1.1 Create Docker Configuration**

```bash
# Create project directory
mkdir erp-poultry
cd erp-poultry

# Create docker directory structure
mkdir -p docker/{nginx,php,mysql,supervisor}
```

### **1.2 Docker Compose Configuration**

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    container_name: erp-poultry-app
    restart: unless-stopped
    working_dir: /var/www/backend
    volumes:
      - ./backend:/var/www/backend
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
    networks:
      - erp-network
    depends_on:
      - database
      - redis

  webserver:
    image: nginx:alpine
    container_name: erp-poultry-nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./backend:/var/www/backend
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - erp-network
    depends_on:
      - app

  database:
    image: mysql:8.0
    container_name: erp-poultry-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: erp_poultry
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_PASSWORD: secret
      MYSQL_USER: erp_user
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - erp-network

  redis:
    image: redis:7-alpine
    container_name: erp-poultry-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redisdata:/data
    ports:
      - "6379:6379"
    networks:
      - erp-network

  frontend:
    image: node:18-alpine
    container_name: erp-poultry-frontend
    working_dir: /app
    volumes:
      - ./frontend:/app
    ports:
      - "3000:3000"
    command: sh -c "npm install && npm run dev"
    networks:
      - erp-network

  mailhog:
    image: mailhog/mailhog
    container_name: erp-poultry-mail
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - erp-network

networks:
  erp-network:
    driver: bridge

volumes:
  dbdata:
  redisdata:
```

### **1.3 PHP Dockerfile**

```dockerfile
# docker/php/Dockerfile
FROM php:8.2-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    git curl libpng-dev libxml2-dev zip unzip \
    freetype-dev libjpeg-turbo-dev \
    mysql-client \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd

# Install PHP extensions
RUN docker-php-ext-install \
    pdo pdo_mysql mbstring exif pcntl bcmath \
    opcache intl

# Install Redis extension
RUN apk add --no-cache pcre-dev $PHPIZE_DEPS \
    && pecl install redis \
    && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/backend

# Copy application files
COPY backend/ .

# Install dependencies
RUN composer install --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www/backend \
    && chmod -R 755 /var/www/backend/storage \
    && chmod -R 755 /var/www/backend/bootstrap/cache

EXPOSE 9000
CMD ["php-fpm"]
```

### **1.4 Nginx Configuration**

```nginx
# docker/nginx/default.conf
server {
    listen 80;
    server_name localhost;
    root /var/www/backend/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    # Handle CORS for API
    location /api {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, Accept' always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

## 🔧 STEP 2: LARAVEL BACKEND SETUP

### **2.1 Create Laravel Project**

```bash
# Create Laravel project
composer create-project laravel/laravel backend
cd backend

# Install required packages
composer require nwidart/laravel-modules
composer require laravel/sanctum
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog
composer require laravel/socialite
composer require fruitcake/laravel-cors
composer require darkaonline/l5-swagger
```

### **2.2 Environment Configuration**

```bash
# Copy environment file
cp .env.example .env
```

```env
# .env
APP_NAME="ERP Poultry Management"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=database
DB_PORT=3306
DB_DATABASE=erp_poultry
DB_USERNAME=erp_user
DB_PASSWORD=secret

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Social Login
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI="${APP_URL}/auth/google/callback"

FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
FACEBOOK_REDIRECT_URI="${APP_URL}/auth/facebook/callback"

# API Documentation
L5_SWAGGER_GENERATE_ALWAYS=true
L5_SWAGGER_CONST_HOST="${APP_URL}/api"
```

### **2.3 Laravel Configuration**

```bash
# Generate application key
php artisan key:generate

# Publish Sanctum configuration
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Publish Spatie Permission configuration
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"

# Publish Laravel Modules configuration
php artisan vendor:publish --provider="Nwidart\Modules\LaravelModulesServiceProvider"

# Publish CORS configuration
php artisan vendor:publish --tag="cors"

# Publish Swagger configuration
php artisan vendor:publish --provider="L5Swagger\L5SwaggerServiceProvider"
```

## ⚛️ STEP 3: REACT FRONTEND SETUP

### **3.1 Create React Project**

```bash
# Create React project with TypeScript
npx create-react-app frontend --template typescript
cd frontend

# Install required packages
npm install @tanstack/react-query zustand
npm install @headlessui/react lucide-react
npm install tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install recharts axios react-router-dom
npm install @hookform/resolvers react-hook-form zod
npm install react-hot-toast
npm install @types/node

# Install development dependencies
npm install -D @types/react @types/react-dom
npm install -D autoprefixer postcss
```

### **3.2 Tailwind CSS Setup**

```bash
# Initialize Tailwind CSS
npx tailwindcss init -p
```

```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

### **3.3 Project Structure Setup**

```bash
# Create directory structure
mkdir -p src/{components,pages,hooks,services,stores,types,utils}
mkdir -p src/components/{ui,forms,charts,layout}
mkdir -p src/modules/{user-management,inventory,accounting}
```

## 🗄️ STEP 4: DATABASE SETUP

### **4.1 Copy Database Schema**

```bash
# Copy the modular database schema
cp erp_poultry_modular_database.sql docker/mysql/init.sql
```

### **4.2 Run Migrations**

```bash
# Start Docker services
docker-compose up -d

# Wait for services to start (30 seconds)
sleep 30

# Run Laravel migrations
docker-compose exec app php artisan migrate

# Seed initial data
docker-compose exec app php artisan db:seed
```

## 🧪 STEP 5: TESTING SETUP

### **5.1 Backend Testing**

```bash
# Install testing dependencies
docker-compose exec app composer require --dev phpunit/phpunit
docker-compose exec app composer require --dev mockery/mockery

# Create test database
docker-compose exec database mysql -u root -psecret -e "CREATE DATABASE erp_poultry_test;"

# Run tests
docker-compose exec app php artisan test
```

### **5.2 Frontend Testing**

```bash
# Install testing dependencies
cd frontend
npm install -D @testing-library/react @testing-library/jest-dom
npm install -D @testing-library/user-event

# Run tests
npm test
```

## ✅ VERIFICATION CHECKLIST

### **Docker Services**
- [ ] All containers running: `docker-compose ps`
- [ ] Backend accessible: http://localhost:8000
- [ ] Frontend accessible: http://localhost:3000
- [ ] Database accessible: localhost:3306
- [ ] Redis accessible: localhost:6379
- [ ] Mailhog accessible: http://localhost:8025

### **Laravel Backend**
- [ ] Application key generated
- [ ] Database connected
- [ ] Migrations run successfully
- [ ] API routes accessible: http://localhost:8000/api
- [ ] Swagger docs: http://localhost:8000/api/documentation

### **React Frontend**
- [ ] Development server running
- [ ] Tailwind CSS working
- [ ] TypeScript compilation successful
- [ ] API connection working

### **Development Tools**
- [ ] VS Code extensions installed
- [ ] Postman/Insomnia configured
- [ ] Database client connected
- [ ] Git repository initialized

## 🚨 TROUBLESHOOTING

### **Common Issues**

**Docker containers not starting:**
```bash
# Check logs
docker-compose logs app
docker-compose logs database

# Restart services
docker-compose down
docker-compose up -d
```

**Permission issues:**
```bash
# Fix Laravel permissions
docker-compose exec app chown -R www-data:www-data /var/www/backend
docker-compose exec app chmod -R 755 /var/www/backend/storage
```

**Database connection failed:**
```bash
# Check database status
docker-compose exec database mysql -u root -psecret -e "SHOW DATABASES;"

# Reset database
docker-compose down -v
docker-compose up -d
```

## 📞 NEXT STEPS

Setelah environment setup selesai:

1. **Verifikasi semua checklist** di atas
2. **Commit initial setup** ke Git repository
3. **Share environment** dengan tim developer
4. **Lanjut ke** `02_CORE_SYSTEM.md` untuk implementasi core system

---

**IMPORTANT**: Pastikan semua developer menggunakan environment yang sama untuk menghindari "works on my machine" issues. Jika ada masalah setup, hubungi DevOps Engineer atau Backend Lead.
