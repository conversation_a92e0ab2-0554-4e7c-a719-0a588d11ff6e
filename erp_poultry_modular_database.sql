/*
 ERP Poultry Modular Database Schema
 
 Project: Sistem Informasi ERP Peternakan Ayam Petelur Modular
 Version: 2.0.0
 Framework: Laravel 10+ with HMVC Architecture
 Database: MySQL 8.0+
 Character Set: utf8mb4 (Full Unicode Support)
 
 Created: 2025-01-16
 Author: Development Team
 
 Module Structure:
 - Core System (Authentication, RBAC, Events, API)
 - User Management Module
 - Inventory Module  
 - Accounting Module
 - Sales Module
 - Purchasing Module
 - Poultry Management Module
 - Feed Management Module
 - Egg Production Module
 - Medical Module
 - Supply Chain Module
 - Reporting & Analytics Module
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- ============================================================================
-- CORE SYSTEM TABLES
-- ============================================================================

-- System Configuration
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL,
  `value` text,
  `description` varchar(255) DEFAULT NULL,
  `type` enum('string','integer','boolean','json','array') DEFAULT 'string',
  `is_public` boolean DEFAULT false,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `system_config_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Module Management
DROP TABLE IF EXISTS `modules`;
CREATE TABLE `modules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text,
  `version` varchar(20) NOT NULL,
  `author` varchar(100) DEFAULT NULL,
  `license` varchar(50) DEFAULT NULL,
  `type` enum('core','plugin','theme','integration') DEFAULT 'plugin',
  `status` enum('active','inactive','installing','uninstalling') DEFAULT 'inactive',
  `dependencies` json DEFAULT NULL,
  `provides` json DEFAULT NULL,
  `config` json DEFAULT NULL,
  `installed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `modules_name_unique` (`name`),
  KEY `modules_status_index` (`status`),
  KEY `modules_type_index` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Module Permissions
DROP TABLE IF EXISTS `module_permissions`;
CREATE TABLE `module_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `module_id` bigint unsigned NOT NULL,
  `permission` varchar(100) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `module_permissions_module_permission_unique` (`module_id`, `permission`),
  CONSTRAINT `module_permissions_module_id_foreign` FOREIGN KEY (`module_id`) REFERENCES `modules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Event Logs
DROP TABLE IF EXISTS `event_logs`;
CREATE TABLE `event_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) NOT NULL,
  `module_source` varchar(100) DEFAULT NULL,
  `module_target` varchar(100) DEFAULT NULL,
  `payload` json DEFAULT NULL,
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending',
  `error_message` text DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `event_logs_event_name_index` (`event_name`),
  KEY `event_logs_status_index` (`status`),
  KEY `event_logs_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Audit Trails
DROP TABLE IF EXISTS `audit_trails`;
CREATE TABLE `audit_trails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `module_name` varchar(100) NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `record_id` bigint unsigned DEFAULT NULL,
  `action` enum('create','update','delete','view','export') NOT NULL,
  `old_data` json DEFAULT NULL,
  `new_data` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audit_trails_user_id_index` (`user_id`),
  KEY `audit_trails_module_name_index` (`module_name`),
  KEY `audit_trails_table_name_index` (`table_name`),
  KEY `audit_trails_action_index` (`action`),
  KEY `audit_trails_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- USER MANAGEMENT MODULE
-- ============================================================================

-- Users (Enhanced from Laravel default)
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `timezone` varchar(50) DEFAULT 'Asia/Jakarta',
  `language` varchar(10) DEFAULT 'id',
  `status` enum('active','inactive','suspended','pending') DEFAULT 'active',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `password_changed_at` timestamp NULL DEFAULT NULL,
  `two_factor_secret` text DEFAULT NULL,
  `two_factor_recovery_codes` text DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_uuid_unique` (`uuid`),
  UNIQUE KEY `users_username_unique` (`username`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_status_index` (`status`),
  KEY `users_deleted_at_index` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Roles
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `level` int DEFAULT 0,
  `is_system` boolean DEFAULT false,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_unique` (`name`),
  KEY `roles_level_index` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Permissions
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `module_name` varchar(100) NOT NULL,
  `group_name` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_unique` (`name`),
  KEY `permissions_module_name_index` (`module_name`),
  KEY `permissions_group_name_index` (`group_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Roles (Many-to-Many)
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `assigned_by` bigint unsigned DEFAULT NULL,
  `assigned_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_roles_user_role_unique` (`user_id`, `role_id`),
  KEY `user_roles_role_id_foreign` (`role_id`),
  KEY `user_roles_assigned_by_foreign` (`assigned_by`),
  CONSTRAINT `user_roles_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_roles_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Role Permissions (Many-to-Many)
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `role_id` bigint unsigned NOT NULL,
  `permission_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permissions_role_permission_unique` (`role_id`, `permission_id`),
  KEY `role_permissions_permission_id_foreign` (`permission_id`),
  CONSTRAINT `role_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Sessions
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_sessions_user_id_index` (`user_id`),
  KEY `user_sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password Reset Tokens
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(100) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Personal Access Tokens (for API)
DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`, `tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- MASTER DATA TABLES (Shared across modules)
-- ============================================================================

-- Companies/Organizations
DROP TABLE IF EXISTS `companies`;
CREATE TABLE `companies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(20) NOT NULL,
  `name` varchar(150) NOT NULL,
  `legal_name` varchar(200) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `registration_number` varchar(50) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(100) DEFAULT 'Indonesia',
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'IDR',
  `timezone` varchar(50) DEFAULT 'Asia/Jakarta',
  `fiscal_year_start` date DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `companies_uuid_unique` (`uuid`),
  UNIQUE KEY `companies_code_unique` (`code`),
  KEY `companies_status_index` (`status`),
  KEY `companies_deleted_at_index` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Regions/Wilayah
DROP TABLE IF EXISTS `regions`;
CREATE TABLE `regions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` enum('province','city','district','village') NOT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `coordinates` point DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `regions_code_unique` (`code`),
  KEY `regions_parent_id_foreign` (`parent_id`),
  KEY `regions_type_index` (`type`),
  KEY `regions_status_index` (`status`),
  CONSTRAINT `regions_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Units of Measure
DROP TABLE IF EXISTS `units`;
CREATE TABLE `units` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `name` varchar(50) NOT NULL,
  `symbol` varchar(10) DEFAULT NULL,
  `type` enum('weight','volume','length','area','count','time') NOT NULL,
  `base_unit_id` bigint unsigned DEFAULT NULL,
  `conversion_factor` decimal(15,6) DEFAULT 1.000000,
  `is_base` boolean DEFAULT false,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `units_code_unique` (`code`),
  KEY `units_base_unit_id_foreign` (`base_unit_id`),
  KEY `units_type_index` (`type`),
  KEY `units_status_index` (`status`),
  CONSTRAINT `units_base_unit_id_foreign` FOREIGN KEY (`base_unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- INVENTORY MODULE
-- ============================================================================

-- Item Categories
DROP TABLE IF EXISTS `item_categories`;
CREATE TABLE `item_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `level` int DEFAULT 0,
  `path` varchar(500) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `sort_order` int DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_categories_code_unique` (`code`),
  KEY `item_categories_parent_id_foreign` (`parent_id`),
  KEY `item_categories_level_index` (`level`),
  KEY `item_categories_status_index` (`status`),
  CONSTRAINT `item_categories_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `item_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Items/Products
DROP TABLE IF EXISTS `items`;
CREATE TABLE `items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(50) NOT NULL,
  `barcode` varchar(100) DEFAULT NULL,
  `name` varchar(200) NOT NULL,
  `description` text DEFAULT NULL,
  `category_id` bigint unsigned DEFAULT NULL,
  `brand` varchar(100) DEFAULT NULL,
  `model` varchar(100) DEFAULT NULL,
  `type` enum('product','service','raw_material','finished_good','consumable') DEFAULT 'product',
  `unit_id` bigint unsigned NOT NULL,
  `purchase_unit_id` bigint unsigned DEFAULT NULL,
  `sale_unit_id` bigint unsigned DEFAULT NULL,
  `weight` decimal(10,3) DEFAULT NULL,
  `volume` decimal(10,3) DEFAULT NULL,
  `dimensions` json DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `images` json DEFAULT NULL,
  `is_trackable` boolean DEFAULT true,
  `is_serialized` boolean DEFAULT false,
  `is_batch_tracked` boolean DEFAULT false,
  `min_stock` decimal(15,3) DEFAULT 0.000,
  `max_stock` decimal(15,3) DEFAULT NULL,
  `reorder_point` decimal(15,3) DEFAULT NULL,
  `lead_time_days` int DEFAULT 0,
  `shelf_life_days` int DEFAULT NULL,
  `cost_method` enum('fifo','lifo','average','standard') DEFAULT 'fifo',
  `standard_cost` decimal(15,2) DEFAULT 0.00,
  `last_cost` decimal(15,2) DEFAULT 0.00,
  `average_cost` decimal(15,2) DEFAULT 0.00,
  `list_price` decimal(15,2) DEFAULT 0.00,
  `sale_price` decimal(15,2) DEFAULT 0.00,
  `tax_category` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('active','inactive','discontinued') DEFAULT 'active',
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `items_uuid_unique` (`uuid`),
  UNIQUE KEY `items_code_unique` (`code`),
  UNIQUE KEY `items_barcode_unique` (`barcode`),
  KEY `items_category_id_foreign` (`category_id`),
  KEY `items_unit_id_foreign` (`unit_id`),
  KEY `items_purchase_unit_id_foreign` (`purchase_unit_id`),
  KEY `items_sale_unit_id_foreign` (`sale_unit_id`),
  KEY `items_type_index` (`type`),
  KEY `items_status_index` (`status`),
  KEY `items_created_by_foreign` (`created_by`),
  KEY `items_updated_by_foreign` (`updated_by`),
  KEY `items_deleted_at_index` (`deleted_at`),
  CONSTRAINT `items_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `item_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `items_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `items_purchase_unit_id_foreign` FOREIGN KEY (`purchase_unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `items_sale_unit_id_foreign` FOREIGN KEY (`sale_unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `items_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `items_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Warehouses
DROP TABLE IF EXISTS `warehouses`;
CREATE TABLE `warehouses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `type` enum('main','branch','virtual','consignment','transit') DEFAULT 'main',
  `company_id` bigint unsigned DEFAULT NULL,
  `region_id` bigint unsigned DEFAULT NULL,
  `address` text DEFAULT NULL,
  `coordinates` point DEFAULT NULL,
  `manager_id` bigint unsigned DEFAULT NULL,
  `capacity` decimal(15,3) DEFAULT NULL,
  `capacity_unit_id` bigint unsigned DEFAULT NULL,
  `is_default` boolean DEFAULT false,
  `is_active` boolean DEFAULT true,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `warehouses_uuid_unique` (`uuid`),
  UNIQUE KEY `warehouses_code_unique` (`code`),
  KEY `warehouses_company_id_foreign` (`company_id`),
  KEY `warehouses_region_id_foreign` (`region_id`),
  KEY `warehouses_manager_id_foreign` (`manager_id`),
  KEY `warehouses_capacity_unit_id_foreign` (`capacity_unit_id`),
  KEY `warehouses_type_index` (`type`),
  KEY `warehouses_is_active_index` (`is_active`),
  KEY `warehouses_deleted_at_index` (`deleted_at`),
  CONSTRAINT `warehouses_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL,
  CONSTRAINT `warehouses_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `warehouses_manager_id_foreign` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `warehouses_capacity_unit_id_foreign` FOREIGN KEY (`capacity_unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Stock Levels (Current Stock per Item per Warehouse)
DROP TABLE IF EXISTS `stock_levels`;
CREATE TABLE `stock_levels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `item_id` bigint unsigned NOT NULL,
  `warehouse_id` bigint unsigned NOT NULL,
  `quantity_on_hand` decimal(15,3) DEFAULT 0.000,
  `quantity_reserved` decimal(15,3) DEFAULT 0.000,
  `quantity_available` decimal(15,3) GENERATED ALWAYS AS ((`quantity_on_hand` - `quantity_reserved`)) STORED,
  `quantity_on_order` decimal(15,3) DEFAULT 0.000,
  `last_movement_at` timestamp NULL DEFAULT NULL,
  `last_count_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stock_levels_item_warehouse_unique` (`item_id`, `warehouse_id`),
  KEY `stock_levels_warehouse_id_foreign` (`warehouse_id`),
  KEY `stock_levels_quantity_on_hand_index` (`quantity_on_hand`),
  KEY `stock_levels_quantity_available_index` (`quantity_available`),
  CONSTRAINT `stock_levels_item_id_foreign` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_levels_warehouse_id_foreign` FOREIGN KEY (`warehouse_id`) REFERENCES `warehouses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Stock Movements (All inventory transactions)
DROP TABLE IF EXISTS `stock_movements`;
CREATE TABLE `stock_movements` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `item_id` bigint unsigned NOT NULL,
  `warehouse_id` bigint unsigned NOT NULL,
  `movement_type` enum('in','out','transfer','adjustment','production','consumption') NOT NULL,
  `transaction_type` varchar(50) NOT NULL,
  `reference_type` varchar(100) DEFAULT NULL,
  `reference_id` bigint unsigned DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `batch_number` varchar(100) DEFAULT NULL,
  `serial_number` varchar(100) DEFAULT NULL,
  `quantity` decimal(15,3) NOT NULL,
  `unit_id` bigint unsigned NOT NULL,
  `unit_cost` decimal(15,2) DEFAULT 0.00,
  `total_cost` decimal(15,2) GENERATED ALWAYS AS ((`quantity` * `unit_cost`)) STORED,
  `reason` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `movement_date` date NOT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stock_movements_uuid_unique` (`uuid`),
  KEY `stock_movements_item_id_foreign` (`item_id`),
  KEY `stock_movements_warehouse_id_foreign` (`warehouse_id`),
  KEY `stock_movements_unit_id_foreign` (`unit_id`),
  KEY `stock_movements_created_by_foreign` (`created_by`),
  KEY `stock_movements_movement_type_index` (`movement_type`),
  KEY `stock_movements_transaction_type_index` (`transaction_type`),
  KEY `stock_movements_reference_index` (`reference_type`, `reference_id`),
  KEY `stock_movements_movement_date_index` (`movement_date`),
  KEY `stock_movements_batch_number_index` (`batch_number`),
  KEY `stock_movements_serial_number_index` (`serial_number`),
  CONSTRAINT `stock_movements_item_id_foreign` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_movements_warehouse_id_foreign` FOREIGN KEY (`warehouse_id`) REFERENCES `warehouses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_movements_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `stock_movements_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- ACCOUNTING MODULE
-- ============================================================================

-- Chart of Accounts
DROP TABLE IF EXISTS `accounts`;
CREATE TABLE `accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(20) NOT NULL,
  `name` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `account_type` enum('asset','liability','equity','revenue','expense') NOT NULL,
  `account_subtype` varchar(50) DEFAULT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `level` int DEFAULT 0,
  `path` varchar(500) DEFAULT NULL,
  `is_header` boolean DEFAULT false,
  `is_active` boolean DEFAULT true,
  `is_system` boolean DEFAULT false,
  `normal_balance` enum('debit','credit') NOT NULL,
  `opening_balance` decimal(15,2) DEFAULT 0.00,
  `current_balance` decimal(15,2) DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'IDR',
  `tax_code` varchar(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounts_uuid_unique` (`uuid`),
  UNIQUE KEY `accounts_code_unique` (`code`),
  KEY `accounts_parent_id_foreign` (`parent_id`),
  KEY `accounts_account_type_index` (`account_type`),
  KEY `accounts_level_index` (`level`),
  KEY `accounts_is_active_index` (`is_active`),
  KEY `accounts_deleted_at_index` (`deleted_at`),
  CONSTRAINT `accounts_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Journal Entries
DROP TABLE IF EXISTS `journal_entries`;
CREATE TABLE `journal_entries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `journal_number` varchar(50) NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `reference_type` varchar(100) DEFAULT NULL,
  `reference_id` bigint unsigned DEFAULT NULL,
  `transaction_date` date NOT NULL,
  `posting_date` date DEFAULT NULL,
  `description` text NOT NULL,
  `total_debit` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total_credit` decimal(15,2) NOT NULL DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'IDR',
  `exchange_rate` decimal(10,4) DEFAULT 1.0000,
  `status` enum('draft','posted','reversed') DEFAULT 'draft',
  `posted_by` bigint unsigned DEFAULT NULL,
  `posted_at` timestamp NULL DEFAULT NULL,
  `reversed_by` bigint unsigned DEFAULT NULL,
  `reversed_at` timestamp NULL DEFAULT NULL,
  `reversal_reason` text DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `journal_entries_uuid_unique` (`uuid`),
  UNIQUE KEY `journal_entries_journal_number_unique` (`journal_number`),
  KEY `journal_entries_reference_index` (`reference_type`, `reference_id`),
  KEY `journal_entries_transaction_date_index` (`transaction_date`),
  KEY `journal_entries_status_index` (`status`),
  KEY `journal_entries_posted_by_foreign` (`posted_by`),
  KEY `journal_entries_reversed_by_foreign` (`reversed_by`),
  KEY `journal_entries_created_by_foreign` (`created_by`),
  KEY `journal_entries_updated_by_foreign` (`updated_by`),
  CONSTRAINT `journal_entries_posted_by_foreign` FOREIGN KEY (`posted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `journal_entries_reversed_by_foreign` FOREIGN KEY (`reversed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `journal_entries_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `journal_entries_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Journal Entry Lines
DROP TABLE IF EXISTS `journal_entry_lines`;
CREATE TABLE `journal_entry_lines` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `journal_entry_id` bigint unsigned NOT NULL,
  `account_id` bigint unsigned NOT NULL,
  `line_number` int NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `debit_amount` decimal(15,2) DEFAULT 0.00,
  `credit_amount` decimal(15,2) DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'IDR',
  `exchange_rate` decimal(10,4) DEFAULT 1.0000,
  `debit_amount_base` decimal(15,2) GENERATED ALWAYS AS ((`debit_amount` * `exchange_rate`)) STORED,
  `credit_amount_base` decimal(15,2) GENERATED ALWAYS AS ((`credit_amount` * `exchange_rate`)) STORED,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `journal_entry_lines_journal_entry_id_foreign` (`journal_entry_id`),
  KEY `journal_entry_lines_account_id_foreign` (`account_id`),
  KEY `journal_entry_lines_line_number_index` (`line_number`),
  CONSTRAINT `journal_entry_lines_journal_entry_id_foreign` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `journal_entry_lines_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- CUSTOMER & SUPPLIER MODULE (Shared by Sales & Purchasing)
-- ============================================================================

-- Business Partners (Customers & Suppliers)
DROP TABLE IF EXISTS `business_partners`;
CREATE TABLE `business_partners` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(50) NOT NULL,
  `name` varchar(150) NOT NULL,
  `legal_name` varchar(200) DEFAULT NULL,
  `type` enum('customer','supplier','both','employee','other') NOT NULL,
  `category` varchar(50) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `registration_number` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `billing_address` json DEFAULT NULL,
  `shipping_address` json DEFAULT NULL,
  `contact_person` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `contact_email` varchar(100) DEFAULT NULL,
  `payment_terms` varchar(50) DEFAULT NULL,
  `credit_limit` decimal(15,2) DEFAULT 0.00,
  `credit_days` int DEFAULT 0,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `price_level` varchar(20) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'IDR',
  `language` varchar(10) DEFAULT 'id',
  `notes` text DEFAULT NULL,
  `is_active` boolean DEFAULT true,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `business_partners_uuid_unique` (`uuid`),
  UNIQUE KEY `business_partners_code_unique` (`code`),
  KEY `business_partners_type_index` (`type`),
  KEY `business_partners_category_index` (`category`),
  KEY `business_partners_is_active_index` (`is_active`),
  KEY `business_partners_created_by_foreign` (`created_by`),
  KEY `business_partners_updated_by_foreign` (`updated_by`),
  KEY `business_partners_deleted_at_index` (`deleted_at`),
  CONSTRAINT `business_partners_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `business_partners_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- SALES MODULE
-- ============================================================================

-- Sales Orders
DROP TABLE IF EXISTS `sales_orders`;
CREATE TABLE `sales_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `order_number` varchar(50) NOT NULL,
  `customer_id` bigint unsigned NOT NULL,
  `customer_reference` varchar(100) DEFAULT NULL,
  `order_date` date NOT NULL,
  `required_date` date DEFAULT NULL,
  `promised_date` date DEFAULT NULL,
  `warehouse_id` bigint unsigned DEFAULT NULL,
  `salesperson_id` bigint unsigned DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'IDR',
  `exchange_rate` decimal(10,4) DEFAULT 1.0000,
  `subtotal` decimal(15,2) DEFAULT 0.00,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_amount` decimal(15,2) DEFAULT 0.00,
  `tax_amount` decimal(15,2) DEFAULT 0.00,
  `shipping_amount` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `payment_terms` varchar(50) DEFAULT NULL,
  `shipping_method` varchar(50) DEFAULT NULL,
  `shipping_address` json DEFAULT NULL,
  `billing_address` json DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `internal_notes` text DEFAULT NULL,
  `status` enum('draft','confirmed','processing','shipped','delivered','cancelled','closed') DEFAULT 'draft',
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sales_orders_uuid_unique` (`uuid`),
  UNIQUE KEY `sales_orders_order_number_unique` (`order_number`),
  KEY `sales_orders_customer_id_foreign` (`customer_id`),
  KEY `sales_orders_warehouse_id_foreign` (`warehouse_id`),
  KEY `sales_orders_salesperson_id_foreign` (`salesperson_id`),
  KEY `sales_orders_order_date_index` (`order_date`),
  KEY `sales_orders_status_index` (`status`),
  KEY `sales_orders_created_by_foreign` (`created_by`),
  KEY `sales_orders_updated_by_foreign` (`updated_by`),
  KEY `sales_orders_deleted_at_index` (`deleted_at`),
  CONSTRAINT `sales_orders_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `business_partners` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `sales_orders_warehouse_id_foreign` FOREIGN KEY (`warehouse_id`) REFERENCES `warehouses` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_orders_salesperson_id_foreign` FOREIGN KEY (`salesperson_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_orders_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_orders_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sales Order Lines
DROP TABLE IF EXISTS `sales_order_lines`;
CREATE TABLE `sales_order_lines` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sales_order_id` bigint unsigned NOT NULL,
  `line_number` int NOT NULL,
  `item_id` bigint unsigned NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `quantity_ordered` decimal(15,3) NOT NULL,
  `quantity_shipped` decimal(15,3) DEFAULT 0.000,
  `quantity_remaining` decimal(15,3) GENERATED ALWAYS AS ((`quantity_ordered` - `quantity_shipped`)) STORED,
  `unit_id` bigint unsigned NOT NULL,
  `unit_price` decimal(15,2) NOT NULL,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_amount` decimal(15,2) DEFAULT 0.00,
  `line_total` decimal(15,2) GENERATED ALWAYS AS (((`quantity_ordered` * `unit_price`) - `discount_amount`)) STORED,
  `tax_code` varchar(20) DEFAULT NULL,
  `tax_amount` decimal(15,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_order_lines_sales_order_id_foreign` (`sales_order_id`),
  KEY `sales_order_lines_item_id_foreign` (`item_id`),
  KEY `sales_order_lines_unit_id_foreign` (`unit_id`),
  KEY `sales_order_lines_line_number_index` (`line_number`),
  CONSTRAINT `sales_order_lines_sales_order_id_foreign` FOREIGN KEY (`sales_order_id`) REFERENCES `sales_orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sales_order_lines_item_id_foreign` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `sales_order_lines_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sales Invoices
DROP TABLE IF EXISTS `sales_invoices`;
CREATE TABLE `sales_invoices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `customer_id` bigint unsigned NOT NULL,
  `sales_order_id` bigint unsigned DEFAULT NULL,
  `invoice_date` date NOT NULL,
  `due_date` date NOT NULL,
  `currency` varchar(10) DEFAULT 'IDR',
  `exchange_rate` decimal(10,4) DEFAULT 1.0000,
  `subtotal` decimal(15,2) DEFAULT 0.00,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_amount` decimal(15,2) DEFAULT 0.00,
  `tax_amount` decimal(15,2) DEFAULT 0.00,
  `shipping_amount` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `paid_amount` decimal(15,2) DEFAULT 0.00,
  `balance_amount` decimal(15,2) GENERATED ALWAYS AS ((`total_amount` - `paid_amount`)) STORED,
  `payment_status` enum('unpaid','partial','paid','overpaid') DEFAULT 'unpaid',
  `billing_address` json DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('draft','sent','paid','cancelled','overdue') DEFAULT 'draft',
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sales_invoices_uuid_unique` (`uuid`),
  UNIQUE KEY `sales_invoices_invoice_number_unique` (`invoice_number`),
  KEY `sales_invoices_customer_id_foreign` (`customer_id`),
  KEY `sales_invoices_sales_order_id_foreign` (`sales_order_id`),
  KEY `sales_invoices_invoice_date_index` (`invoice_date`),
  KEY `sales_invoices_due_date_index` (`due_date`),
  KEY `sales_invoices_payment_status_index` (`payment_status`),
  KEY `sales_invoices_status_index` (`status`),
  KEY `sales_invoices_created_by_foreign` (`created_by`),
  KEY `sales_invoices_updated_by_foreign` (`updated_by`),
  KEY `sales_invoices_deleted_at_index` (`deleted_at`),
  CONSTRAINT `sales_invoices_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `business_partners` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `sales_invoices_sales_order_id_foreign` FOREIGN KEY (`sales_order_id`) REFERENCES `sales_orders` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_invoices_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_invoices_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sales Invoice Lines
DROP TABLE IF EXISTS `sales_invoice_lines`;
CREATE TABLE `sales_invoice_lines` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sales_invoice_id` bigint unsigned NOT NULL,
  `sales_order_line_id` bigint unsigned DEFAULT NULL,
  `line_number` int NOT NULL,
  `item_id` bigint unsigned NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `quantity` decimal(15,3) NOT NULL,
  `unit_id` bigint unsigned NOT NULL,
  `unit_price` decimal(15,2) NOT NULL,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_amount` decimal(15,2) DEFAULT 0.00,
  `line_total` decimal(15,2) GENERATED ALWAYS AS (((`quantity` * `unit_price`) - `discount_amount`)) STORED,
  `tax_code` varchar(20) DEFAULT NULL,
  `tax_amount` decimal(15,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_invoice_lines_sales_invoice_id_foreign` (`sales_invoice_id`),
  KEY `sales_invoice_lines_sales_order_line_id_foreign` (`sales_order_line_id`),
  KEY `sales_invoice_lines_item_id_foreign` (`item_id`),
  KEY `sales_invoice_lines_unit_id_foreign` (`unit_id`),
  KEY `sales_invoice_lines_line_number_index` (`line_number`),
  CONSTRAINT `sales_invoice_lines_sales_invoice_id_foreign` FOREIGN KEY (`sales_invoice_id`) REFERENCES `sales_invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sales_invoice_lines_sales_order_line_id_foreign` FOREIGN KEY (`sales_order_line_id`) REFERENCES `sales_order_lines` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_invoice_lines_item_id_foreign` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `sales_invoice_lines_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- POULTRY MANAGEMENT MODULE
-- ============================================================================

-- Poultry Breeds/Strains
DROP TABLE IF EXISTS `poultry_breeds`;
CREATE TABLE `poultry_breeds` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` enum('layer','broiler','breeder','dual_purpose') NOT NULL,
  `origin_country` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `characteristics` json DEFAULT NULL,
  `performance_data` json DEFAULT NULL,
  `is_active` boolean DEFAULT true,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `poultry_breeds_uuid_unique` (`uuid`),
  UNIQUE KEY `poultry_breeds_code_unique` (`code`),
  KEY `poultry_breeds_type_index` (`type`),
  KEY `poultry_breeds_is_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Farms
DROP TABLE IF EXISTS `farms`;
CREATE TABLE `farms` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(20) NOT NULL,
  `name` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `company_id` bigint unsigned DEFAULT NULL,
  `region_id` bigint unsigned DEFAULT NULL,
  `address` text DEFAULT NULL,
  `coordinates` point DEFAULT NULL,
  `total_area` decimal(10,2) DEFAULT NULL,
  `area_unit` varchar(20) DEFAULT 'hectare',
  `manager_id` bigint unsigned DEFAULT NULL,
  `established_date` date DEFAULT NULL,
  `license_number` varchar(100) DEFAULT NULL,
  `license_expiry` date DEFAULT NULL,
  `biosecurity_level` enum('low','medium','high','maximum') DEFAULT 'medium',
  `capacity` int DEFAULT NULL,
  `status` enum('active','inactive','under_construction','closed') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `farms_uuid_unique` (`uuid`),
  UNIQUE KEY `farms_code_unique` (`code`),
  KEY `farms_company_id_foreign` (`company_id`),
  KEY `farms_region_id_foreign` (`region_id`),
  KEY `farms_manager_id_foreign` (`manager_id`),
  KEY `farms_status_index` (`status`),
  KEY `farms_deleted_at_index` (`deleted_at`),
  CONSTRAINT `farms_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL,
  CONSTRAINT `farms_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `farms_manager_id_foreign` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Houses/Kandang
DROP TABLE IF EXISTS `houses`;
CREATE TABLE `houses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `farm_id` bigint unsigned NOT NULL,
  `house_type` enum('open','semi_closed','closed','cage','free_range') DEFAULT 'open',
  `construction_type` enum('concrete','wood','metal','mixed') DEFAULT 'concrete',
  `length` decimal(8,2) DEFAULT NULL,
  `width` decimal(8,2) DEFAULT NULL,
  `height` decimal(8,2) DEFAULT NULL,
  `area` decimal(10,2) GENERATED ALWAYS AS ((`length` * `width`)) STORED,
  `capacity_birds` int NOT NULL,
  `current_population` int DEFAULT 0,
  `available_capacity` int GENERATED ALWAYS AS ((`capacity_birds` - `current_population`)) STORED,
  `ventilation_type` enum('natural','mechanical','hybrid') DEFAULT 'natural',
  `lighting_type` enum('natural','artificial','hybrid') DEFAULT 'natural',
  `feeding_system` enum('manual','automatic','semi_automatic') DEFAULT 'manual',
  `watering_system` enum('manual','automatic','nipple','bell') DEFAULT 'manual',
  `climate_control` boolean DEFAULT false,
  `biosecurity_features` json DEFAULT NULL,
  `equipment_list` json DEFAULT NULL,
  `status` enum('active','inactive','maintenance','renovation') DEFAULT 'active',
  `built_date` date DEFAULT NULL,
  `last_renovation` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `houses_uuid_unique` (`uuid`),
  UNIQUE KEY `houses_code_unique` (`code`),
  KEY `houses_farm_id_foreign` (`farm_id`),
  KEY `houses_house_type_index` (`house_type`),
  KEY `houses_status_index` (`status`),
  KEY `houses_deleted_at_index` (`deleted_at`),
  CONSTRAINT `houses_farm_id_foreign` FOREIGN KEY (`farm_id`) REFERENCES `farms` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Flocks (Groups of birds)
DROP TABLE IF EXISTS `flocks`;
CREATE TABLE `flocks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `flock_number` varchar(50) NOT NULL,
  `house_id` bigint unsigned NOT NULL,
  `breed_id` bigint unsigned NOT NULL,
  `supplier_id` bigint unsigned DEFAULT NULL,
  `placement_date` date NOT NULL,
  `initial_count` int NOT NULL,
  `current_count` int NOT NULL,
  `mortality_count` int DEFAULT 0,
  `culled_count` int DEFAULT 0,
  `sold_count` int DEFAULT 0,
  `age_weeks` decimal(4,1) GENERATED ALWAYS AS ((datediff(curdate(),`placement_date`) / 7)) STORED,
  `production_stage` enum('chick','grower','layer','breeder','spent') DEFAULT 'chick',
  `expected_production_start` date DEFAULT NULL,
  `actual_production_start` date DEFAULT NULL,
  `expected_production_end` date DEFAULT NULL,
  `vaccination_program_id` bigint unsigned DEFAULT NULL,
  `feed_program_id` bigint unsigned DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('active','completed','sold','culled') DEFAULT 'active',
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `flocks_uuid_unique` (`uuid`),
  UNIQUE KEY `flocks_flock_number_unique` (`flock_number`),
  KEY `flocks_house_id_foreign` (`house_id`),
  KEY `flocks_breed_id_foreign` (`breed_id`),
  KEY `flocks_supplier_id_foreign` (`supplier_id`),
  KEY `flocks_placement_date_index` (`placement_date`),
  KEY `flocks_production_stage_index` (`production_stage`),
  KEY `flocks_status_index` (`status`),
  KEY `flocks_created_by_foreign` (`created_by`),
  KEY `flocks_updated_by_foreign` (`updated_by`),
  KEY `flocks_deleted_at_index` (`deleted_at`),
  CONSTRAINT `flocks_house_id_foreign` FOREIGN KEY (`house_id`) REFERENCES `houses` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `flocks_breed_id_foreign` FOREIGN KEY (`breed_id`) REFERENCES `poultry_breeds` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `flocks_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `business_partners` (`id`) ON DELETE SET NULL,
  CONSTRAINT `flocks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `flocks_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- EGG PRODUCTION MODULE
-- ============================================================================

-- Daily Egg Production Records
DROP TABLE IF EXISTS `egg_production_records`;
CREATE TABLE `egg_production_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `flock_id` bigint unsigned NOT NULL,
  `record_date` date NOT NULL,
  `age_weeks` decimal(4,1) NOT NULL,
  `bird_count` int NOT NULL,
  `eggs_collected` int DEFAULT 0,
  `eggs_good` int DEFAULT 0,
  `eggs_cracked` int DEFAULT 0,
  `eggs_dirty` int DEFAULT 0,
  `eggs_small` int DEFAULT 0,
  `eggs_large` int DEFAULT 0,
  `eggs_double_yolk` int DEFAULT 0,
  `eggs_soft_shell` int DEFAULT 0,
  `eggs_broken` int DEFAULT 0,
  `total_weight_kg` decimal(8,3) DEFAULT 0.000,
  `average_weight_g` decimal(6,2) GENERATED ALWAYS AS (case when (`eggs_collected` > 0) then ((`total_weight_kg` * 1000) / `eggs_collected`) else 0 end) STORED,
  `laying_percentage` decimal(5,2) GENERATED ALWAYS AS (case when (`bird_count` > 0) then ((`eggs_collected` / `bird_count`) * 100) else 0 end) STORED,
  `mortality_count` int DEFAULT 0,
  `culled_count` int DEFAULT 0,
  `feed_consumed_kg` decimal(8,2) DEFAULT 0.00,
  `water_consumed_liters` decimal(8,2) DEFAULT 0.00,
  `temperature_min` decimal(4,1) DEFAULT NULL,
  `temperature_max` decimal(4,1) DEFAULT NULL,
  `humidity_percentage` decimal(4,1) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `recorded_by` bigint unsigned DEFAULT NULL,
  `verified_by` bigint unsigned DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `egg_production_records_uuid_unique` (`uuid`),
  UNIQUE KEY `egg_production_records_flock_date_unique` (`flock_id`, `record_date`),
  KEY `egg_production_records_record_date_index` (`record_date`),
  KEY `egg_production_records_recorded_by_foreign` (`recorded_by`),
  KEY `egg_production_records_verified_by_foreign` (`verified_by`),
  CONSTRAINT `egg_production_records_flock_id_foreign` FOREIGN KEY (`flock_id`) REFERENCES `flocks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `egg_production_records_recorded_by_foreign` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `egg_production_records_verified_by_foreign` FOREIGN KEY (`verified_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- FEED MANAGEMENT MODULE
-- ============================================================================

-- Feed Formulations
DROP TABLE IF EXISTS `feed_formulations`;
CREATE TABLE `feed_formulations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `code` varchar(50) NOT NULL,
  `name` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `feed_type` enum('starter','grower','layer','finisher','breeder') NOT NULL,
  `target_species` enum('layer','broiler','breeder','duck','other') DEFAULT 'layer',
  `age_from_weeks` decimal(4,1) DEFAULT NULL,
  `age_to_weeks` decimal(4,1) DEFAULT NULL,
  `protein_percentage` decimal(5,2) DEFAULT NULL,
  `energy_kcal_kg` decimal(8,2) DEFAULT NULL,
  `calcium_percentage` decimal(5,2) DEFAULT NULL,
  `phosphorus_percentage` decimal(5,2) DEFAULT NULL,
  `fiber_percentage` decimal(5,2) DEFAULT NULL,
  `fat_percentage` decimal(5,2) DEFAULT NULL,
  `moisture_percentage` decimal(5,2) DEFAULT NULL,
  `total_percentage` decimal(5,2) DEFAULT 100.00,
  `cost_per_kg` decimal(10,2) DEFAULT 0.00,
  `is_active` boolean DEFAULT true,
  `approved_by` bigint unsigned DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `feed_formulations_uuid_unique` (`uuid`),
  UNIQUE KEY `feed_formulations_code_unique` (`code`),
  KEY `feed_formulations_feed_type_index` (`feed_type`),
  KEY `feed_formulations_target_species_index` (`target_species`),
  KEY `feed_formulations_is_active_index` (`is_active`),
  KEY `feed_formulations_approved_by_foreign` (`approved_by`),
  KEY `feed_formulations_created_by_foreign` (`created_by`),
  KEY `feed_formulations_updated_by_foreign` (`updated_by`),
  KEY `feed_formulations_deleted_at_index` (`deleted_at`),
  CONSTRAINT `feed_formulations_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `feed_formulations_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `feed_formulations_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Feed Formulation Ingredients
DROP TABLE IF EXISTS `feed_formulation_ingredients`;
CREATE TABLE `feed_formulation_ingredients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `formulation_id` bigint unsigned NOT NULL,
  `item_id` bigint unsigned NOT NULL,
  `percentage` decimal(5,2) NOT NULL,
  `min_percentage` decimal(5,2) DEFAULT NULL,
  `max_percentage` decimal(5,2) DEFAULT NULL,
  `cost_per_kg` decimal(10,2) DEFAULT 0.00,
  `nutritional_values` json DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `sort_order` int DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `feed_formulation_ingredients_formulation_item_unique` (`formulation_id`, `item_id`),
  KEY `feed_formulation_ingredients_item_id_foreign` (`item_id`),
  CONSTRAINT `feed_formulation_ingredients_formulation_id_foreign` FOREIGN KEY (`formulation_id`) REFERENCES `feed_formulations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `feed_formulation_ingredients_item_id_foreign` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Feed Consumption Records
DROP TABLE IF EXISTS `feed_consumption_records`;
CREATE TABLE `feed_consumption_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `flock_id` bigint unsigned NOT NULL,
  `feed_item_id` bigint unsigned NOT NULL,
  `record_date` date NOT NULL,
  `age_weeks` decimal(4,1) NOT NULL,
  `bird_count` int NOT NULL,
  `quantity_kg` decimal(8,2) NOT NULL,
  `cost_per_kg` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(12,2) GENERATED ALWAYS AS ((`quantity_kg` * `cost_per_kg`)) STORED,
  `consumption_per_bird_g` decimal(6,2) GENERATED ALWAYS AS (case when (`bird_count` > 0) then ((`quantity_kg` * 1000) / `bird_count`) else 0 end) STORED,
  `cumulative_consumption_kg` decimal(10,2) DEFAULT 0.00,
  `feed_conversion_ratio` decimal(6,3) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `recorded_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `feed_consumption_records_uuid_unique` (`uuid`),
  UNIQUE KEY `feed_consumption_records_flock_feed_date_unique` (`flock_id`, `feed_item_id`, `record_date`),
  KEY `feed_consumption_records_feed_item_id_foreign` (`feed_item_id`),
  KEY `feed_consumption_records_record_date_index` (`record_date`),
  KEY `feed_consumption_records_recorded_by_foreign` (`recorded_by`),
  CONSTRAINT `feed_consumption_records_flock_id_foreign` FOREIGN KEY (`flock_id`) REFERENCES `flocks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `feed_consumption_records_feed_item_id_foreign` FOREIGN KEY (`feed_item_id`) REFERENCES `items` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `feed_consumption_records_recorded_by_foreign` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC CALCULATIONS
-- ============================================================================

-- Trigger to update stock levels after stock movement
DELIMITER $$
CREATE TRIGGER `update_stock_levels_after_movement`
AFTER INSERT ON `stock_movements`
FOR EACH ROW
BEGIN
    INSERT INTO stock_levels (item_id, warehouse_id, quantity_on_hand, last_movement_at)
    VALUES (NEW.item_id, NEW.warehouse_id,
            CASE
                WHEN NEW.movement_type IN ('in', 'transfer', 'adjustment') THEN NEW.quantity
                ELSE -NEW.quantity
            END,
            NOW())
    ON DUPLICATE KEY UPDATE
        quantity_on_hand = quantity_on_hand +
            CASE
                WHEN NEW.movement_type IN ('in', 'transfer', 'adjustment') THEN NEW.quantity
                ELSE -NEW.quantity
            END,
        last_movement_at = NOW();
END$$

-- Trigger to update account balances after journal entry posting
CREATE TRIGGER `update_account_balances_after_journal_posting`
AFTER UPDATE ON `journal_entries`
FOR EACH ROW
BEGIN
    IF NEW.status = 'posted' AND OLD.status != 'posted' THEN
        UPDATE accounts a
        JOIN journal_entry_lines jel ON a.id = jel.account_id
        SET a.current_balance = a.current_balance +
            CASE
                WHEN a.normal_balance = 'debit' THEN (jel.debit_amount - jel.credit_amount)
                ELSE (jel.credit_amount - jel.debit_amount)
            END
        WHERE jel.journal_entry_id = NEW.id;
    END IF;
END$$

-- Trigger to update flock population after mortality/culling
CREATE TRIGGER `update_flock_population_after_egg_record`
AFTER INSERT ON `egg_production_records`
FOR EACH ROW
BEGIN
    UPDATE flocks
    SET current_count = current_count - NEW.mortality_count - NEW.culled_count,
        mortality_count = mortality_count + NEW.mortality_count,
        culled_count = culled_count + NEW.culled_count
    WHERE id = NEW.flock_id;
END$$

DELIMITER ;

-- ============================================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Composite indexes for common queries
CREATE INDEX `idx_stock_movements_item_warehouse_date` ON `stock_movements` (`item_id`, `warehouse_id`, `movement_date`);
CREATE INDEX `idx_egg_production_flock_date_range` ON `egg_production_records` (`flock_id`, `record_date`);
CREATE INDEX `idx_feed_consumption_flock_date_range` ON `feed_consumption_records` (`flock_id`, `record_date`);
CREATE INDEX `idx_journal_entries_date_status` ON `journal_entries` (`transaction_date`, `status`);
CREATE INDEX `idx_sales_orders_customer_date` ON `sales_orders` (`customer_id`, `order_date`);
CREATE INDEX `idx_business_partners_type_active` ON `business_partners` (`type`, `is_active`);

-- Full-text search indexes
ALTER TABLE `items` ADD FULLTEXT(`name`, `description`);
ALTER TABLE `business_partners` ADD FULLTEXT(`name`, `legal_name`);

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert default system configuration
INSERT INTO `system_config` (`key`, `value`, `description`, `type`, `is_public`) VALUES
('app.name', 'ERP Poultry Management System', 'Application name', 'string', true),
('app.version', '2.0.0', 'Application version', 'string', true),
('app.timezone', 'Asia/Jakarta', 'Default timezone', 'string', false),
('app.currency', 'IDR', 'Default currency', 'string', false),
('app.language', 'id', 'Default language', 'string', false),
('inventory.cost_method', 'fifo', 'Default inventory costing method', 'string', false),
('accounting.fiscal_year_start', '01-01', 'Fiscal year start (MM-DD)', 'string', false),
('poultry.default_production_start_weeks', '18', 'Default production start age in weeks', 'integer', false);

-- Insert core modules
INSERT INTO `modules` (`name`, `display_name`, `description`, `version`, `type`, `status`, `provides`) VALUES
('core', 'Core System', 'Core system functionality', '2.0.0', 'core', 'active', '["auth", "rbac", "api", "events"]'),
('user-management', 'User Management', 'User and role management', '2.0.0', 'plugin', 'active', '["users", "roles", "permissions"]'),
('inventory', 'Inventory Management', 'Inventory and stock management', '2.0.0', 'plugin', 'active', '["inventory", "stock", "warehouses"]'),
('accounting', 'Accounting', 'Financial accounting system', '2.0.0', 'plugin', 'active', '["accounts", "journals", "reports"]'),
('sales', 'Sales Management', 'Sales order and invoice management', '2.0.0', 'plugin', 'active', '["sales", "customers", "invoices"]'),
('poultry', 'Poultry Management', 'Poultry farming management', '2.0.0', 'plugin', 'active', '["farms", "houses", "flocks"]'),
('egg-production', 'Egg Production', 'Egg production tracking', '2.0.0', 'plugin', 'active', '["egg-records", "production-analytics"]'),
('feed-management', 'Feed Management', 'Feed formulation and consumption', '2.0.0', 'plugin', 'active', '["formulations", "feed-consumption"]');

-- Insert default roles
INSERT INTO `roles` (`name`, `display_name`, `description`, `level`, `is_system`) VALUES
('super_admin', 'Super Administrator', 'Full system access', 100, true),
('admin', 'Administrator', 'Administrative access', 90, true),
('manager', 'Manager', 'Management level access', 80, false),
('supervisor', 'Supervisor', 'Supervisory access', 70, false),
('operator', 'Operator', 'Operational access', 60, false),
('viewer', 'Viewer', 'Read-only access', 50, false);

-- Insert default permissions
INSERT INTO `permissions` (`name`, `display_name`, `description`, `module_name`, `group_name`) VALUES
-- Core permissions
('core.admin', 'Core Administration', 'Core system administration', 'core', 'administration'),
('core.config', 'System Configuration', 'Manage system configuration', 'core', 'configuration'),

-- User management permissions
('users.view', 'View Users', 'View user information', 'user-management', 'users'),
('users.create', 'Create Users', 'Create new users', 'user-management', 'users'),
('users.edit', 'Edit Users', 'Edit user information', 'user-management', 'users'),
('users.delete', 'Delete Users', 'Delete users', 'user-management', 'users'),
('roles.manage', 'Manage Roles', 'Manage user roles and permissions', 'user-management', 'roles'),

-- Inventory permissions
('inventory.view', 'View Inventory', 'View inventory information', 'inventory', 'inventory'),
('inventory.create', 'Create Items', 'Create new inventory items', 'inventory', 'inventory'),
('inventory.edit', 'Edit Items', 'Edit inventory items', 'inventory', 'inventory'),
('inventory.delete', 'Delete Items', 'Delete inventory items', 'inventory', 'inventory'),
('inventory.stock_movement', 'Stock Movement', 'Perform stock movements', 'inventory', 'stock'),

-- Accounting permissions
('accounting.view', 'View Accounting', 'View accounting information', 'accounting', 'accounting'),
('accounting.journal', 'Journal Entries', 'Create and manage journal entries', 'accounting', 'journals'),
('accounting.reports', 'Financial Reports', 'Generate financial reports', 'accounting', 'reports'),

-- Sales permissions
('sales.view', 'View Sales', 'View sales information', 'sales', 'sales'),
('sales.create', 'Create Sales', 'Create sales orders and invoices', 'sales', 'sales'),
('sales.edit', 'Edit Sales', 'Edit sales orders and invoices', 'sales', 'sales'),
('sales.delete', 'Delete Sales', 'Delete sales orders and invoices', 'sales', 'sales'),

-- Poultry permissions
('poultry.view', 'View Poultry', 'View poultry information', 'poultry', 'poultry'),
('poultry.manage', 'Manage Poultry', 'Manage farms, houses, and flocks', 'poultry', 'poultry'),
('poultry.records', 'Poultry Records', 'Manage poultry records', 'poultry', 'records'),

-- Egg production permissions
('eggs.view', 'View Egg Production', 'View egg production records', 'egg-production', 'production'),
('eggs.record', 'Record Egg Production', 'Record daily egg production', 'egg-production', 'production'),
('eggs.reports', 'Egg Production Reports', 'Generate egg production reports', 'egg-production', 'reports'),

-- Feed management permissions
('feed.view', 'View Feed Management', 'View feed information', 'feed-management', 'feed'),
('feed.formulate', 'Feed Formulation', 'Create and manage feed formulations', 'feed-management', 'formulation'),
('feed.record', 'Feed Consumption', 'Record feed consumption', 'feed-management', 'consumption');

-- Insert default units
INSERT INTO `units` (`code`, `name`, `symbol`, `type`, `is_base`) VALUES
-- Weight units
('kg', 'Kilogram', 'kg', 'weight', true),
('g', 'Gram', 'g', 'weight', false),
('ton', 'Ton', 't', 'weight', false),
-- Volume units
('liter', 'Liter', 'L', 'volume', true),
('ml', 'Milliliter', 'mL', 'volume', false),
-- Count units
('pcs', 'Pieces', 'pcs', 'count', true),
('dozen', 'Dozen', 'dz', 'count', false),
('box', 'Box', 'box', 'count', false),
-- Area units
('m2', 'Square Meter', 'm²', 'area', true),
('hectare', 'Hectare', 'ha', 'area', false);

-- Update unit conversion factors
UPDATE `units` SET `base_unit_id` = (SELECT id FROM units WHERE code = 'kg'), `conversion_factor` = 0.001 WHERE code = 'g';
UPDATE `units` SET `base_unit_id` = (SELECT id FROM units WHERE code = 'kg'), `conversion_factor` = 1000 WHERE code = 'ton';
UPDATE `units` SET `base_unit_id` = (SELECT id FROM units WHERE code = 'liter'), `conversion_factor` = 0.001 WHERE code = 'ml';
UPDATE `units` SET `base_unit_id` = (SELECT id FROM units WHERE code = 'pcs'), `conversion_factor` = 12 WHERE code = 'dozen';
UPDATE `units` SET `base_unit_id` = (SELECT id FROM units WHERE code = 'm2'), `conversion_factor` = 10000 WHERE code = 'hectare';

-- Insert default item categories
INSERT INTO `item_categories` (`code`, `name`, `description`, `level`) VALUES
('FEED', 'Feed & Nutrition', 'Animal feed and nutritional supplements', 0),
('MED', 'Medical & Veterinary', 'Medicines, vaccines, and veterinary supplies', 0),
('EGG', 'Eggs & Products', 'Eggs and egg-based products', 0),
('EQUIP', 'Equipment & Tools', 'Farm equipment and tools', 0),
('SUPPLY', 'General Supplies', 'General farm supplies and consumables', 0);

-- Insert default poultry breeds
INSERT INTO `poultry_breeds` (`code`, `name`, `type`, `origin_country`, `description`) VALUES
('ISA-BROWN', 'ISA Brown', 'layer', 'France', 'High-producing brown egg layer'),
('LOHMANN', 'Lohmann Brown', 'layer', 'Germany', 'Excellent brown egg production'),
('HY-LINE', 'Hy-Line Brown', 'layer', 'USA', 'Superior brown egg layer'),
('NOVOGEN', 'Novogen Brown', 'layer', 'France', 'Robust brown egg layer'),
('ROSS-308', 'Ross 308', 'broiler', 'UK', 'Fast-growing broiler breed');

-- ============================================================================
-- ENABLE FOREIGN KEY CHECKS
-- ============================================================================

SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- DATABASE OPTIMIZATION SETTINGS
-- ============================================================================

-- Optimize MySQL settings for ERP workload
-- These should be added to my.cnf configuration file:
/*
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
query_cache_size = 128M
query_cache_type = 1
max_connections = 200
wait_timeout = 28800
interactive_timeout = 28800
*/

-- ============================================================================
-- END OF DATABASE SCHEMA
-- ============================================================================
