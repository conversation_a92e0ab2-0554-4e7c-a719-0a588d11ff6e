{"ast": null, "code": "// This icon file is generated automatically.\nvar TwitchFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"fill-rule\": \"evenodd\",\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"filter\",\n        \"attrs\": {\n          \"filterUnits\": \"objectBoundingBox\",\n          \"height\": \"102.3%\",\n          \"id\": \"a\",\n          \"width\": \"102.3%\",\n          \"x\": \"-1.2%\",\n          \"y\": \"-1.2%\"\n        },\n        \"children\": [{\n          \"tag\": \"feOffset\",\n          \"attrs\": {\n            \"dy\": \"2\",\n            \"in\": \"SourceAlpha\",\n            \"result\": \"shadowOffsetOuter1\"\n          }\n        }, {\n          \"tag\": \"feGaussianBlur\",\n          \"attrs\": {\n            \"in\": \"shadowOffsetOuter1\",\n            \"result\": \"shadowBlurOuter1\",\n            \"stdDeviation\": \"2\"\n          }\n        }, {\n          \"tag\": \"feColorMatrix\",\n          \"attrs\": {\n            \"in\": \"shadowBlurOuter1\",\n            \"result\": \"shadowMatrixOuter1\",\n            \"values\": \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0\"\n          }\n        }, {\n          \"tag\": \"feMerge\",\n          \"attrs\": {},\n          \"children\": [{\n            \"tag\": \"feMergeNode\",\n            \"attrs\": {\n              \"in\": \"shadowMatrixOuter1\"\n            }\n          }, {\n            \"tag\": \"feMergeNode\",\n            \"attrs\": {\n              \"in\": \"SourceGraphic\"\n            }\n          }]\n        }]\n      }]\n    }, {\n      \"tag\": \"g\",\n      \"attrs\": {\n        \"filter\": \"url(#a)\",\n        \"transform\": \"translate(9 9)\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M185.14 112L128 254.86V797.7h171.43V912H413.7L528 797.71h142.86l200-200V112zm314.29 428.57H413.7V310.21h85.72zm200 0H613.7V310.21h85.72z\"\n        }\n      }]\n    }]\n  },\n  \"name\": \"twitch\",\n  \"theme\": \"filled\"\n};\nexport default TwitchFilled;", "map": {"version": 3, "names": ["TwitchFilled"], "sources": ["C:/laragon/www/neomuria2025_augment/frontend/node_modules/@ant-design/icons-svg/es/asn/TwitchFilled.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar TwitchFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"filter\", \"attrs\": { \"filterUnits\": \"objectBoundingBox\", \"height\": \"102.3%\", \"id\": \"a\", \"width\": \"102.3%\", \"x\": \"-1.2%\", \"y\": \"-1.2%\" }, \"children\": [{ \"tag\": \"feOffset\", \"attrs\": { \"dy\": \"2\", \"in\": \"SourceAlpha\", \"result\": \"shadowOffsetOuter1\" } }, { \"tag\": \"feGaussianBlur\", \"attrs\": { \"in\": \"shadowOffsetOuter1\", \"result\": \"shadowBlurOuter1\", \"stdDeviation\": \"2\" } }, { \"tag\": \"feColorMatrix\", \"attrs\": { \"in\": \"shadowBlurOuter1\", \"result\": \"shadowMatrixOuter1\", \"values\": \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0\" } }, { \"tag\": \"feMerge\", \"attrs\": {}, \"children\": [{ \"tag\": \"feMergeNode\", \"attrs\": { \"in\": \"shadowMatrixOuter1\" } }, { \"tag\": \"feMergeNode\", \"attrs\": { \"in\": \"SourceGraphic\" } }] }] }] }, { \"tag\": \"g\", \"attrs\": { \"filter\": \"url(#a)\", \"transform\": \"translate(9 9)\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M185.14 112L128 254.86V797.7h171.43V912H413.7L528 797.71h142.86l200-200V112zm314.29 428.57H413.7V310.21h85.72zm200 0H613.7V310.21h85.72z\" } }] }] }, \"name\": \"twitch\", \"theme\": \"filled\" };\nexport default TwitchFilled;\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,WAAW,EAAE,SAAS;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,QAAQ;QAAE,OAAO,EAAE;UAAE,aAAa,EAAE,mBAAmB;UAAE,QAAQ,EAAE,QAAQ;UAAE,IAAI,EAAE,GAAG;UAAE,OAAO,EAAE,QAAQ;UAAE,GAAG,EAAE,OAAO;UAAE,GAAG,EAAE;QAAQ,CAAC;QAAE,UAAU,EAAE,CAAC;UAAE,KAAK,EAAE,UAAU;UAAE,OAAO,EAAE;YAAE,IAAI,EAAE,GAAG;YAAE,IAAI,EAAE,aAAa;YAAE,QAAQ,EAAE;UAAqB;QAAE,CAAC,EAAE;UAAE,KAAK,EAAE,gBAAgB;UAAE,OAAO,EAAE;YAAE,IAAI,EAAE,oBAAoB;YAAE,QAAQ,EAAE,kBAAkB;YAAE,cAAc,EAAE;UAAI;QAAE,CAAC,EAAE;UAAE,KAAK,EAAE,eAAe;UAAE,OAAO,EAAE;YAAE,IAAI,EAAE,kBAAkB;YAAE,QAAQ,EAAE,oBAAoB;YAAE,QAAQ,EAAE;UAA4C;QAAE,CAAC,EAAE;UAAE,KAAK,EAAE,SAAS;UAAE,OAAO,EAAE,CAAC,CAAC;UAAE,UAAU,EAAE,CAAC;YAAE,KAAK,EAAE,aAAa;YAAE,OAAO,EAAE;cAAE,IAAI,EAAE;YAAqB;UAAE,CAAC,EAAE;YAAE,KAAK,EAAE,aAAa;YAAE,OAAO,EAAE;cAAE,IAAI,EAAE;YAAgB;UAAE,CAAC;QAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,GAAG;MAAE,OAAO,EAAE;QAAE,QAAQ,EAAE,SAAS;QAAE,WAAW,EAAE;MAAiB,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE;QAA2I;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,QAAQ;EAAE,OAAO,EAAE;AAAS,CAAC;AAClsC,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}