<?php

namespace Modules\PoultryManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FarmStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $farmId = $this->route('farm')?->id;

        return [
            'farm_name' => ['required', 'string', 'max:255'],
            'farm_type' => ['required', 'in:commercial,smallholder,breeding,research'],
            'owner_name' => ['required', 'string', 'max:255'],
            'manager_id' => ['nullable', 'exists:users,id'],
            'address' => ['required', 'string'],
            'city' => ['required', 'string', 'max:100'],
            'province' => ['required', 'string', 'max:100'],
            'postal_code' => ['nullable', 'string', 'max:10'],
            'country' => ['nullable', 'string', 'max:100'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'total_area_hectares' => ['nullable', 'numeric', 'min:0'],
            'established_date' => ['nullable', 'date', 'before_or_equal:today'],
            'license_number' => ['nullable', 'string', 'max:100'],
            'certification' => ['nullable', 'array'],
            'certification.*' => ['string'],
            'status' => ['nullable', 'in:active,inactive,maintenance'],
            'notes' => ['nullable', 'string'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->can('manage_farms');
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'farm_name.required' => 'Farm name is required.',
            'farm_type.required' => 'Farm type is required.',
            'farm_type.in' => 'Invalid farm type selected.',
            'owner_name.required' => 'Owner name is required.',
            'manager_id.exists' => 'Selected manager does not exist.',
            'address.required' => 'Farm address is required.',
            'city.required' => 'City is required.',
            'province.required' => 'Province is required.',
            'latitude.between' => 'Latitude must be between -90 and 90.',
            'longitude.between' => 'Longitude must be between -180 and 180.',
            'total_area_hectares.min' => 'Total area must be a positive number.',
            'established_date.before_or_equal' => 'Established date cannot be in the future.',
            'status.in' => 'Invalid status selected.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'farm_name' => 'farm name',
            'farm_type' => 'farm type',
            'owner_name' => 'owner name',
            'manager_id' => 'farm manager',
            'total_area_hectares' => 'total area',
            'established_date' => 'established date',
            'license_number' => 'license number',
        ];
    }
}
