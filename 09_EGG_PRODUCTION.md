# 09 - EGG PRODUCTION MODULE

## 📋 OVERVIEW

Modul Egg Production mengelola seluruh aspek produksi telur dari daily collection hingga grading, packaging, dan distribution. Modul ini terintegrasi dengan poultry management untuk flock tracking, inventory untuk stock management, dan sales untuk order fulfillment.

## 🎯 TUJUAN

- Daily egg collection recording dengan real-time tracking
- Egg grading dan quality control management
- Production performance analysis per flock
- Egg inventory management dengan expiry tracking
- Integration dengan sales untuk order fulfillment
- Production cost calculation dan profitability analysis
- Environmental factor correlation dengan production

## ⏱️ ESTIMASI WAKTU

**Total**: 18-22 jam
- Backend implementation: 12-14 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Production Manager** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Production tracking
egg_production_records
egg_collection_batches
egg_grading_records

-- Quality & inventory
egg_grades
egg_inventory
egg_inventory_movements

-- Supporting tables
flocks (dari Poultry Management)
houses (dari Poultry Management)
items (dari Inventory)
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Egg Production module
php artisan module:make EggProduction

# Create module components
php artisan module:make-controller EggProduction EggProductionController --api
php artisan module:make-controller EggProduction EggCollectionController --api
php artisan module:make-controller EggProduction EggGradingController --api
php artisan module:make-controller EggProduction EggInventoryController --api
php artisan module:make-controller EggProduction ProductionReportController --api
php artisan module:make-model EggProduction EggProductionRecord
php artisan module:make-model EggProduction EggCollectionBatch
php artisan module:make-model EggProduction EggGradingRecord
php artisan module:make-model EggProduction EggInventory
php artisan module:make-request EggProduction EggProductionStoreRequest
php artisan module:make-request EggProduction EggGradingStoreRequest
php artisan module:make-resource EggProduction EggProductionResource
php artisan module:make-resource EggProduction EggInventoryResource
php artisan module:make-policy EggProduction EggProductionPolicy
php artisan module:make-seeder EggProduction EggProductionSeeder
```

### **1.2 Egg Production Record Model**

```php
<?php
// Modules/EggProduction/Entities/EggProductionRecord.php

namespace Modules\EggProduction\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class EggProductionRecord extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'record_date',
        'flock_id',
        'house_id',
        'collection_time',
        'eggs_collected',
        'mortality_count',
        'culled_count',
        'feed_consumed_kg',
        'water_consumed_liters',
        'temperature_min',
        'temperature_max',
        'humidity_percentage',
        'lighting_hours',
        'notes',
        'recorded_by',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'record_date' => 'date',
        'collection_time' => 'time',
        'eggs_collected' => 'integer',
        'mortality_count' => 'integer',
        'culled_count' => 'integer',
        'feed_consumed_kg' => 'decimal:2',
        'water_consumed_liters' => 'decimal:2',
        'temperature_min' => 'decimal:1',
        'temperature_max' => 'decimal:1',
        'humidity_percentage' => 'decimal:1',
        'lighting_hours' => 'decimal:1',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['eggs_collected', 'mortality_count', 'feed_consumed_kg'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function flock()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\Flock::class);
    }

    public function house()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\House::class);
    }

    public function collectionBatches()
    {
        return $this->hasMany(EggCollectionBatch::class, 'production_record_id');
    }

    public function recordedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'recorded_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('record_date', [$startDate, $endDate]);
    }

    public function scopeByFlock($query, $flockId)
    {
        return $query->where('flock_id', $flockId);
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeToday($query)
    {
        return $query->where('record_date', now()->toDateString());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('record_date', [
            now()->startOfWeek()->toDateString(),
            now()->endOfWeek()->toDateString()
        ]);
    }

    // Accessors
    public function getLayingPercentageAttribute(): float
    {
        $currentBirds = $this->flock->current_count;
        return $currentBirds > 0 ? ($this->eggs_collected / $currentBirds) * 100 : 0;
    }

    public function getFeedConversionRatioAttribute(): float
    {
        return $this->eggs_collected > 0 ? $this->feed_consumed_kg / $this->eggs_collected : 0;
    }

    public function getEggMassAttribute(): float
    {
        // Assuming average egg weight of 60g
        return $this->eggs_collected * 0.06;
    }

    public function getFeedEfficiencyAttribute(): float
    {
        $eggMass = $this->egg_mass;
        return $eggMass > 0 ? $this->feed_consumed_kg / $eggMass : 0;
    }

    public function getWaterFeedRatioAttribute(): float
    {
        return $this->feed_consumed_kg > 0 ? $this->water_consumed_liters / $this->feed_consumed_kg : 0;
    }

    public function getAverageTemperatureAttribute(): float
    {
        return ($this->temperature_min + $this->temperature_max) / 2;
    }

    // Methods
    public function calculatePerformanceMetrics(): array
    {
        return [
            'laying_percentage' => $this->laying_percentage,
            'feed_conversion_ratio' => $this->feed_conversion_ratio,
            'feed_efficiency' => $this->feed_efficiency,
            'water_feed_ratio' => $this->water_feed_ratio,
            'mortality_rate' => $this->flock->current_count > 0 
                ? ($this->mortality_count / $this->flock->current_count) * 100 
                : 0,
            'egg_mass' => $this->egg_mass,
            'eggs_per_bird' => $this->flock->current_count > 0 
                ? $this->eggs_collected / $this->flock->current_count 
                : 0,
        ];
    }

    public static function getDailyProduction(string $date = null): array
    {
        $date = $date ?: now()->toDateString();
        
        $records = static::with(['flock', 'house'])
            ->where('record_date', $date)
            ->get();

        return [
            'date' => $date,
            'total_eggs' => $records->sum('eggs_collected'),
            'total_mortality' => $records->sum('mortality_count'),
            'total_feed_consumed' => $records->sum('feed_consumed_kg'),
            'total_water_consumed' => $records->sum('water_consumed_liters'),
            'average_laying_percentage' => $records->avg('laying_percentage'),
            'average_temperature' => $records->avg('average_temperature'),
            'average_humidity' => $records->avg('humidity_percentage'),
            'houses_count' => $records->count(),
            'active_flocks' => $records->pluck('flock_id')->unique()->count(),
        ];
    }
}
```

### **1.3 Egg Collection Batch Model**

```php
<?php
// Modules/EggProduction/Entities/EggCollectionBatch.php

namespace Modules\EggProduction\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class EggCollectionBatch extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'batch_number',
        'production_record_id',
        'collection_date',
        'collection_time',
        'collector_id',
        'total_eggs',
        'dirty_eggs',
        'broken_eggs',
        'small_eggs',
        'normal_eggs',
        'large_eggs',
        'extra_large_eggs',
        'double_yolk_eggs',
        'soft_shell_eggs',
        'cracked_eggs',
        'blood_spot_eggs',
        'collection_notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'collection_date' => 'date',
        'collection_time' => 'time',
        'total_eggs' => 'integer',
        'dirty_eggs' => 'integer',
        'broken_eggs' => 'integer',
        'small_eggs' => 'integer',
        'normal_eggs' => 'integer',
        'large_eggs' => 'integer',
        'extra_large_eggs' => 'integer',
        'double_yolk_eggs' => 'integer',
        'soft_shell_eggs' => 'integer',
        'cracked_eggs' => 'integer',
        'blood_spot_eggs' => 'integer',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['total_eggs', 'status', 'broken_eggs'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function productionRecord()
    {
        return $this->belongsTo(EggProductionRecord::class, 'production_record_id');
    }

    public function collector()
    {
        return $this->belongsTo(\App\Models\User::class, 'collector_id');
    }

    public function gradingRecords()
    {
        return $this->hasMany(EggGradingRecord::class, 'batch_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('collection_date', [$startDate, $endDate]);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeGraded($query)
    {
        return $query->where('status', 'graded');
    }

    // Accessors
    public function getGoodEggsAttribute(): int
    {
        return $this->normal_eggs + $this->large_eggs + $this->extra_large_eggs;
    }

    public function getDefectiveEggsAttribute(): int
    {
        return $this->dirty_eggs + $this->broken_eggs + $this->soft_shell_eggs + 
               $this->cracked_eggs + $this->blood_spot_eggs;
    }

    public function getQualityPercentageAttribute(): float
    {
        return $this->total_eggs > 0 ? ($this->good_eggs / $this->total_eggs) * 100 : 0;
    }

    public function getBreakagePercentageAttribute(): float
    {
        return $this->total_eggs > 0 ? ($this->broken_eggs / $this->total_eggs) * 100 : 0;
    }

    // Methods
    public function calculateTotals(): void
    {
        $total = $this->dirty_eggs + $this->broken_eggs + $this->small_eggs + 
                $this->normal_eggs + $this->large_eggs + $this->extra_large_eggs + 
                $this->double_yolk_eggs + $this->soft_shell_eggs + $this->cracked_eggs + 
                $this->blood_spot_eggs;

        $this->update(['total_eggs' => $total]);
    }

    public function getGradingBreakdown(): array
    {
        return [
            'Grade A (Extra Large)' => $this->extra_large_eggs,
            'Grade A (Large)' => $this->large_eggs,
            'Grade A (Normal)' => $this->normal_eggs,
            'Grade B (Small)' => $this->small_eggs,
            'Grade C (Dirty)' => $this->dirty_eggs,
            'Defective (Broken)' => $this->broken_eggs,
            'Defective (Soft Shell)' => $this->soft_shell_eggs,
            'Defective (Cracked)' => $this->cracked_eggs,
            'Defective (Blood Spot)' => $this->blood_spot_eggs,
            'Special (Double Yolk)' => $this->double_yolk_eggs,
        ];
    }

    public static function generateBatchNumber(): string
    {
        $date = now()->format('Ymd');
        $prefix = "BATCH{$date}";
        
        $lastBatch = static::where('batch_number', 'like', $prefix . '%')
            ->orderBy('batch_number', 'desc')
            ->first();
        
        if ($lastBatch) {
            $lastNumber = (int) substr($lastBatch->batch_number, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.4 Egg Production Service**

```php
<?php
// Modules/EggProduction/Services/EggProductionService.php

namespace Modules\EggProduction\Services;

use Modules\EggProduction\Entities\EggProductionRecord;
use Modules\EggProduction\Entities\EggCollectionBatch;
use Modules\EggProduction\Entities\EggGradingRecord;
use Modules\EggProduction\Entities\EggInventory;
use Modules\EggProduction\Events\EggProductionRecorded;
use Modules\EggProduction\Events\EggBatchGraded;
use Modules\Inventory\Services\StockService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class EggProductionService
{
    protected StockService $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    public function recordDailyProduction(array $data): EggProductionRecord
    {
        return DB::transaction(function () use ($data) {
            $record = EggProductionRecord::create([
                'uuid' => Str::uuid(),
                'record_date' => $data['record_date'],
                'flock_id' => $data['flock_id'],
                'house_id' => $data['house_id'],
                'collection_time' => $data['collection_time'] ?? now()->format('H:i:s'),
                'eggs_collected' => $data['eggs_collected'],
                'mortality_count' => $data['mortality_count'] ?? 0,
                'culled_count' => $data['culled_count'] ?? 0,
                'feed_consumed_kg' => $data['feed_consumed_kg'] ?? 0,
                'water_consumed_liters' => $data['water_consumed_liters'] ?? 0,
                'temperature_min' => $data['temperature_min'] ?? null,
                'temperature_max' => $data['temperature_max'] ?? null,
                'humidity_percentage' => $data['humidity_percentage'] ?? null,
                'lighting_hours' => $data['lighting_hours'] ?? null,
                'notes' => $data['notes'] ?? null,
                'recorded_by' => $data['recorded_by'] ?? auth()->id(),
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Update flock population if there are mortalities or culls
            if ($data['mortality_count'] > 0 || $data['culled_count'] > 0) {
                $record->flock->updatePopulation(
                    $data['mortality_count'] ?? 0,
                    $data['culled_count'] ?? 0,
                    0
                );
            }

            // Create collection batch if eggs were collected
            if ($data['eggs_collected'] > 0) {
                $this->createCollectionBatch($record, $data);
            }

            event(new EggProductionRecorded($record));

            return $record;
        });
    }

    private function createCollectionBatch(EggProductionRecord $record, array $data): EggCollectionBatch
    {
        return EggCollectionBatch::create([
            'uuid' => Str::uuid(),
            'batch_number' => EggCollectionBatch::generateBatchNumber(),
            'production_record_id' => $record->id,
            'collection_date' => $record->record_date,
            'collection_time' => $record->collection_time,
            'collector_id' => $record->recorded_by,
            'total_eggs' => $record->eggs_collected,
            'status' => 'pending',
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);
    }

    public function gradeEggBatch(EggCollectionBatch $batch, array $gradingData): EggGradingRecord
    {
        return DB::transaction(function () use ($batch, $gradingData) {
            // Update batch with grading details
            $batch->update([
                'dirty_eggs' => $gradingData['dirty_eggs'] ?? 0,
                'broken_eggs' => $gradingData['broken_eggs'] ?? 0,
                'small_eggs' => $gradingData['small_eggs'] ?? 0,
                'normal_eggs' => $gradingData['normal_eggs'] ?? 0,
                'large_eggs' => $gradingData['large_eggs'] ?? 0,
                'extra_large_eggs' => $gradingData['extra_large_eggs'] ?? 0,
                'double_yolk_eggs' => $gradingData['double_yolk_eggs'] ?? 0,
                'soft_shell_eggs' => $gradingData['soft_shell_eggs'] ?? 0,
                'cracked_eggs' => $gradingData['cracked_eggs'] ?? 0,
                'blood_spot_eggs' => $gradingData['blood_spot_eggs'] ?? 0,
                'status' => 'graded',
                'updated_by' => auth()->id(),
            ]);

            // Create grading record
            $gradingRecord = EggGradingRecord::create([
                'uuid' => Str::uuid(),
                'batch_id' => $batch->id,
                'grading_date' => $gradingData['grading_date'] ?? now()->toDateString(),
                'grader_id' => $gradingData['grader_id'] ?? auth()->id(),
                'grade_a_count' => ($gradingData['normal_eggs'] ?? 0) + 
                                  ($gradingData['large_eggs'] ?? 0) + 
                                  ($gradingData['extra_large_eggs'] ?? 0),
                'grade_b_count' => $gradingData['small_eggs'] ?? 0,
                'grade_c_count' => $gradingData['dirty_eggs'] ?? 0,
                'reject_count' => ($gradingData['broken_eggs'] ?? 0) + 
                                 ($gradingData['soft_shell_eggs'] ?? 0) + 
                                 ($gradingData['cracked_eggs'] ?? 0) + 
                                 ($gradingData['blood_spot_eggs'] ?? 0),
                'notes' => $gradingData['notes'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Update inventory for each grade
            $this->updateEggInventory($batch, $gradingData);

            event(new EggBatchGraded($batch, $gradingRecord));

            return $gradingRecord;
        });
    }

    private function updateEggInventory(EggCollectionBatch $batch, array $gradingData): void
    {
        $grades = [
            'extra_large' => $gradingData['extra_large_eggs'] ?? 0,
            'large' => $gradingData['large_eggs'] ?? 0,
            'normal' => $gradingData['normal_eggs'] ?? 0,
            'small' => $gradingData['small_eggs'] ?? 0,
            'dirty' => $gradingData['dirty_eggs'] ?? 0,
        ];

        foreach ($grades as $grade => $quantity) {
            if ($quantity > 0) {
                $this->addToEggInventory($batch, $grade, $quantity);
            }
        }
    }

    private function addToEggInventory(EggCollectionBatch $batch, string $grade, int $quantity): void
    {
        $inventory = EggInventory::firstOrCreate([
            'batch_id' => $batch->id,
            'grade' => $grade,
            'production_date' => $batch->collection_date,
        ], [
            'uuid' => Str::uuid(),
            'quantity_available' => 0,
            'quantity_reserved' => 0,
            'quantity_sold' => 0,
            'expiry_date' => $batch->collection_date->addDays(28), // 4 weeks shelf life
            'status' => 'available',
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ]);

        $inventory->increment('quantity_available', $quantity);
    }

    public function getProductionAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();
        $flockId = $filters['flock_id'] ?? null;
        $houseId = $filters['house_id'] ?? null;

        $query = EggProductionRecord::byDateRange($startDate, $endDate);

        if ($flockId) {
            $query->where('flock_id', $flockId);
        }

        if ($houseId) {
            $query->where('house_id', $houseId);
        }

        $records = $query->with(['flock', 'house'])->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => [
                'total_eggs' => $records->sum('eggs_collected'),
                'total_mortality' => $records->sum('mortality_count'),
                'total_feed_consumed' => $records->sum('feed_consumed_kg'),
                'total_water_consumed' => $records->sum('water_consumed_liters'),
                'average_laying_percentage' => $records->avg('laying_percentage'),
                'average_fcr' => $records->avg('feed_conversion_ratio'),
                'average_temperature' => $records->avg('average_temperature'),
                'average_humidity' => $records->avg('humidity_percentage'),
            ],
            'daily_production' => $this->getDailyProductionTrend($records),
            'flock_performance' => $this->getFlockPerformance($records),
            'house_performance' => $this->getHousePerformance($records),
            'quality_metrics' => $this->getQualityMetrics($startDate, $endDate),
        ];
    }

    private function getDailyProductionTrend($records): array
    {
        return $records->groupBy('record_date')
            ->map(function ($dayRecords, $date) {
                return [
                    'date' => $date,
                    'total_eggs' => $dayRecords->sum('eggs_collected'),
                    'laying_percentage' => $dayRecords->avg('laying_percentage'),
                    'mortality' => $dayRecords->sum('mortality_count'),
                    'feed_consumed' => $dayRecords->sum('feed_consumed_kg'),
                ];
            })
            ->values()
            ->toArray();
    }

    private function getFlockPerformance($records): array
    {
        return $records->groupBy('flock_id')
            ->map(function ($flockRecords) {
                $flock = $flockRecords->first()->flock;
                return [
                    'flock_number' => $flock->flock_number,
                    'breed' => $flock->breed->name,
                    'age_weeks' => $flock->age_weeks,
                    'total_eggs' => $flockRecords->sum('eggs_collected'),
                    'average_laying_percentage' => $flockRecords->avg('laying_percentage'),
                    'average_fcr' => $flockRecords->avg('feed_conversion_ratio'),
                    'mortality_count' => $flockRecords->sum('mortality_count'),
                ];
            })
            ->values()
            ->toArray();
    }

    private function getHousePerformance($records): array
    {
        return $records->groupBy('house_id')
            ->map(function ($houseRecords) {
                $house = $houseRecords->first()->house;
                return [
                    'house_code' => $house->code,
                    'house_name' => $house->name,
                    'farm' => $house->farm->name,
                    'total_eggs' => $houseRecords->sum('eggs_collected'),
                    'average_laying_percentage' => $houseRecords->avg('laying_percentage'),
                    'average_temperature' => $houseRecords->avg('average_temperature'),
                    'average_humidity' => $houseRecords->avg('humidity_percentage'),
                ];
            })
            ->values()
            ->toArray();
    }

    private function getQualityMetrics(string $startDate, string $endDate): array
    {
        $batches = EggCollectionBatch::byDateRange($startDate, $endDate)
            ->where('status', 'graded')
            ->get();

        $totalEggs = $batches->sum('total_eggs');
        
        return [
            'total_batches' => $batches->count(),
            'total_eggs_graded' => $totalEggs,
            'grade_a_percentage' => $totalEggs > 0 ? ($batches->sum('good_eggs') / $totalEggs) * 100 : 0,
            'defective_percentage' => $totalEggs > 0 ? ($batches->sum('defective_eggs') / $totalEggs) * 100 : 0,
            'breakage_percentage' => $totalEggs > 0 ? ($batches->sum('broken_eggs') / $totalEggs) * 100 : 0,
            'average_quality_score' => $batches->avg('quality_percentage'),
        ];
    }

    public function getInventoryStatus(): array
    {
        $inventory = EggInventory::where('status', 'available')
            ->where('quantity_available', '>', 0)
            ->get();

        $totalAvailable = $inventory->sum('quantity_available');
        $totalReserved = $inventory->sum('quantity_reserved');
        $expiringStock = $inventory->where('expiry_date', '<=', now()->addDays(7));

        return [
            'total_available' => $totalAvailable,
            'total_reserved' => $totalReserved,
            'net_available' => $totalAvailable - $totalReserved,
            'expiring_soon' => $expiringStock->sum('quantity_available'),
            'by_grade' => $inventory->groupBy('grade')->map(function ($items) {
                return $items->sum('quantity_available');
            }),
            'by_age' => $this->getInventoryByAge($inventory),
        ];
    }

    private function getInventoryByAge($inventory): array
    {
        return [
            'fresh_0_7_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(7);
            })->sum('quantity_available'),
            'good_8_14_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(14) && 
                       $item->production_date < now()->subDays(7);
            })->sum('quantity_available'),
            'aging_15_21_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(21) && 
                       $item->production_date < now()->subDays(14);
            })->sum('quantity_available'),
            'old_22_28_days' => $inventory->filter(function ($item) {
                return $item->production_date >= now()->subDays(28) && 
                       $item->production_date < now()->subDays(21);
            })->sum('quantity_available'),
        ];
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create egg production structure
mkdir -p frontend/src/modules/egg-production/{components,pages,hooks,services,types}
```

### **2.2 Egg Production Service**

```typescript
// frontend/src/modules/egg-production/services/eggProductionService.ts
import { apiClient } from '@/core/api/apiClient';

export interface EggProductionRecord {
  id: number;
  uuid: string;
  record_date: string;
  flock: any;
  house: any;
  collection_time: string;
  eggs_collected: number;
  mortality_count: number;
  culled_count: number;
  feed_consumed_kg: number;
  water_consumed_liters: number;
  temperature_min?: number;
  temperature_max?: number;
  humidity_percentage?: number;
  lighting_hours?: number;
  laying_percentage: number;
  feed_conversion_ratio: number;
  egg_mass: number;
  feed_efficiency: number;
  water_feed_ratio: number;
  average_temperature?: number;
  notes?: string;
  recorded_by?: any;
  collection_batches?: EggCollectionBatch[];
  created_at: string;
  updated_at: string;
}

export interface EggCollectionBatch {
  id: number;
  uuid: string;
  batch_number: string;
  production_record: EggProductionRecord;
  collection_date: string;
  collection_time: string;
  collector?: any;
  total_eggs: number;
  dirty_eggs: number;
  broken_eggs: number;
  small_eggs: number;
  normal_eggs: number;
  large_eggs: number;
  extra_large_eggs: number;
  double_yolk_eggs: number;
  soft_shell_eggs: number;
  cracked_eggs: number;
  blood_spot_eggs: number;
  good_eggs: number;
  defective_eggs: number;
  quality_percentage: number;
  breakage_percentage: number;
  collection_notes?: string;
  status: 'pending' | 'graded' | 'processed';
  grading_records?: EggGradingRecord[];
  created_at: string;
  updated_at: string;
}

export interface EggGradingRecord {
  id: number;
  uuid: string;
  batch: EggCollectionBatch;
  grading_date: string;
  grader?: any;
  grade_a_count: number;
  grade_b_count: number;
  grade_c_count: number;
  reject_count: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface EggInventory {
  id: number;
  uuid: string;
  batch?: EggCollectionBatch;
  grade: 'extra_large' | 'large' | 'normal' | 'small' | 'dirty';
  production_date: string;
  quantity_available: number;
  quantity_reserved: number;
  quantity_sold: number;
  expiry_date: string;
  status: 'available' | 'reserved' | 'expired' | 'sold';
  created_at: string;
  updated_at: string;
}

class EggProductionService {
  // Production Records
  async getProductionRecords(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/egg-production/records?${params.toString()}`);
  }

  async getProductionRecord(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/egg-production/records/${id}${params}`);
  }

  async createProductionRecord(data: {
    record_date: string;
    flock_id: number;
    house_id: number;
    collection_time?: string;
    eggs_collected: number;
    mortality_count?: number;
    culled_count?: number;
    feed_consumed_kg?: number;
    water_consumed_liters?: number;
    temperature_min?: number;
    temperature_max?: number;
    humidity_percentage?: number;
    lighting_hours?: number;
    notes?: string;
    recorded_by?: number;
  }) {
    return apiClient.post('/egg-production/records', data);
  }

  async updateProductionRecord(id: number, data: any) {
    return apiClient.put(`/egg-production/records/${id}`, data);
  }

  async deleteProductionRecord(id: number) {
    return apiClient.delete(`/egg-production/records/${id}`);
  }

  async getDailyProduction(date?: string) {
    const params = date ? `?date=${date}` : '';
    return apiClient.get(`/egg-production/daily-production${params}`);
  }

  // Collection Batches
  async getCollectionBatches(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/egg-production/batches?${params.toString()}`);
  }

  async getCollectionBatch(id: number) {
    return apiClient.get(`/egg-production/batches/${id}`);
  }

  async updateCollectionBatch(id: number, data: any) {
    return apiClient.put(`/egg-production/batches/${id}`, data);
  }

  // Egg Grading
  async gradeEggBatch(batchId: number, gradingData: {
    grading_date?: string;
    grader_id?: number;
    dirty_eggs?: number;
    broken_eggs?: number;
    small_eggs?: number;
    normal_eggs?: number;
    large_eggs?: number;
    extra_large_eggs?: number;
    double_yolk_eggs?: number;
    soft_shell_eggs?: number;
    cracked_eggs?: number;
    blood_spot_eggs?: number;
    notes?: string;
  }) {
    return apiClient.post(`/egg-production/batches/${batchId}/grade`, gradingData);
  }

  async getGradingRecords(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/egg-production/grading?${params.toString()}`);
  }

  // Egg Inventory
  async getEggInventory(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/egg-production/inventory?${params.toString()}`);
  }

  async getInventoryStatus() {
    return apiClient.get('/egg-production/inventory/status');
  }

  async reserveEggInventory(inventoryId: number, quantity: number) {
    return apiClient.post(`/egg-production/inventory/${inventoryId}/reserve`, { quantity });
  }

  async releaseEggInventory(inventoryId: number, quantity: number) {
    return apiClient.post(`/egg-production/inventory/${inventoryId}/release`, { quantity });
  }

  // Analytics & Reports
  async getProductionAnalytics(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/egg-production/analytics?${params.toString()}`);
  }

  async getProductionReport(reportType: string, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/egg-production/reports/${reportType}?${params.toString()}`);
  }

  async getFlockPerformance(flockId: number, period: string = '30d') {
    return apiClient.get(`/egg-production/flock/${flockId}/performance?period=${period}`);
  }

  async getHousePerformance(houseId: number, period: string = '30d') {
    return apiClient.get(`/egg-production/house/${houseId}/performance?period=${period}`);
  }
}

export const eggProductionService = new EggProductionService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/EggProduction/Routes/api.php

use Modules\EggProduction\Http\Controllers\EggProductionController;
use Modules\EggProduction\Http\Controllers\EggCollectionController;
use Modules\EggProduction\Http\Controllers\EggGradingController;
use Modules\EggProduction\Http\Controllers\EggInventoryController;
use Modules\EggProduction\Http\Controllers\ProductionReportController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    Route::prefix('egg-production')->group(function () {
        // Production Records
        Route::apiResource('records', EggProductionController::class);
        Route::get('daily-production', [EggProductionController::class, 'dailyProduction']);
        Route::get('flock/{flock}/performance', [EggProductionController::class, 'flockPerformance']);
        Route::get('house/{house}/performance', [EggProductionController::class, 'housePerformance']);
        
        // Collection Batches
        Route::apiResource('batches', EggCollectionController::class);
        Route::post('batches/{batch}/grade', [EggCollectionController::class, 'grade']);
        
        // Grading
        Route::get('grading', [EggGradingController::class, 'index']);
        Route::get('grading/{grading}', [EggGradingController::class, 'show']);
        
        // Inventory
        Route::get('inventory', [EggInventoryController::class, 'index']);
        Route::get('inventory/status', [EggInventoryController::class, 'status']);
        Route::post('inventory/{inventory}/reserve', [EggInventoryController::class, 'reserve']);
        Route::post('inventory/{inventory}/release', [EggInventoryController::class, 'release']);
        
        // Analytics & Reports
        Route::get('analytics', [ProductionReportController::class, 'analytics']);
        Route::get('reports/{type}', [ProductionReportController::class, 'report']);
    });
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Daily production recording working
- [ ] Egg collection batch management
- [ ] Grading system functional
- [ ] Inventory tracking accurate
- [ ] Performance calculations correct
- [ ] Integration dengan poultry management

### **Frontend**
- [ ] Production recording interface
- [ ] Collection batch management
- [ ] Grading interface working
- [ ] Inventory dashboard
- [ ] Analytics visualization
- [ ] Real-time production tracking

### **Integration**
- [ ] Flock population updates
- [ ] Inventory movements accurate
- [ ] Performance metrics calculation
- [ ] Quality tracking working
- [ ] Expiry date management

## 📞 NEXT STEPS

Setelah Egg Production module selesai:

1. **Test production workflow** end-to-end
2. **Verify inventory tracking** accuracy
3. **Test grading system** functionality
4. **Validate performance calculations**
5. **Commit module** ke repository
6. **Lanjut ke** `10_FEED_MANAGEMENT.md`

---

**IMPORTANT**: Egg Production adalah core revenue module untuk peternakan layer. Pastikan semua tracking dan calculations akurat untuk production optimization dan profitability analysis.
