# 06 - SALES MODULE

## 📋 OVERVIEW

Modul Sales mengelola seluruh proses penjualan dari quotation hingga delivery dan invoicing. Modul ini terintegrasi dengan inventory untuk stock management, accounting untuk automatic journal posting, dan customer management untuk CRM functionality.

## 🎯 TUJUAN

- Manajemen customer dan prospect database
- Sales quotation dan order processing
- Inventory allocation dan delivery management
- Invoicing dengan multiple payment terms
- Sales commission calculation
- Sales analytics dan reporting
- Integration dengan accounting untuk revenue recognition

## ⏱️ ESTIMASI WAKTU

**Total**: 22-26 jam
- Backend implementation: 14-18 jam
- Frontend implementation: 8-10 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Sales Manager** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Customer management
customers
customer_contacts
customer_addresses
customer_price_lists

-- Sales process
sales_quotations
sales_quotation_items
sales_orders
sales_order_items
delivery_orders
delivery_order_items
sales_invoices
sales_invoice_items

-- Supporting tables
payment_terms
sales_territories
sales_representatives
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Sales module
php artisan module:make Sales

# Create module components
php artisan module:make-controller Sales CustomerController --api
php artisan module:make-controller Sales SalesQuotationController --api
php artisan module:make-controller Sales SalesOrderController --api
php artisan module:make-controller Sales DeliveryOrderController --api
php artisan module:make-controller Sales SalesInvoiceController --api
php artisan module:make-controller Sales SalesReportController --api
php artisan module:make-model Sales Customer
php artisan module:make-model Sales SalesQuotation
php artisan module:make-model Sales SalesOrder
php artisan module:make-model Sales DeliveryOrder
php artisan module:make-model Sales SalesInvoice
php artisan module:make-request Sales CustomerStoreRequest
php artisan module:make-request Sales SalesOrderStoreRequest
php artisan module:make-resource Sales CustomerResource
php artisan module:make-resource Sales SalesOrderResource
php artisan module:make-policy Sales SalesPolicy
php artisan module:make-seeder Sales SalesSeeder
```

### **1.2 Customer Model**

```php
<?php
// Modules/Sales/Entities/Customer.php

namespace Modules\Sales\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Customer extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'uuid',
        'customer_code',
        'customer_name',
        'customer_type',
        'company_name',
        'tax_id',
        'email',
        'phone',
        'mobile',
        'website',
        'industry',
        'customer_group',
        'sales_rep_id',
        'territory_id',
        'payment_terms_id',
        'price_list_id',
        'credit_limit',
        'credit_days',
        'discount_percentage',
        'tax_exempt',
        'billing_address',
        'shipping_address',
        'notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'credit_days' => 'integer',
        'discount_percentage' => 'decimal:2',
        'tax_exempt' => 'boolean',
        'billing_address' => 'array',
        'shipping_address' => 'array',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['customer_name', 'status', 'credit_limit', 'payment_terms_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function salesRep()
    {
        return $this->belongsTo(\App\Models\User::class, 'sales_rep_id');
    }

    public function territory()
    {
        return $this->belongsTo(SalesTerritory::class, 'territory_id');
    }

    public function paymentTerms()
    {
        return $this->belongsTo(PaymentTerms::class, 'payment_terms_id');
    }

    public function priceList()
    {
        return $this->belongsTo(PriceList::class, 'price_list_id');
    }

    public function contacts()
    {
        return $this->hasMany(CustomerContact::class);
    }

    public function addresses()
    {
        return $this->hasMany(CustomerAddress::class);
    }

    public function quotations()
    {
        return $this->hasMany(SalesQuotation::class);
    }

    public function orders()
    {
        return $this->hasMany(SalesOrder::class);
    }

    public function invoices()
    {
        return $this->hasMany(SalesInvoice::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('customer_name', 'like', "%{$search}%")
              ->orWhere('customer_code', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    public function scopeByType($query, $type)
    {
        return $query->where('customer_type', $type);
    }

    public function scopeByTerritory($query, $territoryId)
    {
        return $query->where('territory_id', $territoryId);
    }

    public function scopeBySalesRep($query, $salesRepId)
    {
        return $query->where('sales_rep_id', $salesRepId);
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->company_name ?: $this->customer_name;
    }

    public function getCurrentBalanceAttribute(): float
    {
        return $this->invoices()
            ->where('status', '!=', 'paid')
            ->sum('total_amount');
    }

    public function getAvailableCreditAttribute(): float
    {
        return max(0, $this->credit_limit - $this->current_balance);
    }

    public function getIsOverCreditLimitAttribute(): bool
    {
        return $this->credit_limit > 0 && $this->current_balance > $this->credit_limit;
    }

    // Methods
    public function canPlaceOrder(float $orderAmount): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        if ($this->credit_limit > 0) {
            return ($this->current_balance + $orderAmount) <= $this->credit_limit;
        }

        return true;
    }

    public function getSalesHistory(string $period = '12m'): array
    {
        $startDate = now()->sub($period);
        
        $orders = $this->orders()
            ->where('order_date', '>=', $startDate)
            ->where('status', '!=', 'cancelled')
            ->get();

        $invoices = $this->invoices()
            ->where('invoice_date', '>=', $startDate)
            ->where('status', '!=', 'cancelled')
            ->get();

        return [
            'total_orders' => $orders->count(),
            'total_order_value' => $orders->sum('total_amount'),
            'total_invoices' => $invoices->count(),
            'total_invoice_value' => $invoices->sum('total_amount'),
            'average_order_value' => $orders->count() > 0 ? $orders->avg('total_amount') : 0,
            'last_order_date' => $orders->max('order_date'),
            'last_invoice_date' => $invoices->max('invoice_date'),
        ];
    }

    public static function generateCustomerCode(): string
    {
        $year = now()->year;
        $prefix = "CUST{$year}";
        
        $lastCustomer = static::where('customer_code', 'like', $prefix . '%')
            ->orderBy('customer_code', 'desc')
            ->first();
        
        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->customer_code, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.3 Sales Order Model**

```php
<?php
// Modules/Sales/Entities/SalesOrder.php

namespace Modules\Sales\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class SalesOrder extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'order_number',
        'customer_id',
        'quotation_id',
        'order_date',
        'required_date',
        'promised_date',
        'sales_rep_id',
        'territory_id',
        'payment_terms_id',
        'currency_id',
        'exchange_rate',
        'subtotal',
        'discount_amount',
        'tax_amount',
        'shipping_amount',
        'total_amount',
        'notes',
        'internal_notes',
        'status',
        'approved_by',
        'approved_at',
        'transaction_type',
        'barter_agreement_id',
        'cash_amount',
        'barter_value',
        'barter_settlement_status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'order_date' => 'date',
        'required_date' => 'date',
        'promised_date' => 'date',
        'exchange_rate' => 'decimal:6',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'cash_amount' => 'decimal:2',
        'barter_value' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'total_amount', 'promised_date'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function quotation()
    {
        return $this->belongsTo(SalesQuotation::class, 'quotation_id');
    }

    public function salesRep()
    {
        return $this->belongsTo(\App\Models\User::class, 'sales_rep_id');
    }

    public function territory()
    {
        return $this->belongsTo(SalesTerritory::class, 'territory_id');
    }

    public function paymentTerms()
    {
        return $this->belongsTo(PaymentTerms::class, 'payment_terms_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function items()
    {
        return $this->hasMany(SalesOrderItem::class);
    }

    public function deliveryOrders()
    {
        return $this->hasMany(DeliveryOrder::class);
    }

    public function invoices()
    {
        return $this->hasMany(SalesInvoice::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    public function barterAgreement()
    {
        return $this->belongsTo(\Modules\BarterTrade\Entities\BarterAgreement::class, 'barter_agreement_id');
    }

    public function barterTransactions()
    {
        return $this->morphMany(\Modules\BarterTrade\Entities\BarterTransaction::class, 'reference');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('order_date', [$startDate, $endDate]);
    }

    public function scopeBySalesRep($query, $salesRepId)
    {
        return $query->where('sales_rep_id', $salesRepId);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    // Accessors
    public function getIsApprovedAttribute(): bool
    {
        return $this->status === 'approved';
    }

    public function getCanBeApprovedAttribute(): bool
    {
        return $this->status === 'pending';
    }

    public function getCanBeCancelledAttribute(): bool
    {
        return in_array($this->status, ['pending', 'approved']) && 
               $this->deliveryOrders()->where('status', '!=', 'cancelled')->count() === 0;
    }

    public function getDeliveredQuantityAttribute(): float
    {
        return $this->deliveryOrders()
            ->where('status', 'delivered')
            ->with('items')
            ->get()
            ->flatMap->items
            ->sum('quantity');
    }

    public function getInvoicedAmountAttribute(): float
    {
        return $this->invoices()
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');
    }

    public function getOutstandingAmountAttribute(): float
    {
        return $this->total_amount - $this->invoiced_amount;
    }

    public function getIsFullyDeliveredAttribute(): bool
    {
        $orderedQuantity = $this->items->sum('quantity');
        return $orderedQuantity > 0 && $this->delivered_quantity >= $orderedQuantity;
    }

    public function getIsFullyInvoicedAttribute(): bool
    {
        return $this->outstanding_amount <= 0.01;
    }

    public function getIsMixedPaymentAttribute(): bool
    {
        return $this->transaction_type === 'mixed';
    }

    public function getIsBarterOnlyAttribute(): bool
    {
        return $this->transaction_type === 'barter';
    }

    public function getBarterPercentageAttribute(): float
    {
        return $this->total_amount > 0 ? ($this->barter_value / $this->total_amount) * 100 : 0;
    }

    public function getCashPercentageAttribute(): float
    {
        return $this->total_amount > 0 ? ($this->cash_amount / $this->total_amount) * 100 : 0;
    }

    public function getBarterSettlementPercentageAttribute(): float
    {
        if ($this->barter_value <= 0) return 100;

        $settledValue = $this->barterTransactions()
            ->where('settlement_status', 'settled')
            ->sum('barter_component');

        return ($settledValue / $this->barter_value) * 100;
    }

    // Methods
    public function calculateTotals(): void
    {
        $subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });

        $discountAmount = $subtotal * ($this->discount_percentage / 100);
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * ($this->tax_percentage / 100);
        $totalAmount = $taxableAmount + $taxAmount + $this->shipping_amount;

        $this->update([
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }

    public function approve(): bool
    {
        if (!$this->can_be_approved) {
            return false;
        }

        // Check customer credit limit
        if (!$this->customer->canPlaceOrder($this->total_amount)) {
            throw new \Exception('Customer credit limit exceeded');
        }

        // Reserve inventory
        foreach ($this->items as $item) {
            $stockLevel = \Modules\Inventory\Entities\StockLevel::where('item_id', $item->item_id)
                ->where('warehouse_id', $item->warehouse_id)
                ->first();

            if ($stockLevel) {
                $stockLevel->increment('quantity_reserved', $item->quantity);
            }
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => auth()->id(),
            'approved_at' => now(),
        ]);

        return true;
    }

    public function cancel(string $reason = null): bool
    {
        if (!$this->can_be_cancelled) {
            return false;
        }

        // Release reserved inventory
        if ($this->is_approved) {
            foreach ($this->items as $item) {
                $stockLevel = \Modules\Inventory\Entities\StockLevel::where('item_id', $item->item_id)
                    ->where('warehouse_id', $item->warehouse_id)
                    ->first();

                if ($stockLevel) {
                    $stockLevel->decrement('quantity_reserved', $item->quantity);
                }
            }
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . "\nCancelled: " . ($reason ?: 'No reason provided'),
        ]);

        return true;
    }

    public static function generateOrderNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $prefix = "SO{$year}{$month}";
        
        $lastOrder = static::where('order_number', 'like', $prefix . '%')
            ->orderBy('order_number', 'desc')
            ->first();
        
        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->order_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $newNumber;
    }
}
```

### **1.4 Sales Service**

```php
<?php
// Modules/Sales/Services/SalesService.php

namespace Modules\Sales\Services;

use Modules\Sales\Entities\SalesOrder;
use Modules\Sales\Entities\SalesInvoice;
use Modules\Sales\Entities\DeliveryOrder;
use Modules\Sales\Events\SalesOrderApproved;
use Modules\Sales\Events\SalesOrderDelivered;
use Modules\Sales\Events\SalesInvoiceCreated;
use Modules\Accounting\Services\AccountingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SalesService
{
    protected AccountingService $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    public function createSalesOrder(array $data): SalesOrder
    {
        return DB::transaction(function () use ($data) {
            $salesOrder = SalesOrder::create([
                'uuid' => Str::uuid(),
                'order_number' => SalesOrder::generateOrderNumber(),
                'customer_id' => $data['customer_id'],
                'quotation_id' => $data['quotation_id'] ?? null,
                'order_date' => $data['order_date'],
                'required_date' => $data['required_date'] ?? null,
                'promised_date' => $data['promised_date'] ?? null,
                'sales_rep_id' => $data['sales_rep_id'] ?? auth()->id(),
                'territory_id' => $data['territory_id'] ?? null,
                'payment_terms_id' => $data['payment_terms_id'],
                'currency_id' => $data['currency_id'] ?? 1,
                'exchange_rate' => $data['exchange_rate'] ?? 1.0,
                'shipping_amount' => $data['shipping_amount'] ?? 0,
                'notes' => $data['notes'] ?? null,
                'internal_notes' => $data['internal_notes'] ?? null,
                'status' => 'pending',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            foreach ($data['items'] as $itemData) {
                $salesOrder->items()->create([
                    'uuid' => Str::uuid(),
                    'item_id' => $itemData['item_id'],
                    'warehouse_id' => $itemData['warehouse_id'],
                    'description' => $itemData['description'] ?? null,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'discount_percentage' => $itemData['discount_percentage'] ?? 0,
                    'tax_percentage' => $itemData['tax_percentage'] ?? 0,
                    'line_total' => $itemData['quantity'] * $itemData['unit_price'],
                ]);
            }

            $salesOrder->calculateTotals();

            return $salesOrder;
        });
    }

    public function approveSalesOrder(SalesOrder $salesOrder): bool
    {
        $result = $salesOrder->approve();
        
        if ($result) {
            event(new SalesOrderApproved($salesOrder));
        }
        
        return $result;
    }

    public function createDeliveryOrder(SalesOrder $salesOrder, array $items): DeliveryOrder
    {
        return DB::transaction(function () use ($salesOrder, $items) {
            $deliveryOrder = DeliveryOrder::create([
                'uuid' => Str::uuid(),
                'delivery_number' => DeliveryOrder::generateDeliveryNumber(),
                'sales_order_id' => $salesOrder->id,
                'customer_id' => $salesOrder->customer_id,
                'delivery_date' => now()->toDateString(),
                'warehouse_id' => $items[0]['warehouse_id'], // Assuming single warehouse
                'status' => 'pending',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            foreach ($items as $itemData) {
                $deliveryOrder->items()->create([
                    'uuid' => Str::uuid(),
                    'sales_order_item_id' => $itemData['sales_order_item_id'],
                    'item_id' => $itemData['item_id'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                ]);
            }

            return $deliveryOrder;
        });
    }

    public function deliverOrder(DeliveryOrder $deliveryOrder): bool
    {
        return DB::transaction(function () use ($deliveryOrder) {
            // Update delivery status
            $deliveryOrder->update([
                'status' => 'delivered',
                'delivered_at' => now(),
                'delivered_by' => auth()->id(),
            ]);

            // Update inventory
            foreach ($deliveryOrder->items as $item) {
                // Move stock out
                app(\Modules\Inventory\Services\StockService::class)->moveStock(
                    $item->item,
                    $deliveryOrder->warehouse,
                    'out',
                    $item->quantity,
                    'sales_delivery',
                    [
                        'reference_type' => DeliveryOrder::class,
                        'reference_id' => $deliveryOrder->id,
                        'reference_number' => $deliveryOrder->delivery_number,
                        'reason' => 'Sales delivery',
                    ]
                );

                // Release reservation
                $stockLevel = \Modules\Inventory\Entities\StockLevel::where('item_id', $item->item_id)
                    ->where('warehouse_id', $deliveryOrder->warehouse_id)
                    ->first();

                if ($stockLevel) {
                    $stockLevel->decrement('quantity_reserved', $item->quantity);
                }
            }

            event(new SalesOrderDelivered($deliveryOrder));

            return true;
        });
    }

    public function createInvoice(SalesOrder $salesOrder, array $items = null): SalesInvoice
    {
        return DB::transaction(function () use ($salesOrder, $items) {
            $invoice = SalesInvoice::create([
                'uuid' => Str::uuid(),
                'invoice_number' => SalesInvoice::generateInvoiceNumber(),
                'sales_order_id' => $salesOrder->id,
                'customer_id' => $salesOrder->customer_id,
                'invoice_date' => now()->toDateString(),
                'due_date' => now()->addDays($salesOrder->paymentTerms->days ?? 30)->toDateString(),
                'payment_terms_id' => $salesOrder->payment_terms_id,
                'currency_id' => $salesOrder->currency_id,
                'exchange_rate' => $salesOrder->exchange_rate,
                'subtotal' => $salesOrder->subtotal,
                'discount_amount' => $salesOrder->discount_amount,
                'tax_amount' => $salesOrder->tax_amount,
                'total_amount' => $salesOrder->total_amount,
                'status' => 'pending',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Copy items from sales order or use provided items
            $itemsToInvoice = $items ?: $salesOrder->items;
            
            foreach ($itemsToInvoice as $item) {
                $invoice->items()->create([
                    'uuid' => Str::uuid(),
                    'sales_order_item_id' => $item['sales_order_item_id'] ?? $item->id,
                    'item_id' => $item['item_id'] ?? $item->item_id,
                    'description' => $item['description'] ?? $item->description,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'tax_percentage' => $item['tax_percentage'] ?? 0,
                    'line_total' => $item['quantity'] * $item['unit_price'],
                ]);
            }

            // Create accounting entries
            $this->createSalesAccountingEntries($invoice);

            event(new SalesInvoiceCreated($invoice));

            return $invoice;
        });
    }

    private function createSalesAccountingEntries(SalesInvoice $invoice): void
    {
        $entries = [];

        // Accounts Receivable (Debit)
        $entries[] = [
            'account_id' => config('sales.accounts.accounts_receivable'),
            'description' => "Sales to {$invoice->customer->customer_name}",
            'debit_amount' => $invoice->total_amount,
            'credit_amount' => 0,
        ];

        // Sales Revenue (Credit)
        $entries[] = [
            'account_id' => config('sales.accounts.sales_revenue'),
            'description' => "Sales revenue",
            'debit_amount' => 0,
            'credit_amount' => $invoice->subtotal,
        ];

        // Sales Tax (Credit)
        if ($invoice->tax_amount > 0) {
            $entries[] = [
                'account_id' => config('sales.accounts.sales_tax'),
                'description' => "Sales tax",
                'debit_amount' => 0,
                'credit_amount' => $invoice->tax_amount,
            ];
        }

        $this->accountingService->createAutomaticEntry(
            SalesInvoice::class,
            $invoice->id,
            $entries
        );
    }

    public function getSalesAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();
        $salesRepId = $filters['sales_rep_id'] ?? null;

        $ordersQuery = SalesOrder::byDateRange($startDate, $endDate);
        $invoicesQuery = SalesInvoice::whereBetween('invoice_date', [$startDate, $endDate]);

        if ($salesRepId) {
            $ordersQuery->where('sales_rep_id', $salesRepId);
            $invoicesQuery->where('sales_rep_id', $salesRepId);
        }

        $orders = $ordersQuery->get();
        $invoices = $invoicesQuery->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'orders' => [
                'count' => $orders->count(),
                'total_value' => $orders->sum('total_amount'),
                'average_value' => $orders->avg('total_amount'),
                'pending_count' => $orders->where('status', 'pending')->count(),
                'approved_count' => $orders->where('status', 'approved')->count(),
            ],
            'invoices' => [
                'count' => $invoices->count(),
                'total_value' => $invoices->sum('total_amount'),
                'paid_value' => $invoices->where('status', 'paid')->sum('total_amount'),
                'outstanding_value' => $invoices->where('status', '!=', 'paid')->sum('total_amount'),
            ],
            'top_customers' => $this->getTopCustomers($startDate, $endDate, 5),
            'sales_by_rep' => $this->getSalesByRep($startDate, $endDate),
        ];
    }

    private function getTopCustomers(string $startDate, string $endDate, int $limit): array
    {
        return SalesOrder::with('customer')
            ->byDateRange($startDate, $endDate)
            ->selectRaw('customer_id, SUM(total_amount) as total_sales, COUNT(*) as order_count')
            ->groupBy('customer_id')
            ->orderBy('total_sales', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($order) {
                return [
                    'customer_name' => $order->customer->customer_name,
                    'total_sales' => $order->total_sales,
                    'order_count' => $order->order_count,
                ];
            })
            ->toArray();
    }

    private function getSalesByRep(string $startDate, string $endDate): array
    {
        return SalesOrder::with('salesRep')
            ->byDateRange($startDate, $endDate)
            ->selectRaw('sales_rep_id, SUM(total_amount) as total_sales, COUNT(*) as order_count')
            ->groupBy('sales_rep_id')
            ->orderBy('total_sales', 'desc')
            ->get()
            ->map(function ($order) {
                return [
                    'sales_rep_name' => $order->salesRep->name ?? 'Unknown',
                    'total_sales' => $order->total_sales,
                    'order_count' => $order->order_count,
                ];
            })
            ->toArray();
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create sales structure
mkdir -p frontend/src/modules/sales/{components,pages,hooks,services,types}
```

### **2.2 Sales Service**

```typescript
// frontend/src/modules/sales/services/salesService.ts
import { apiClient } from '@/core/api/apiClient';

export interface Customer {
  id: number;
  uuid: string;
  customer_code: string;
  customer_name: string;
  customer_type: 'individual' | 'company';
  company_name?: string;
  tax_id?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  industry?: string;
  customer_group?: string;
  sales_rep?: any;
  territory?: any;
  payment_terms?: PaymentTerms;
  price_list?: any;
  credit_limit: number;
  credit_days: number;
  discount_percentage: number;
  tax_exempt: boolean;
  billing_address?: any;
  shipping_address?: any;
  display_name: string;
  current_balance: number;
  available_credit: number;
  is_over_credit_limit: boolean;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
}

export interface SalesOrder {
  id: number;
  uuid: string;
  order_number: string;
  customer: Customer;
  quotation?: any;
  order_date: string;
  required_date?: string;
  promised_date?: string;
  sales_rep?: any;
  territory?: any;
  payment_terms: PaymentTerms;
  currency: Currency;
  exchange_rate: number;
  subtotal: number;
  discount_amount: number;
  tax_amount: number;
  shipping_amount: number;
  total_amount: number;
  notes?: string;
  internal_notes?: string;
  status: 'pending' | 'approved' | 'delivered' | 'invoiced' | 'cancelled';
  is_approved: boolean;
  can_be_approved: boolean;
  can_be_cancelled: boolean;
  delivered_quantity: number;
  invoiced_amount: number;
  outstanding_amount: number;
  is_fully_delivered: boolean;
  is_fully_invoiced: boolean;
  approved_by?: any;
  approved_at?: string;
  items: SalesOrderItem[];
  created_at: string;
  updated_at: string;
}

export interface SalesOrderItem {
  id: number;
  uuid: string;
  item: any;
  warehouse: any;
  description?: string;
  quantity: number;
  unit_price: number;
  discount_percentage: number;
  tax_percentage: number;
  line_total: number;
}

export interface PaymentTerms {
  id: number;
  name: string;
  days: number;
  discount_percentage?: number;
  discount_days?: number;
}

export interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  exchange_rate: number;
}

class SalesService {
  // Customers
  async getCustomers(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/customers?${params.toString()}`);
  }

  async getCustomer(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/customers/${id}${params}`);
  }

  async createCustomer(data: any) {
    return apiClient.post('/customers', data);
  }

  async updateCustomer(id: number, data: any) {
    return apiClient.put(`/customers/${id}`, data);
  }

  async deleteCustomer(id: number) {
    return apiClient.delete(`/customers/${id}`);
  }

  async getCustomerSalesHistory(customerId: number, period: string = '12m') {
    return apiClient.get(`/customers/${customerId}/sales-history?period=${period}`);
  }

  // Sales Orders
  async getSalesOrders(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/sales-orders?${params.toString()}`);
  }

  async getSalesOrder(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/sales-orders/${id}${params}`);
  }

  async createSalesOrder(data: {
    customer_id: number;
    order_date: string;
    required_date?: string;
    promised_date?: string;
    payment_terms_id: number;
    currency_id?: number;
    exchange_rate?: number;
    shipping_amount?: number;
    notes?: string;
    internal_notes?: string;
    items: Array<{
      item_id: number;
      warehouse_id: number;
      description?: string;
      quantity: number;
      unit_price: number;
      discount_percentage?: number;
      tax_percentage?: number;
    }>;
  }) {
    return apiClient.post('/sales-orders', data);
  }

  async updateSalesOrder(id: number, data: any) {
    return apiClient.put(`/sales-orders/${id}`, data);
  }

  async deleteSalesOrder(id: number) {
    return apiClient.delete(`/sales-orders/${id}`);
  }

  async approveSalesOrder(id: number) {
    return apiClient.post(`/sales-orders/${id}/approve`);
  }

  async cancelSalesOrder(id: number, reason?: string) {
    return apiClient.post(`/sales-orders/${id}/cancel`, { reason });
  }

  // Delivery Orders
  async getDeliveryOrders(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/delivery-orders?${params.toString()}`);
  }

  async createDeliveryOrder(salesOrderId: number, items: any[]) {
    return apiClient.post('/delivery-orders', {
      sales_order_id: salesOrderId,
      items
    });
  }

  async deliverOrder(deliveryOrderId: number) {
    return apiClient.post(`/delivery-orders/${deliveryOrderId}/deliver`);
  }

  // Sales Invoices
  async getSalesInvoices(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/sales-invoices?${params.toString()}`);
  }

  async createInvoice(salesOrderId: number, items?: any[]) {
    return apiClient.post('/sales-invoices', {
      sales_order_id: salesOrderId,
      items
    });
  }

  async payInvoice(invoiceId: number, paymentData: any) {
    return apiClient.post(`/sales-invoices/${invoiceId}/pay`, paymentData);
  }

  // Analytics & Reports
  async getSalesAnalytics(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/sales/analytics?${params.toString()}`);
  }

  async getSalesReport(reportType: string, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/sales/reports/${reportType}?${params.toString()}`);
  }

  // Master Data
  async getPaymentTerms() {
    return apiClient.get('/payment-terms');
  }

  async getSalesTerritories() {
    return apiClient.get('/sales-territories');
  }

  async getSalesReps() {
    return apiClient.get('/sales-reps');
  }
}

export const salesService = new SalesService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/Sales/Routes/api.php

use Modules\Sales\Http\Controllers\CustomerController;
use Modules\Sales\Http\Controllers\SalesQuotationController;
use Modules\Sales\Http\Controllers\SalesOrderController;
use Modules\Sales\Http\Controllers\DeliveryOrderController;
use Modules\Sales\Http\Controllers\SalesInvoiceController;
use Modules\Sales\Http\Controllers\SalesReportController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Customers
    Route::apiResource('customers', CustomerController::class);
    Route::get('customers/{customer}/sales-history', [CustomerController::class, 'salesHistory']);
    
    // Sales Quotations
    Route::apiResource('sales-quotations', SalesQuotationController::class);
    Route::post('sales-quotations/{quotation}/convert', [SalesQuotationController::class, 'convertToOrder']);
    
    // Sales Orders
    Route::apiResource('sales-orders', SalesOrderController::class);
    Route::post('sales-orders/{salesOrder}/approve', [SalesOrderController::class, 'approve']);
    Route::post('sales-orders/{salesOrder}/cancel', [SalesOrderController::class, 'cancel']);
    
    // Delivery Orders
    Route::apiResource('delivery-orders', DeliveryOrderController::class);
    Route::post('delivery-orders/{deliveryOrder}/deliver', [DeliveryOrderController::class, 'deliver']);
    
    // Sales Invoices
    Route::apiResource('sales-invoices', SalesInvoiceController::class);
    Route::post('sales-invoices/{invoice}/pay', [SalesInvoiceController::class, 'recordPayment']);
    
    // Sales Analytics & Reports
    Route::prefix('sales')->group(function () {
        Route::get('analytics', [SalesReportController::class, 'analytics']);
        Route::get('reports/{type}', [SalesReportController::class, 'report']);
    });
    
    // Master Data
    Route::get('payment-terms', [CustomerController::class, 'paymentTerms']);
    Route::get('sales-territories', [CustomerController::class, 'territories']);
    Route::get('sales-reps', [CustomerController::class, 'salesReps']);
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Customer CRUD operations working
- [ ] Sales order workflow complete
- [ ] Inventory integration working
- [ ] Accounting integration functional
- [ ] Delivery management working
- [ ] Invoice generation accurate

### **Frontend**
- [ ] Customer management interface
- [ ] Sales order creation/approval
- [ ] Delivery tracking interface
- [ ] Invoice management
- [ ] Sales analytics dashboard
- [ ] Real-time status updates

### **Integration**
- [ ] Inventory reservation working
- [ ] Accounting entries automatic
- [ ] Credit limit checking
- [ ] Multi-currency support
- [ ] Commission calculations

## 📞 NEXT STEPS

Setelah Sales module selesai:

1. **Test sales workflow** end-to-end
2. **Verify inventory integration** accuracy
3. **Test accounting integration**
4. **Validate commission calculations**
5. **Commit module** ke repository
6. **Lanjut ke** `07_PURCHASING_MODULE.md`

---

**IMPORTANT**: Sales module adalah revenue-generating core. Pastikan semua calculations akurat dan integration dengan inventory/accounting bekerja sempurna.
