# 01 - PROJECT SETUP & ENVIRONMENT

## 📋 OVERVIEW

Modul Project Setup menyediakan foundation untuk pengembangan ERP Poultry Management System dengan setup environment yang optimal, project structure yang terorganisir, dan development tools yang diperlukan untuk productive development workflow.

## 🎯 TUJUAN

- Setup development environment yang konsisten
- Establish project structure dan coding standards
- Configure development tools dan automation
- Setup version control dan collaboration workflow
- Prepare database dan initial configurations
- Setup testing framework dan CI/CD pipeline
- Documentation dan knowledge management setup

## ⏱️ ESTIMASI WAKTU

**Total**: 12-16 jam
- Environment setup: 4-6 jam
- Project structure: 3-4 jam
- Development tools: 3-4 jam
- Documentation setup: 2-3 jam

## 👥 TIM YANG TERLIBAT

- **Project Manager** (Lead)
- **Backend Developer**
- **Frontend Developer**
- **DevOps Engineer**

## 🛠️ DEVELOPMENT ENVIRONMENT

### **System Requirements**
- **OS**: Windows 10/11, macOS, or Linux
- **PHP**: 8.1 or higher
- **Node.js**: 18.x or higher
- **Database**: MySQL 8.0 or higher
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 50GB free space

### **Required Software**
- **PHP & Composer**
- **Node.js & NPM**
- **MySQL/MariaDB**
- **Redis**
- **Git**
- **Docker** (optional but recommended)

## 🔧 STEP 1: BACKEND SETUP (LARAVEL)

### **1.1 Install Laravel & Dependencies**

```bash
# Create Laravel project
composer create-project laravel/laravel erp-poultry "10.*"
cd erp-poultry

# Install required packages
composer require laravel/sanctum
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog
composer require nwidart/laravel-modules
composer require maatwebsite/excel
composer require barryvdh/laravel-dompdf
composer require intervention/image
composer require pusher/pusher-php-server

# Development packages
composer require --dev laravel/telescope
composer require --dev barryvdh/laravel-debugbar
composer require --dev nunomaduro/collision
composer require --dev phpunit/phpunit
```

### **1.2 Environment Configuration**

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure database in .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=erp_poultry
DB_USERNAME=root
DB_PASSWORD=

# Configure Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Configure mail
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

# Configure queue
QUEUE_CONNECTION=redis

# Configure session
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Configure cache
CACHE_DRIVER=redis
```

### **1.3 Database Setup**

```bash
# Create database
mysql -u root -p
CREATE DATABASE erp_poultry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# Run migrations
php artisan migrate

# Install Passport (if using)
php artisan passport:install

# Install Telescope
php artisan telescope:install
php artisan migrate

# Publish Sanctum migration
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan migrate
```

## ⚛️ STEP 2: FRONTEND SETUP (REACT)

### **2.1 Create React Application**

```bash
# Create React app with TypeScript
npx create-react-app frontend --template typescript
cd frontend

# Install additional dependencies
npm install @reduxjs/toolkit react-redux
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material
npm install axios
npm install react-router-dom
npm install @types/react-router-dom
npm install react-hook-form
npm install @hookform/resolvers yup
npm install recharts
npm install date-fns
npm install react-query

# Development dependencies
npm install --save-dev @testing-library/react
npm install --save-dev @testing-library/jest-dom
npm install --save-dev @testing-library/user-event
```

### **2.2 Project Structure**

```
frontend/
├── public/
├── src/
│   ├── components/          # Reusable components
│   │   ├── common/         # Common UI components
│   │   ├── forms/          # Form components
│   │   └── layout/         # Layout components
│   ├── modules/            # Feature modules
│   │   ├── auth/          # Authentication
│   │   ├── dashboard/     # Dashboard
│   │   ├── inventory/     # Inventory management
│   │   └── sales/         # Sales management
│   ├── services/          # API services
│   ├── store/             # Redux store
│   ├── types/             # TypeScript types
│   ├── utils/             # Utility functions
│   ├── hooks/             # Custom hooks
│   └── constants/         # Constants
├── package.json
└── tsconfig.json
```

## 📱 STEP 3: MOBILE SETUP (REACT NATIVE)

### **3.1 Initialize React Native Project**

```bash
# Create React Native project
npx react-native init ERPPoultryMobile --template react-native-template-typescript

cd ERPPoultryMobile

# Install dependencies
npm install @react-navigation/native
npm install @react-navigation/stack
npm install @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context
npm install @reduxjs/toolkit react-redux
npm install axios
npm install react-native-vector-icons
npm install react-native-elements
npm install @react-native-async-storage/async-storage
npm install react-native-sqlite-storage

# iOS setup (if developing for iOS)
cd ios && pod install && cd ..
```

## 🐳 STEP 4: DOCKER SETUP

### **4.1 Docker Configuration**

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - REDIS_HOST=redis

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: erp_poultry
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api

volumes:
  mysql_data:
```

### **4.2 Dockerfile for Laravel**

```dockerfile
# Dockerfile
FROM php:8.1-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy existing application directory contents
COPY . /var/www/html

# Install dependencies
RUN composer install

# Expose port 8000 and start php-fpm server
EXPOSE 8000
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8000"]
```

## 🔧 STEP 5: DEVELOPMENT TOOLS

### **5.1 Code Quality Tools**

```bash
# PHP Code Sniffer
composer require --dev squizlabs/php_codesniffer

# PHP CS Fixer
composer require --dev friendsofphp/php-cs-fixer

# PHPStan
composer require --dev phpstan/phpstan

# Larastan
composer require --dev nunomaduro/larastan
```

### **5.2 Git Hooks Setup**

```bash
# Install pre-commit hooks
npm install --save-dev husky lint-staged

# Add to package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.php": [
      "php-cs-fixer fix",
      "git add"
    ],
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "git add"
    ]
  }
}
```

## 📚 STEP 6: DOCUMENTATION SETUP

### **6.1 API Documentation**

```bash
# Install Swagger/OpenAPI
composer require darkaonline/l5-swagger

# Publish config
php artisan vendor:publish --provider "L5Swagger\L5SwaggerServiceProvider"

# Generate documentation
php artisan l5-swagger:generate
```

### **6.2 Project Documentation Structure**

```
docs/
├── api/                    # API documentation
├── deployment/             # Deployment guides
├── development/            # Development guides
├── user-manual/           # User manuals
└── architecture/          # System architecture
```

## ✅ VERIFICATION CHECKLIST

### **Backend Setup**
- [ ] Laravel 10 installed and configured
- [ ] Database connection working
- [ ] Redis connection working
- [ ] Required packages installed
- [ ] Telescope accessible
- [ ] API routes responding

### **Frontend Setup**
- [ ] React app created and running
- [ ] TypeScript configured
- [ ] Required packages installed
- [ ] API integration working
- [ ] Routing configured

### **Mobile Setup**
- [ ] React Native project created
- [ ] Dependencies installed
- [ ] Android/iOS build working
- [ ] Navigation configured

### **Development Tools**
- [ ] Docker containers running
- [ ] Code quality tools configured
- [ ] Git hooks working
- [ ] Documentation accessible

### **Project Structure**
- [ ] Modular architecture implemented
- [ ] Coding standards defined
- [ ] Environment variables configured
- [ ] Testing framework ready

## 📞 NEXT STEPS

Setelah Project Setup selesai:

1. **Verify all environments** working properly
2. **Setup team access** dan permissions
3. **Create initial modules** structure
4. **Setup CI/CD pipeline**
5. **Begin development** dengan Module 02
6. **Regular team sync** untuk ensure consistency

---

**IMPORTANT**: Project setup adalah foundation yang critical. Pastikan semua environments consistent dan development tools properly configured sebelum mulai development modules.
