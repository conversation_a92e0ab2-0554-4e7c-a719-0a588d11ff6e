# 13 - MO<PERSON>LE APP MODULE

## 📋 OVERVIEW

Modul Mobile App menyediakan aplikasi mobile native untuk field operations, memungkinkan farm workers dan managers untuk mengakses dan input data secara real-time di lapangan. App ini menggunakan React Native untuk cross-platform compatibility dan offline-first architecture.

## 🎯 TUJUAN

- Mobile app untuk daily operations di lapangan
- Offline-first architecture dengan sync capabilities
- Real-time data collection dan monitoring
- Push notifications untuk alerts dan reminders
- GPS tracking untuk location-based operations
- Camera integration untuk photo documentation
- Barcode/QR code scanning untuk inventory
- Voice-to-text untuk quick data entry

## ⏱️ ESTIMASI WAKTU

**Total**: 24-28 jam
- React Native setup: 4-6 jam
- Core features implementation: 12-16 jam
- Offline sync implementation: 4-6 jam
- Testing & optimization: 4-6 jam

## 👥 TIM YANG TERLIBAT

- **Mobile Developer** (Lead)
- **Backend Developer**
- **UI/UX Designer**

## 🏗️ MOBILE APP ARCHITECTURE

```
┌─────────────────────────────────────────────────────────────┐
│                    MOBILE APP ARCHITECTURE                  │
├─────────────────────────────────────────────────────────────┤
│  React Native App                                          │
│  ├── Authentication (Biometric + PIN)                      │
│  ├── Offline Storage (SQLite + AsyncStorage)               │
│  ├── Background Sync (Redux Persist + Queue)               │
│  └── Push Notifications (Firebase)                         │
├─────────────────────────────────────────────────────────────┤
│  Core Features                                             │
│  ├── Daily Production Recording                            │
│  ├── Feed Consumption Tracking                             │
│  ├── Health Monitoring                                     │
│  ├── Inventory Management                                  │
│  └── Task Management                                       │
├─────────────────────────────────────────────────────────────┤
│  Device Integration                                        │
│  ├── Camera (Photo Documentation)                          │
│  ├── GPS (Location Tracking)                               │
│  ├── Barcode Scanner                                       │
│  ├── Voice Recognition                                     │
│  └── Biometric Authentication                              │
├─────────────────────────────────────────────────────────────┤
│  Backend Integration                                       │
│  ├── REST API Client                                       │
│  ├── WebSocket (Real-time Updates)                         │
│  ├── File Upload (Photos/Documents)                        │
│  └── Sync Queue Management                                 │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 STEP 1: REACT NATIVE SETUP

### **1.1 Initialize React Native Project**

```bash
# Create React Native project
npx react-native init ERPPoultryMobile --template react-native-template-typescript

cd ERPPoultryMobile

# Install essential dependencies
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context
npm install @reduxjs/toolkit react-redux redux-persist
npm install react-native-sqlite-storage @react-native-async-storage/async-storage
npm install react-native-camera react-native-permissions
npm install react-native-barcode-scanner-google react-native-qrcode-scanner
npm install @react-native-firebase/app @react-native-firebase/messaging
npm install react-native-geolocation-service
npm install react-native-voice react-native-tts
npm install react-native-biometrics react-native-keychain
npm install axios react-native-network-info
npm install react-native-vector-icons react-native-elements
npm install react-native-chart-kit react-native-svg
npm install react-native-date-picker react-native-modal

# iOS specific
cd ios && pod install && cd ..
```

### **1.2 Project Structure**

```
ERPPoultryMobile/
├── src/
│   ├── components/           # Reusable components
│   ├── screens/             # Screen components
│   ├── navigation/          # Navigation configuration
│   ├── services/            # API and business logic
│   ├── store/               # Redux store and slices
│   ├── utils/               # Utility functions
│   ├── types/               # TypeScript type definitions
│   ├── hooks/               # Custom React hooks
│   ├── constants/           # App constants
│   └── assets/              # Images, fonts, etc.
├── android/                 # Android specific code
├── ios/                     # iOS specific code
└── package.json
```

### **1.3 Core Configuration**

```typescript
// src/store/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { combineReducers } from 'redux';

import authSlice from './slices/authSlice';
import productionSlice from './slices/productionSlice';
import inventorySlice from './slices/inventorySlice';
import syncSlice from './slices/syncSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth', 'production', 'inventory', 'sync'],
};

const rootReducer = combineReducers({
  auth: authSlice,
  production: productionSlice,
  inventory: inventorySlice,
  sync: syncSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

## 📱 STEP 2: CORE FEATURES IMPLEMENTATION

### **2.1 Authentication Screen**

```typescript
// src/screens/AuthScreen.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
} from 'react-native';
import { useDispatch } from 'react-redux';
import ReactNativeBiometrics from 'react-native-biometrics';
import { login, setBiometricEnabled } from '../store/slices/authSlice';
import { authService } from '../services/authService';

const AuthScreen: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const { available } = await ReactNativeBiometrics.isSensorAvailable();
      setBiometricAvailable(available);
    } catch (error) {
      console.log('Biometric check error:', error);
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter email and password');
      return;
    }

    setLoading(true);
    try {
      const response = await authService.login(email, password);
      dispatch(login(response.data));
      
      if (biometricAvailable) {
        Alert.alert(
          'Enable Biometric Login',
          'Would you like to enable biometric authentication for faster login?',
          [
            { text: 'No', style: 'cancel' },
            { text: 'Yes', onPress: enableBiometric },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Login Failed', 'Invalid credentials');
    } finally {
      setLoading(false);
    }
  };

  const enableBiometric = async () => {
    try {
      const { success } = await ReactNativeBiometrics.simplePrompt({
        promptMessage: 'Confirm your identity',
      });
      
      if (success) {
        dispatch(setBiometricEnabled(true));
      }
    } catch (error) {
      console.log('Biometric setup error:', error);
    }
  };

  const handleBiometricLogin = async () => {
    try {
      const { success } = await ReactNativeBiometrics.simplePrompt({
        promptMessage: 'Authenticate to login',
      });
      
      if (success) {
        // Use stored credentials for biometric login
        const storedCredentials = await authService.getStoredCredentials();
        if (storedCredentials) {
          const response = await authService.loginWithToken(storedCredentials.token);
          dispatch(login(response.data));
        }
      }
    } catch (error) {
      Alert.alert('Authentication Failed', 'Please try again');
    }
  };

  return (
    <View style={styles.container}>
      <Image source={require('../assets/logo.png')} style={styles.logo} />
      <Text style={styles.title}>ERP Poultry Mobile</Text>
      
      <TextInput
        style={styles.input}
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      
      <TextInput
        style={styles.input}
        placeholder="Password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />
      
      <TouchableOpacity
        style={styles.loginButton}
        onPress={handleLogin}
        disabled={loading}
      >
        <Text style={styles.loginButtonText}>
          {loading ? 'Logging in...' : 'Login'}
        </Text>
      </TouchableOpacity>
      
      {biometricAvailable && (
        <TouchableOpacity
          style={styles.biometricButton}
          onPress={handleBiometricLogin}
        >
          <Text style={styles.biometricButtonText}>Use Biometric Login</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#333',
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    marginBottom: 15,
    backgroundColor: 'white',
  },
  loginButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#007bff',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  biometricButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#28a745',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  biometricButtonText: {
    color: 'white',
    fontSize: 16,
  },
});

export default AuthScreen;
```

### **2.2 Production Recording Screen**

```typescript
// src/screens/ProductionRecordScreen.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import DatePicker from 'react-native-date-picker';
import { Picker } from '@react-native-picker/picker';
import { Camera } from 'react-native-camera';
import Voice from '@react-native-voice/voice';

import { addProductionRecord } from '../store/slices/productionSlice';
import { productionService } from '../services/productionService';
import { RootState } from '../store/store';

const ProductionRecordScreen: React.FC = () => {
  const [recordDate, setRecordDate] = useState(new Date());
  const [selectedFlock, setSelectedFlock] = useState('');
  const [selectedHouse, setSelectedHouse] = useState('');
  const [eggsCollected, setEggsCollected] = useState('');
  const [mortalityCount, setMortalityCount] = useState('');
  const [feedConsumed, setFeedConsumed] = useState('');
  const [waterConsumed, setWaterConsumed] = useState('');
  const [temperature, setTemperature] = useState('');
  const [humidity, setHumidity] = useState('');
  const [notes, setNotes] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const dispatch = useDispatch();
  const { flocks, houses } = useSelector((state: RootState) => state.production);
  const { isOnline } = useSelector((state: RootState) => state.sync);

  useEffect(() => {
    Voice.onSpeechResults = onSpeechResults;
    return () => {
      Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  const onSpeechResults = (event: any) => {
    const spokenText = event.value[0];
    setNotes(notes + ' ' + spokenText);
    setIsListening(false);
  };

  const startListening = async () => {
    try {
      setIsListening(true);
      await Voice.start('en-US');
    } catch (error) {
      console.log('Voice recognition error:', error);
      setIsListening(false);
    }
  };

  const stopListening = async () => {
    try {
      await Voice.stop();
      setIsListening(false);
    } catch (error) {
      console.log('Voice stop error:', error);
    }
  };

  const handleSubmit = async () => {
    if (!selectedFlock || !selectedHouse || !eggsCollected) {
      Alert.alert('Error', 'Please fill in required fields');
      return;
    }

    const recordData = {
      record_date: recordDate.toISOString().split('T')[0],
      flock_id: parseInt(selectedFlock),
      house_id: parseInt(selectedHouse),
      eggs_collected: parseInt(eggsCollected),
      mortality_count: parseInt(mortalityCount) || 0,
      feed_consumed_kg: parseFloat(feedConsumed) || 0,
      water_consumed_liters: parseFloat(waterConsumed) || 0,
      temperature_min: parseFloat(temperature) || null,
      humidity_percentage: parseFloat(humidity) || null,
      notes,
    };

    try {
      if (isOnline) {
        await productionService.createProductionRecord(recordData);
        Alert.alert('Success', 'Production record saved successfully');
      } else {
        dispatch(addProductionRecord({ ...recordData, synced: false }));
        Alert.alert('Saved Offline', 'Record will be synced when online');
      }
      
      // Reset form
      resetForm();
    } catch (error) {
      Alert.alert('Error', 'Failed to save production record');
    }
  };

  const resetForm = () => {
    setEggsCollected('');
    setMortalityCount('');
    setFeedConsumed('');
    setWaterConsumed('');
    setTemperature('');
    setHumidity('');
    setNotes('');
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Daily Production Record</Text>
      
      <View style={styles.formGroup}>
        <Text style={styles.label}>Date *</Text>
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowDatePicker(true)}
        >
          <Text>{recordDate.toDateString()}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Flock *</Text>
        <Picker
          selectedValue={selectedFlock}
          onValueChange={setSelectedFlock}
          style={styles.picker}
        >
          <Picker.Item label="Select Flock" value="" />
          {flocks.map((flock) => (
            <Picker.Item
              key={flock.id}
              label={flock.flock_number}
              value={flock.id.toString()}
            />
          ))}
        </Picker>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>House *</Text>
        <Picker
          selectedValue={selectedHouse}
          onValueChange={setSelectedHouse}
          style={styles.picker}
        >
          <Picker.Item label="Select House" value="" />
          {houses.map((house) => (
            <Picker.Item
              key={house.id}
              label={house.name}
              value={house.id.toString()}
            />
          ))}
        </Picker>
      </View>

      <View style={styles.row}>
        <View style={styles.halfWidth}>
          <Text style={styles.label}>Eggs Collected *</Text>
          <TextInput
            style={styles.input}
            value={eggsCollected}
            onChangeText={setEggsCollected}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>
        
        <View style={styles.halfWidth}>
          <Text style={styles.label}>Mortality</Text>
          <TextInput
            style={styles.input}
            value={mortalityCount}
            onChangeText={setMortalityCount}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={styles.halfWidth}>
          <Text style={styles.label}>Feed Consumed (kg)</Text>
          <TextInput
            style={styles.input}
            value={feedConsumed}
            onChangeText={setFeedConsumed}
            keyboardType="numeric"
            placeholder="0.0"
          />
        </View>
        
        <View style={styles.halfWidth}>
          <Text style={styles.label}>Water Consumed (L)</Text>
          <TextInput
            style={styles.input}
            value={waterConsumed}
            onChangeText={setWaterConsumed}
            keyboardType="numeric"
            placeholder="0.0"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={styles.halfWidth}>
          <Text style={styles.label}>Temperature (°C)</Text>
          <TextInput
            style={styles.input}
            value={temperature}
            onChangeText={setTemperature}
            keyboardType="numeric"
            placeholder="0.0"
          />
        </View>
        
        <View style={styles.halfWidth}>
          <Text style={styles.label}>Humidity (%)</Text>
          <TextInput
            style={styles.input}
            value={humidity}
            onChangeText={setHumidity}
            keyboardType="numeric"
            placeholder="0.0"
          />
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Notes</Text>
        <View style={styles.notesContainer}>
          <TextInput
            style={styles.notesInput}
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={4}
            placeholder="Add notes..."
          />
          <TouchableOpacity
            style={styles.voiceButton}
            onPress={isListening ? stopListening : startListening}
          >
            <Text style={styles.voiceButtonText}>
              {isListening ? '🛑' : '🎤'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
        <Text style={styles.submitButtonText}>Save Record</Text>
      </TouchableOpacity>

      <DatePicker
        modal
        open={showDatePicker}
        date={recordDate}
        mode="date"
        onConfirm={(date) => {
          setShowDatePicker(false);
          setRecordDate(date);
        }}
        onCancel={() => setShowDatePicker(false)}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
    color: '#333',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    backgroundColor: 'white',
    fontSize: 16,
  },
  picker: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: 'white',
  },
  dateButton: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    backgroundColor: 'white',
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  halfWidth: {
    width: '48%',
  },
  notesContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  notesInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: 'white',
    fontSize: 16,
    textAlignVertical: 'top',
  },
  voiceButton: {
    marginLeft: 10,
    width: 50,
    height: 50,
    backgroundColor: '#007bff',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButtonText: {
    fontSize: 20,
  },
  submitButton: {
    height: 50,
    backgroundColor: '#28a745',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default ProductionRecordScreen;
```

### **2.3 Offline Sync Service**

```typescript
// src/services/syncService.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { store } from '../store/store';
import { setSyncStatus, addSyncQueue, removeSyncQueue } from '../store/slices/syncSlice';
import { productionService } from './productionService';
import { inventoryService } from './inventoryService';

interface SyncQueueItem {
  id: string;
  type: 'production' | 'inventory' | 'health' | 'feed';
  action: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount: number;
}

class SyncService {
  private syncQueue: SyncQueueItem[] = [];
  private isOnline = false;
  private isSyncing = false;

  constructor() {
    this.initializeNetworkListener();
    this.loadSyncQueue();
  }

  private initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      store.dispatch(setSyncStatus({
        isOnline: this.isOnline,
        lastSyncTime: Date.now(),
      }));

      // Start sync when coming back online
      if (!wasOnline && this.isOnline && this.syncQueue.length > 0) {
        this.startSync();
      }
    });
  }

  private async loadSyncQueue() {
    try {
      const queueData = await AsyncStorage.getItem('syncQueue');
      if (queueData) {
        this.syncQueue = JSON.parse(queueData);
      }
    } catch (error) {
      console.log('Error loading sync queue:', error);
    }
  }

  private async saveSyncQueue() {
    try {
      await AsyncStorage.setItem('syncQueue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.log('Error saving sync queue:', error);
    }
  }

  public async addToSyncQueue(item: Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>) {
    const queueItem: SyncQueueItem = {
      ...item,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.syncQueue.push(queueItem);
    await this.saveSyncQueue();
    
    store.dispatch(addSyncQueue(queueItem));

    // Try to sync immediately if online
    if (this.isOnline && !this.isSyncing) {
      this.startSync();
    }
  }

  public async startSync() {
    if (this.isSyncing || !this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    this.isSyncing = true;
    console.log(`Starting sync with ${this.syncQueue.length} items`);

    const itemsToSync = [...this.syncQueue];
    
    for (const item of itemsToSync) {
      try {
        await this.syncItem(item);
        
        // Remove from queue on success
        this.syncQueue = this.syncQueue.filter(q => q.id !== item.id);
        store.dispatch(removeSyncQueue(item.id));
        
      } catch (error) {
        console.log(`Sync failed for item ${item.id}:`, error);
        
        // Increment retry count
        const queueItem = this.syncQueue.find(q => q.id === item.id);
        if (queueItem) {
          queueItem.retryCount++;
          
          // Remove from queue if max retries reached
          if (queueItem.retryCount >= 3) {
            this.syncQueue = this.syncQueue.filter(q => q.id !== item.id);
            store.dispatch(removeSyncQueue(item.id));
            console.log(`Max retries reached for item ${item.id}, removing from queue`);
          }
        }
      }
    }

    await this.saveSyncQueue();
    this.isSyncing = false;
    
    console.log(`Sync completed. ${this.syncQueue.length} items remaining`);
  }

  private async syncItem(item: SyncQueueItem): Promise<void> {
    switch (item.type) {
      case 'production':
        return this.syncProductionItem(item);
      case 'inventory':
        return this.syncInventoryItem(item);
      case 'health':
        return this.syncHealthItem(item);
      case 'feed':
        return this.syncFeedItem(item);
      default:
        throw new Error(`Unknown sync type: ${item.type}`);
    }
  }

  private async syncProductionItem(item: SyncQueueItem): Promise<void> {
    switch (item.action) {
      case 'create':
        await productionService.createProductionRecord(item.data);
        break;
      case 'update':
        await productionService.updateProductionRecord(item.data.id, item.data);
        break;
      case 'delete':
        await productionService.deleteProductionRecord(item.data.id);
        break;
    }
  }

  private async syncInventoryItem(item: SyncQueueItem): Promise<void> {
    switch (item.action) {
      case 'create':
        await inventoryService.createInventoryMovement(item.data);
        break;
      case 'update':
        await inventoryService.updateInventoryMovement(item.data.id, item.data);
        break;
    }
  }

  private async syncHealthItem(item: SyncQueueItem): Promise<void> {
    // Implement health sync logic
  }

  private async syncFeedItem(item: SyncQueueItem): Promise<void> {
    // Implement feed sync logic
  }

  public getSyncQueueStatus() {
    return {
      totalItems: this.syncQueue.length,
      pendingItems: this.syncQueue.filter(item => item.retryCount === 0).length,
      failedItems: this.syncQueue.filter(item => item.retryCount > 0).length,
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
    };
  }

  public async forceSyncAll() {
    if (this.isOnline) {
      await this.startSync();
    } else {
      throw new Error('Cannot sync while offline');
    }
  }

  public clearSyncQueue() {
    this.syncQueue = [];
    this.saveSyncQueue();
  }
}

export const syncService = new SyncService();
```

## 🔗 STEP 3: NAVIGATION SETUP

### **3.1 Navigation Configuration**

```typescript
// src/navigation/AppNavigator.tsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';

import AuthScreen from '../screens/AuthScreen';
import DashboardScreen from '../screens/DashboardScreen';
import ProductionRecordScreen from '../screens/ProductionRecordScreen';
import InventoryScreen from '../screens/InventoryScreen';
import HealthScreen from '../screens/HealthScreen';
import SyncScreen from '../screens/SyncScreen';
import { RootState } from '../store/store';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Production':
              iconName = 'assignment';
              break;
            case 'Inventory':
              iconName = 'inventory';
              break;
            case 'Health':
              iconName = 'local-hospital';
              break;
            case 'Sync':
              iconName = 'sync';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007bff',
        tabBarInactiveTintColor: 'gray',
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Production" component={ProductionRecordScreen} />
      <Tab.Screen name="Inventory" component={InventoryScreen} />
      <Tab.Screen name="Health" component={HealthScreen} />
      <Tab.Screen name="Sync" component={SyncScreen} />
    </Tab.Navigator>
  );
};

const AppNavigator: React.FC = () => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <Stack.Screen name="Main" component={MainTabs} />
        ) : (
          <Stack.Screen name="Auth" component={AuthScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
```

## ✅ VERIFICATION CHECKLIST

### **Mobile App**
- [ ] React Native setup complete
- [ ] Authentication working (including biometric)
- [ ] Production recording functional
- [ ] Offline storage working
- [ ] Sync mechanism functional
- [ ] Camera integration working
- [ ] Voice recognition working

### **Features**
- [ ] Daily production recording
- [ ] Inventory management
- [ ] Health monitoring
- [ ] Feed consumption tracking
- [ ] Photo documentation
- [ ] GPS location tracking

### **Integration**
- [ ] API integration working
- [ ] Real-time sync functional
- [ ] Push notifications working
- [ ] Offline-first architecture
- [ ] Data consistency maintained

## 📞 NEXT STEPS

Setelah Mobile App module selesai:

1. **Test app functionality** pada device
2. **Verify offline sync** mechanism
3. **Test push notifications**
4. **Validate data consistency**
5. **Commit module** ke repository
6. **Lanjut ke** `14_INTEGRATION_TESTING.md`

---

**IMPORTANT**: Mobile App adalah field operations tool yang critical. Pastikan offline functionality bekerja sempurna dan sync mechanism reliable untuk data integrity di lapangan.
