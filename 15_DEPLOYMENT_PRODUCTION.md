# 15 - DEPLOYMENT & PRODUCTION

## 📋 OVERVIEW

Modul Deployment & Production menyediakan comprehensive deployment strategy untuk production environment dengan focus pada scalability, security, monitoring, dan maintenance. Deployment menggunakan containerization dengan Docker dan orchestration dengan Kubernetes.

## 🎯 TUJUAN

- Production-ready deployment dengan high availability
- Containerized architecture dengan Docker & Kubernetes
- Automated CI/CD pipeline dengan zero-downtime deployment
- Comprehensive monitoring dan logging
- Security hardening dan compliance
- Backup & disaster recovery strategy
- Performance optimization dan scaling
- Maintenance procedures dan troubleshooting

## ⏱️ ESTIMASI WAKTU

**Total**: 28-32 jam
- Infrastructure setup: 8-10 jam
- Containerization: 6-8 jam
- CI/CD pipeline: 6-8 jam
- Monitoring & logging: 4-6 jam
- Security & backup: 4-6 jam

## 👥 TIM YANG TERLIBAT

- **DevOps Engineer** (Lead)
- **Backend Developer**
- **Frontend Developer**
- **Security Engineer**

## 🏗️ PRODUCTION ARCHITECTURE

```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUCTION ARCHITECTURE                  │
├─────────────────────────────────────────────────────────────┤
│  Load Balancer (Nginx/HAProxy)                            │
│  ├── SSL Termination                                       │
│  ├── Rate Limiting                                         │
│  └── Health Checks                                         │
├─────────────────────────────────────────────────────────────┤
│  Kubernetes Cluster                                       │
│  ├── Frontend Pods (React App)                            │
│  ├── Backend Pods (Laravel API)                           │
│  ├── Mobile API Gateway                                   │
│  └── Background Workers                                   │
├─────────────────────────────────────────────────────────────┤
│  Databases                                                │
│  ├── MySQL Master/Slave                                   │
│  ├── Redis Cache                                          │
│  └── Elasticsearch (Logging)                              │
├─────────────────────────────────────────────────────────────┤
│  Storage                                                  │
│  ├── File Storage (S3/MinIO)                              │
│  ├── Backup Storage                                       │
│  └── Log Storage                                          │
├─────────────────────────────────────────────────────────────┤
│  Monitoring & Observability                              │
│  ├── Prometheus (Metrics)                                 │
│  ├── Grafana (Dashboards)                                 │
│  ├── ELK Stack (Logging)                                  │
│  └── Jaeger (Tracing)                                     │
└─────────────────────────────────────────────────────────────┘
```

## 🐳 STEP 1: CONTAINERIZATION

### **1.1 Backend Dockerfile**

```dockerfile
# Dockerfile.backend
FROM php:8.1-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    mysql-client \
    nginx \
    supervisor

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create application directory
WORKDIR /var/www/html

# Copy composer files
COPY composer.json composer.lock ./

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Copy application code
COPY . .

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# Copy configuration files
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY docker/php/php.ini /usr/local/etc/php/php.ini
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose port
EXPOSE 80

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

### **1.2 Frontend Dockerfile**

```dockerfile
# Dockerfile.frontend
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### **1.3 Docker Compose for Development**

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:80"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_DATABASE=erp_poultry
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    volumes:
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
    depends_on:
      - mysql
      - redis
    networks:
      - erp-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
    networks:
      - erp-network

  # Mobile API Gateway
  mobile-gateway:
    build:
      context: .
      dockerfile: Dockerfile.mobile-gateway
    ports:
      - "8001:80"
    environment:
      - APP_ENV=production
      - MOBILE_API=true
    depends_on:
      - backend
    networks:
      - erp-network

  # Database
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=erp_poultry
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - erp-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - erp-network

  # Queue Worker
  queue-worker:
    build:
      context: .
      dockerfile: Dockerfile.backend
    command: php artisan queue:work --sleep=3 --tries=3
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - mysql
      - redis
    networks:
      - erp-network

  # Scheduler
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.backend
    command: php artisan schedule:work
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - erp-network

volumes:
  mysql_data:
  redis_data:

networks:
  erp-network:
    driver: bridge
```

## ☸️ STEP 2: KUBERNETES DEPLOYMENT

### **2.1 Kubernetes Manifests**

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: erp-poultry
  labels:
    name: erp-poultry

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: erp-config
  namespace: erp-poultry
data:
  APP_ENV: "production"
  APP_DEBUG: "false"
  DB_HOST: "mysql-service"
  DB_DATABASE: "erp_poultry"
  REDIS_HOST: "redis-service"
  CACHE_DRIVER: "redis"
  SESSION_DRIVER: "redis"
  QUEUE_CONNECTION: "redis"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: erp-secrets
  namespace: erp-poultry
type: Opaque
data:
  APP_KEY: <base64-encoded-app-key>
  DB_PASSWORD: <base64-encoded-db-password>
  JWT_SECRET: <base64-encoded-jwt-secret>

---
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: erp-poultry
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: erp-poultry/backend:latest
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: erp-config
        - secretRef:
            name: erp-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
# k8s/backend-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: erp-poultry
spec:
  selector:
    app: backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP

---
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-deployment
  namespace: erp-poultry
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: erp-poultry/frontend:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

---
# k8s/frontend-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: erp-poultry
spec:
  selector:
    app: frontend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: erp-ingress
  namespace: erp-poultry
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - erp-poultry.com
    - api.erp-poultry.com
    secretName: erp-tls-secret
  rules:
  - host: erp-poultry.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  - host: api.erp-poultry.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 80
```

### **2.2 Database Deployment**

```yaml
# k8s/mysql-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql
  namespace: erp-poultry
spec:
  serviceName: mysql-service
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: erp-secrets
              key: DB_PASSWORD
        - name: MYSQL_DATABASE
          value: erp_poultry
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
  volumeClaimTemplates:
  - metadata:
      name: mysql-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi

---
# k8s/mysql-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: erp-poultry
spec:
  selector:
    app: mysql
  ports:
  - protocol: TCP
    port: 3306
    targetPort: 3306
  type: ClusterIP

---
# k8s/redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: erp-poultry
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
# k8s/redis-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: erp-poultry
spec:
  selector:
    app: redis
  ports:
  - protocol: TCP
    port: 6379
    targetPort: 6379
  type: ClusterIP
```

## 🚀 STEP 3: CI/CD PIPELINE

### **3.1 GitHub Actions Deployment**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Tests
      run: |
        # Run all tests before deployment
        composer install
        php artisan test
        cd frontend && npm ci && npm test

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        component: [backend, frontend, mobile-gateway]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.${{ matrix.component }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Configure kubectl
      uses: azure/k8s-set-context@v1
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG }}

    - name: Deploy to Kubernetes
      run: |
        # Update image tags in manifests
        sed -i "s|image: erp-poultry/backend:latest|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:${{ github.sha }}|g" k8s/backend-deployment.yaml
        sed -i "s|image: erp-poultry/frontend:latest|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }}|g" k8s/frontend-deployment.yaml
        
        # Apply manifests
        kubectl apply -f k8s/
        
        # Wait for rollout
        kubectl rollout status deployment/backend-deployment -n erp-poultry
        kubectl rollout status deployment/frontend-deployment -n erp-poultry

    - name: Run Database Migrations
      run: |
        kubectl exec -n erp-poultry deployment/backend-deployment -- php artisan migrate --force

    - name: Verify Deployment
      run: |
        kubectl get pods -n erp-poultry
        kubectl get services -n erp-poultry
        kubectl get ingress -n erp-poultry

  notify:
    needs: deploy
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### **3.2 Deployment Scripts**

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# Configuration
NAMESPACE="erp-poultry"
REGISTRY="ghcr.io/your-org/erp-poultry"
VERSION=${1:-latest}

echo "🚀 Starting deployment of ERP Poultry v${VERSION}"

# Check if kubectl is configured
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ kubectl is not configured or cluster is not accessible"
    exit 1
fi

# Create namespace if it doesn't exist
kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations
echo "📝 Applying configurations..."
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# Deploy databases first
echo "🗄️ Deploying databases..."
kubectl apply -f k8s/mysql-deployment.yaml
kubectl apply -f k8s/redis-deployment.yaml

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
kubectl wait --for=condition=ready pod -l app=mysql -n ${NAMESPACE} --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n ${NAMESPACE} --timeout=300s

# Deploy applications
echo "🏗️ Deploying applications..."
kubectl apply -f k8s/backend-deployment.yaml
kubectl apply -f k8s/frontend-deployment.yaml

# Wait for applications to be ready
echo "⏳ Waiting for applications to be ready..."
kubectl wait --for=condition=ready pod -l app=backend -n ${NAMESPACE} --timeout=300s
kubectl wait --for=condition=ready pod -l app=frontend -n ${NAMESPACE} --timeout=300s

# Apply services and ingress
echo "🌐 Applying services and ingress..."
kubectl apply -f k8s/backend-service.yaml
kubectl apply -f k8s/frontend-service.yaml
kubectl apply -f k8s/ingress.yaml

# Run database migrations
echo "🔄 Running database migrations..."
kubectl exec -n ${NAMESPACE} deployment/backend-deployment -- php artisan migrate --force

# Verify deployment
echo "✅ Verifying deployment..."
kubectl get pods -n ${NAMESPACE}
kubectl get services -n ${NAMESPACE}
kubectl get ingress -n ${NAMESPACE}

echo "🎉 Deployment completed successfully!"
echo "📊 Access the application at: https://erp-poultry.com"
echo "🔧 API endpoint: https://api.erp-poultry.com"
```

## 📊 STEP 4: MONITORING & LOGGING

### **4.1 Prometheus Configuration**

```yaml
# monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "alert_rules.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)

      - job_name: 'erp-backend'
        static_configs:
          - targets: ['backend-service.erp-poultry:80']
        metrics_path: /metrics

      - job_name: 'mysql'
        static_configs:
          - targets: ['mysql-exporter:9104']

      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']

  alert_rules.yml: |
    groups:
    - name: erp-poultry-alerts
      rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected
          description: "Error rate is {{ $value }} errors per second"

      - alert: DatabaseDown
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: MySQL database is down
          description: "MySQL database has been down for more than 1 minute"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High memory usage
          description: "Memory usage is above 90%"
```

### **4.2 Grafana Dashboards**

```json
{
  "dashboard": {
    "id": null,
    "title": "ERP Poultry System Dashboard",
    "tags": ["erp", "poultry"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ],
        "yAxes": [
          {
            "label": "Requests/sec"
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "id": 3,
        "title": "Database Connections",
        "type": "singlestat",
        "targets": [
          {
            "expr": "mysql_global_status_threads_connected",
            "legendFormat": "Active Connections"
          }
        ]
      },
      {
        "id": 4,
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "container_memory_usage_bytes{pod=~\"backend-.*\"} / container_spec_memory_limit_bytes",
            "legendFormat": "{{pod}}"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

## 🔒 STEP 5: SECURITY & BACKUP

### **5.1 Security Configuration**

```yaml
# security/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: erp-network-policy
  namespace: erp-poultry
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 80
  - from:
    - podSelector:
        matchLabels:
          app: backend
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 80
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: mysql
    ports:
    - protocol: TCP
      port: 3306
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# security/pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: erp-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

### **5.2 Backup Strategy**

```bash
#!/bin/bash
# scripts/backup.sh

set -e

NAMESPACE="erp-poultry"
BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
S3_BUCKET="erp-poultry-backups"

echo "🔄 Starting backup process..."

# Create backup directory
mkdir -p ${BACKUP_DIR}

# Database backup
echo "📊 Backing up MySQL database..."
kubectl exec -n ${NAMESPACE} deployment/mysql -- mysqldump \
  --single-transaction \
  --routines \
  --triggers \
  --all-databases \
  -u root -p${MYSQL_ROOT_PASSWORD} > ${BACKUP_DIR}/mysql_backup.sql

# Redis backup
echo "💾 Backing up Redis data..."
kubectl exec -n ${NAMESPACE} deployment/redis -- redis-cli BGSAVE
kubectl cp ${NAMESPACE}/redis-pod:/data/dump.rdb ${BACKUP_DIR}/redis_backup.rdb

# Application files backup
echo "📁 Backing up application files..."
kubectl exec -n ${NAMESPACE} deployment/backend-deployment -- tar -czf - /var/www/html/storage > ${BACKUP_DIR}/storage_backup.tar.gz

# Kubernetes manifests backup
echo "⚙️ Backing up Kubernetes manifests..."
kubectl get all -n ${NAMESPACE} -o yaml > ${BACKUP_DIR}/k8s_manifests.yaml

# Compress backup
echo "🗜️ Compressing backup..."
tar -czf ${BACKUP_DIR}.tar.gz -C $(dirname ${BACKUP_DIR}) $(basename ${BACKUP_DIR})

# Upload to S3
echo "☁️ Uploading to S3..."
aws s3 cp ${BACKUP_DIR}.tar.gz s3://${S3_BUCKET}/

# Cleanup local backup
rm -rf ${BACKUP_DIR} ${BACKUP_DIR}.tar.gz

echo "✅ Backup completed successfully!"
```

## ✅ VERIFICATION CHECKLIST

### **Infrastructure**
- [ ] Kubernetes cluster setup
- [ ] Container registry configured
- [ ] Load balancer configured
- [ ] SSL certificates installed
- [ ] DNS records configured

### **Application Deployment**
- [ ] All services deployed
- [ ] Health checks working
- [ ] Database migrations completed
- [ ] File storage configured
- [ ] Cache working properly

### **Monitoring & Logging**
- [ ] Prometheus collecting metrics
- [ ] Grafana dashboards working
- [ ] Log aggregation setup
- [ ] Alerting configured
- [ ] Performance monitoring active

### **Security**
- [ ] Network policies applied
- [ ] RBAC configured
- [ ] Secrets management setup
- [ ] Security scanning completed
- [ ] Backup strategy implemented

### **CI/CD**
- [ ] Automated deployment pipeline
- [ ] Testing integration
- [ ] Rollback procedures
- [ ] Environment promotion
- [ ] Notification system

## 📞 NEXT STEPS

Setelah Deployment & Production selesai:

1. **Verify production deployment**
2. **Test all functionalities** end-to-end
3. **Monitor system performance**
4. **Validate backup procedures**
5. **Document operational procedures**
6. **Train operations team**
7. **Go live** dengan production system

---

**IMPORTANT**: Production deployment adalah critical milestone. Pastikan semua systems reliable, secure, dan well-monitored sebelum go-live. Siapkan rollback plan dan 24/7 monitoring untuk production stability.
