{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\neomuria2025_augment\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { Provider } from 'react-redux';\nimport { <PERSON>rowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport { store } from './store/index';\nimport App from './App';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n        theme: {\n          token: {\n            colorPrimary: '#1890ff'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 15,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "store", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "theme", "token", "colorPrimary", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/laragon/www/neomuria2025_augment/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport { store } from './store/index';\nimport App from './App';\nimport './index.css';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <Provider store={store}>\n      <BrowserRouter>\n        <ConfigProvider\n          theme={{\n            token: {\n              colorPrimary: '#1890ff',\n            },\n          }}\n        >\n          <App />\n        </ConfigProvider>\n      </BrowserRouter>\n    </Provider>\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGR,QAAQ,CAACS,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACR,KAAK,CAACc,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACN,QAAQ;IAACG,KAAK,EAAEA,KAAM;IAAAU,QAAA,eACrBP,OAAA,CAACL,aAAa;MAAAY,QAAA,eACZP,OAAA,CAACJ,cAAc;QACbY,KAAK,EAAE;UACLC,KAAK,EAAE;YACLC,YAAY,EAAE;UAChB;QACF,CAAE;QAAAH,QAAA,eAEFP,OAAA,CAACF,GAAG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}