# IMPLEMENTASI MODULAR ERP PETERNAKAN AYAM - LARAVEL 10

## 🎯 OVERVIEW IMPLEMENTASI

### **Tech Stack Modern (Docker-Ready)**
- **Backend**: Laravel 10.x + PHP 8.2+ (Dockerized)
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: Tailwind CSS + Headless UI + Shadcn/ui
- **Database**: MySQL 8.0+ dengan modular schema (Docker)
- **Cache/Queue**: Redis (Docker)
- **State Management**: Zustand + React Query
- **Authentication**: Laravel Sanctum + Social Login
- **API**: RESTful API + OpenAPI Documentation
- **Icons**: Lucide React + Heroicons
- **Charts**: Recharts + Chart.js

### **Design System**
- **Design Language**: Modern, Clean, Intuitive
- **Color Palette**: Professional dengan accent colors
- **Typography**: Inter font family
- **Components**: Reusable component library
- **Responsive**: Mobile-first approach
- **Dark Mode**: Built-in dark/light theme

## 🏗️ ARSITEKTUR MODULAR LARAVEL

### **Docker-Ready Project Structure**
```
erp-poultry/
├── docker/                      # Docker Configuration
│   ├── nginx/
│   │   └── default.conf
│   ├── php/
│   │   ├── Dockerfile
│   │   └── php.ini
│   └── mysql/
│       └── init.sql
├── app/
│   ├── Core/                    # Core System
│   │   ├── Auth/
│   │   │   ├── Controllers/
│   │   │   ├── Services/
│   │   │   └── Traits/
│   │   ├── RBAC/                # Role-Based Access Control
│   │   │   ├── Models/
│   │   │   ├── Policies/
│   │   │   └── Middleware/
│   │   ├── API/                 # REST API Layer
│   │   │   ├── Controllers/
│   │   │   ├── Resources/
│   │   │   └── Middleware/
│   │   └── Services/
│   ├── Modules/                 # Modular Plugins
│   │   ├── UserManagement/
│   │   ├── Inventory/
│   │   ├── Accounting/
│   │   ├── Sales/
│   │   ├── PoultryManagement/
│   │   └── EggProduction/
│   └── Shared/                  # Shared Components
│       ├── Traits/
│       ├── Services/
│       └── Contracts/
├── frontend/                    # React Frontend
│   ├── src/
│   │   ├── components/          # Reusable Components
│   │   │   ├── ui/              # Base UI Components
│   │   │   ├── forms/           # Form Components
│   │   │   └── charts/          # Chart Components
│   │   ├── pages/               # Page Components
│   │   ├── hooks/               # Custom Hooks
│   │   ├── stores/              # Zustand Stores
│   │   ├── services/            # API Services
│   │   ├── types/               # TypeScript Types
│   │   └── utils/               # Utility Functions
│   ├── public/
│   └── package.json
├── database/
│   ├── migrations/
│   │   └── modules/             # Per-module migrations
│   └── seeders/
├── routes/
│   ├── api.php                  # API Routes
│   └── web.php                  # Web Routes
├── docker-compose.yml           # Docker Compose
├── Dockerfile                   # Main Dockerfile
└── config/
    ├── modules.php              # Module configuration
    ├── cors.php                 # CORS configuration
    └── sanctum.php              # API authentication
```

### **Module Architecture Pattern**
```php
// app/Modules/[ModuleName]/
├── Controllers/
├── Models/
├── Services/
├── Requests/
├── Resources/
├── Events/
├── Listeners/
├── Policies/
├── Database/
│   ├── Migrations/
│   └── Seeders/
├── Routes/
│   ├── web.php
│   └── api.php
├── Config/
│   └── module.php
└── module.json
```

## 📋 IMPLEMENTASI PER MODUL

### **PHASE 1: CORE FOUNDATION (Week 1-2)**

#### **1.1 Docker-Ready Core System Setup**
```bash
# Project initialization with Docker
git clone https://github.com/your-org/erp-poultry-starter.git
cd erp-poultry

# Backend setup
composer create-project laravel/laravel backend
cd backend

# Install Laravel packages
composer require nwidart/laravel-modules
composer require laravel/sanctum
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog
composer require laravel/socialite
composer require fruitcake/laravel-cors
composer require darkaonline/l5-swagger

# Frontend setup (React)
cd ../frontend
npx create-react-app . --template typescript
npm install @tanstack/react-query zustand
npm install @headlessui/react lucide-react
npm install tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install recharts axios react-router-dom
npm install @hookform/resolvers react-hook-form zod
npm install shadcn-ui

# Docker setup
cd ..
docker-compose up -d --build
```

#### **1.2 Docker Configuration**
```dockerfile
# Dockerfile (Multi-stage build)
FROM php:8.2-fpm-alpine as backend

# Install system dependencies
RUN apk add --no-cache \
    git curl libpng-dev libxml2-dev zip unzip \
    mysql-client nginx supervisor

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Backend setup
WORKDIR /var/www/backend
COPY backend/ .
RUN composer install --optimize-autoloader --no-dev

# Frontend build stage
FROM node:18-alpine as frontend
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci
COPY frontend/ .
RUN npm run build

# Final stage
FROM backend as final
COPY --from=frontend /app/frontend/dist /var/www/frontend/dist
COPY docker/nginx/default.conf /etc/nginx/conf.d/
COPY docker/supervisord.conf /etc/supervisor/conf.d/

EXPOSE 80
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

```yaml
# docker-compose.yml (Production-ready)
version: '3.8'

services:
  app:
    build: .
    container_name: erp-poultry-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./backend/storage:/var/www/backend/storage
      - ./docker/nginx/ssl:/etc/nginx/ssl
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
    depends_on:
      - database
      - redis
    networks:
      - erp-network

  database:
    image: mysql:8.0
    container_name: erp-poultry-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: erp_poultry
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - dbdata:/var/lib/mysql
      - ./erp_poultry_modular_database.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - erp-network

  redis:
    image: redis:7-alpine
    container_name: erp-poultry-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redisdata:/data
    ports:
      - "6379:6379"
    networks:
      - erp-network

  mailhog:
    image: mailhog/mailhog
    container_name: erp-poultry-mail
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - erp-network

networks:
  erp-network:
    driver: bridge

volumes:
  dbdata:
  redisdata:
```

#### **1.3 REST API Authentication with RBAC & Social Login**
```php
// app/Core/Auth/Controllers/AuthController.php
class AuthController extends Controller
{
    use SocialAuthTrait, RBACTrait;

    public function login(LoginRequest $request)
    {
        $credentials = $request->validated();

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // Check if user has required permissions
            if (!$this->hasModuleAccess($user, 'core')) {
                return $this->errorResponse('Access denied', 403);
            }

            $token = $user->createToken('auth-token', $this->getUserAbilities($user))->plainTextToken;

            // Log activity
            activity()
                ->causedBy($user)
                ->withProperties(['ip' => $request->ip()])
                ->log('User logged in via API');

            return $this->successResponse([
                'user' => new UserResource($user->load('roles.permissions')),
                'token' => $token,
                'abilities' => $this->getUserAbilities($user),
                'modules' => $this->getAvailableModules($user)
            ]);
        }

        return $this->errorResponse('Invalid credentials', 401);
    }

    public function socialLogin(string $provider)
    {
        return Socialite::driver($provider)
            ->stateless()
            ->redirect();
    }

    public function socialCallback(string $provider, Request $request)
    {
        try {
            $socialUser = Socialite::driver($provider)->stateless()->user();

            $user = $this->findOrCreateSocialUser($socialUser, $provider);

            $token = $user->createToken('social-auth-token', $this->getUserAbilities($user))->plainTextToken;

            return $this->successResponse([
                'user' => new UserResource($user->load('roles.permissions')),
                'token' => $token,
                'abilities' => $this->getUserAbilities($user),
                'modules' => $this->getAvailableModules($user)
            ]);

        } catch (Exception $e) {
            return $this->errorResponse('Social login failed', 400);
        }
    }

    public function me(Request $request)
    {
        $user = $request->user()->load('roles.permissions');

        return $this->successResponse([
            'user' => new UserResource($user),
            'abilities' => $this->getUserAbilities($user),
            'modules' => $this->getAvailableModules($user)
        ]);
    }

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        activity()
            ->causedBy($request->user())
            ->log('User logged out');

        return $this->successResponse(['message' => 'Logged out successfully']);
    }
}
```

```php
// app/Core/RBAC/Traits/RBACTrait.php
trait RBACTrait
{
    public function hasModuleAccess(User $user, string $module): bool
    {
        return $user->hasPermissionTo("{$module}.access") || $user->hasRole('super_admin');
    }

    public function getUserAbilities(User $user): array
    {
        $abilities = $user->getAllPermissions()->pluck('name')->toArray();

        // Add role-based abilities
        foreach ($user->roles as $role) {
            $abilities[] = "role:{$role->name}";
        }

        return array_unique($abilities);
    }

    public function getAvailableModules(User $user): array
    {
        $modules = Module::where('status', 'active')->get();

        return $modules->filter(function ($module) use ($user) {
            return $this->hasModuleAccess($user, $module->name);
        })->map(function ($module) {
            return [
                'name' => $module->name,
                'display_name' => $module->display_name,
                'icon' => $module->config['icon'] ?? 'CubeIcon',
                'route' => $module->config['route'] ?? "/{$module->name}"
            ];
        })->values()->toArray();
    }
}
```

```php
// app/Core/Auth/Traits/SocialAuthTrait.php
trait SocialAuthTrait
{
    protected function findOrCreateSocialUser($socialUser, string $provider): User
    {
        // Check if user exists with this social provider
        $existingUser = User::where('email', $socialUser->getEmail())->first();

        if ($existingUser) {
            // Update social provider info
            $existingUser->update([
                "{$provider}_id" => $socialUser->getId(),
                "{$provider}_token" => $socialUser->token,
            ]);

            return $existingUser;
        }

        // Create new user
        $user = User::create([
            'uuid' => Str::uuid(),
            'username' => $this->generateUsername($socialUser->getName()),
            'email' => $socialUser->getEmail(),
            'first_name' => $this->extractFirstName($socialUser->getName()),
            'last_name' => $this->extractLastName($socialUser->getName()),
            'avatar' => $socialUser->getAvatar(),
            'email_verified_at' => now(),
            "{$provider}_id" => $socialUser->getId(),
            "{$provider}_token" => $socialUser->token,
            'password' => Hash::make(Str::random(32)), // Random password
        ]);

        // Assign default role
        $user->assignRole('viewer');

        return $user;
    }

    private function generateUsername(string $name): string
    {
        $username = Str::slug($name, '');
        $counter = 1;

        while (User::where('username', $username)->exists()) {
            $username = Str::slug($name, '') . $counter;
            $counter++;
        }

        return $username;
    }
}
```

#### **1.4 React Starter Kit with Reusable Components**
```typescript
// frontend/src/components/ui/Button.tsx
import React from 'react';
import { cn } from '@/utils/cn';
import { Loader2 } from 'lucide-react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', loading, icon, children, disabled, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';

    const variants = {
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500'
    };

    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };

    return (
      <button
        className={cn(baseClasses, variants[variant], sizes[size], className)}
        disabled={disabled || loading}
        ref={ref}
        {...props}
      >
        {loading ? (
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
        ) : icon ? (
          <span className="w-4 h-4 mr-2">{icon}</span>
        ) : null}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';
export { Button };
```

```typescript
// frontend/src/hooks/useAuth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService } from '@/services/authService';

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar?: string;
  roles: Role[];
  permissions: Permission[];
}

interface AuthState {
  user: User | null;
  token: string | null;
  abilities: string[];
  modules: Module[];
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  socialLogin: (provider: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasModuleAccess: (module: string) => boolean;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      abilities: [],
      modules: [],
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials) => {
        set({ isLoading: true });
        try {
          const response = await authService.login(credentials);
          set({
            user: response.user,
            token: response.token,
            abilities: response.abilities,
            modules: response.modules,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      socialLogin: async (provider) => {
        set({ isLoading: true });
        try {
          const response = await authService.socialLogin(provider);
          set({
            user: response.user,
            token: response.token,
            abilities: response.abilities,
            modules: response.modules,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        try {
          await authService.logout();
        } finally {
          set({
            user: null,
            token: null,
            abilities: [],
            modules: [],
            isAuthenticated: false
          });
        }
      },

      checkAuth: async () => {
        const { token } = get();
        if (!token) return;

        try {
          const response = await authService.me();
          set({
            user: response.user,
            abilities: response.abilities,
            modules: response.modules,
            isAuthenticated: true
          });
        } catch (error) {
          set({
            user: null,
            token: null,
            abilities: [],
            modules: [],
            isAuthenticated: false
          });
        }
      },

      hasPermission: (permission) => {
        const { abilities } = get();
        return abilities.includes(permission) || abilities.includes('role:super_admin');
      },

      hasRole: (role) => {
        const { abilities } = get();
        return abilities.includes(`role:${role}`);
      },

      hasModuleAccess: (module) => {
        const { modules } = get();
        return modules.some(m => m.name === module);
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        abilities: state.abilities,
        modules: state.modules,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
```

```typescript
// frontend/src/services/authService.ts
import { apiClient } from './apiClient';

interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

interface AuthResponse {
  user: User;
  token: string;
  abilities: string[];
  modules: Module[];
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data.data;
  }

  async socialLogin(provider: string): Promise<AuthResponse> {
    const response = await apiClient.get(`/auth/social/${provider}/callback`);
    return response.data.data;
  }

  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
  }

  async me(): Promise<AuthResponse> {
    const response = await apiClient.get('/auth/me');
    return response.data.data;
  }

  async register(userData: RegisterData): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/register', userData);
    return response.data.data;
  }

  async forgotPassword(email: string): Promise<void> {
    await apiClient.post('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, password: string): Promise<void> {
    await apiClient.post('/auth/reset-password', { token, password });
  }
}

export const authService = new AuthService();
```

#### **1.5 REST API Base Structure**
```php
// app/Core/API/Controllers/BaseApiController.php
abstract class BaseApiController extends Controller
{
    use ApiResponseTrait, ValidationTrait, CacheTrait;

    protected int $perPage = 15;
    protected array $allowedIncludes = [];
    protected array $allowedFilters = [];
    protected array $allowedSorts = [];

    public function index(Request $request)
    {
        $this->authorize('viewAny', $this->getModelClass());

        $query = $this->getModelClass()::query();

        // Apply filters
        $query = $this->applyFilters($query, $request);

        // Apply includes
        $query = $this->applyIncludes($query, $request);

        // Apply sorting
        $query = $this->applySorting($query, $request);

        $items = $query->paginate($request->get('per_page', $this->perPage));

        return $this->successResponse([
            'data' => $this->getResourceClass()::collection($items->items()),
            'meta' => $this->getPaginationMeta($items)
        ]);
    }

    public function show(Request $request, int $id)
    {
        $item = $this->findOrFail($id);
        $this->authorize('view', $item);

        $query = $this->getModelClass()::where('id', $id);
        $query = $this->applyIncludes($query, $request);
        $item = $query->first();

        return $this->successResponse([
            'data' => new ($this->getResourceClass())($item)
        ]);
    }

    public function store(Request $request)
    {
        $this->authorize('create', $this->getModelClass());

        $validated = $this->validateRequest($request, $this->getStoreRules());

        $item = DB::transaction(function () use ($validated) {
            return $this->performStore($validated);
        });

        return $this->successResponse([
            'data' => new ($this->getResourceClass())($item),
            'message' => 'Created successfully'
        ], 201);
    }

    public function update(Request $request, int $id)
    {
        $item = $this->findOrFail($id);
        $this->authorize('update', $item);

        $validated = $this->validateRequest($request, $this->getUpdateRules($item));

        $item = DB::transaction(function () use ($item, $validated) {
            return $this->performUpdate($item, $validated);
        });

        return $this->successResponse([
            'data' => new ($this->getResourceClass())($item),
            'message' => 'Updated successfully'
        ]);
    }

    public function destroy(int $id)
    {
        $item = $this->findOrFail($id);
        $this->authorize('delete', $item);

        DB::transaction(function () use ($item) {
            $this->performDestroy($item);
        });

        return $this->successResponse([
            'message' => 'Deleted successfully'
        ]);
    }

    // Abstract methods to be implemented by child controllers
    abstract protected function getModelClass(): string;
    abstract protected function getResourceClass(): string;
    abstract protected function getStoreRules(): array;
    abstract protected function getUpdateRules($item): array;
    abstract protected function performStore(array $data);
    abstract protected function performUpdate($item, array $data);
    abstract protected function performDestroy($item): void;
}
```

```php
// app/Core/API/Traits/ApiResponseTrait.php
trait ApiResponseTrait
{
    protected function successResponse(array $data, int $status = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data['data'] ?? null,
            'message' => $data['message'] ?? null,
            'meta' => $data['meta'] ?? null
        ], $status);
    }

    protected function errorResponse(string $message, int $status = 400, array $errors = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $status);
    }

    protected function validationErrorResponse(array $errors): JsonResponse
    {
        return $this->errorResponse('Validation failed', 422, $errors);
    }

    protected function getPaginationMeta($paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem()
        ];
    }
}
```

### **PHASE 2: USER MANAGEMENT MODULE (Week 3)**

#### **2.1 Module Installation & API Setup**
```bash
# Backend module creation
php artisan module:make UserManagement
php artisan module:make-controller UserManagement UserController --api
php artisan module:make-model UserManagement User
php artisan module:make-request UserManagement UserRequest
php artisan module:make-resource UserManagement UserResource
php artisan module:make-policy UserManagement UserPolicy

# Frontend module creation
mkdir -p frontend/src/modules/user-management
mkdir -p frontend/src/modules/user-management/{components,pages,hooks,types}
```

#### **2.2 Lean & Reusable User Management API**
```php
// Modules/UserManagement/Controllers/UserController.php
class UserController extends BaseApiController
{
    protected array $allowedIncludes = ['roles', 'permissions', 'modules'];
    protected array $allowedFilters = ['search', 'role', 'status', 'created_at'];
    protected array $allowedSorts = ['name', 'email', 'created_at', 'last_login_at'];

    protected function getModelClass(): string
    {
        return User::class;
    }

    protected function getResourceClass(): string
    {
        return UserResource::class;
    }

    protected function getStoreRules(): array
    {
        return [
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id'
        ];
    }

    protected function getUpdateRules($user): array
    {
        return [
            'username' => "required|string|max:50|unique:users,username,{$user->id}",
            'email' => "required|email|unique:users,email,{$user->id}",
            'password' => 'nullable|string|min:8|confirmed',
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'status' => 'in:active,inactive,suspended',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id'
        ];
    }

    protected function performStore(array $data): User
    {
        $user = User::create([
            'uuid' => Str::uuid(),
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'phone' => $data['phone'] ?? null,
        ]);

        if (isset($data['roles'])) {
            $user->syncRoles($data['roles']);
        }

        event(new UserCreated($user));

        return $user->load('roles');
    }

    protected function performUpdate($user, array $data): User
    {
        $updateData = collect($data)->except(['password', 'roles'])->toArray();

        if (!empty($data['password'])) {
            $updateData['password'] = Hash::make($data['password']);
        }

        $user->update($updateData);

        if (isset($data['roles'])) {
            $user->syncRoles($data['roles']);
        }

        event(new UserUpdated($user));

        return $user->load('roles');
    }

    protected function performDestroy($user): void
    {
        $user->delete();
        event(new UserDeleted($user));
    }

    public function assignRole(Request $request, User $user)
    {
        $this->authorize('assignRole', $user);

        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id'
        ]);

        $user->syncRoles($validated['roles']);

        return $this->successResponse([
            'data' => new UserResource($user->load('roles')),
            'message' => 'Roles assigned successfully'
        ]);
    }

    public function revokeRole(Request $request, User $user)
    {
        $this->authorize('revokeRole', $user);

        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id'
        ]);

        $user->removeRole($validated['roles']);

        return $this->successResponse([
            'data' => new UserResource($user->load('roles')),
            'message' => 'Roles revoked successfully'
        ]);
    }
}
```

```php
// Modules/UserManagement/Resources/UserResource.php
class UserResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'username' => $this->username,
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->first_name . ' ' . $this->last_name,
            'phone' => $this->phone,
            'avatar' => $this->avatar,
            'status' => $this->status,
            'last_login_at' => $this->last_login_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),

            // Conditional includes
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
            'modules' => ModuleResource::collection($this->whenLoaded('modules')),
        ];
    }
}
```

#### **2.3 React User Management Interface**
```typescript
// frontend/src/modules/user-management/pages/UsersPage.tsx
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { DataTable } from '@/components/ui/DataTable';
import { Modal } from '@/components/ui/Modal';
import { useAuth } from '@/hooks/useAuth';
import { userService } from '../services/userService';
import { UserForm } from '../components/UserForm';
import { UserFilters } from '../types';

export const UsersPage: React.FC = () => {
  const { hasPermission } = useAuth();
  const queryClient = useQueryClient();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    role: '',
    status: '',
    page: 1,
    per_page: 15
  });

  const { data: usersData, isLoading } = useQuery({
    queryKey: ['users', filters],
    queryFn: () => userService.getUsers(filters),
    keepPreviousData: true
  });

  const { data: rolesData } = useQuery({
    queryKey: ['roles'],
    queryFn: () => userService.getRoles(),
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const deleteUserMutation = useMutation({
    mutationFn: userService.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries(['users']);
      toast.success('User deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete user');
    }
  });

  const columns = [
    {
      key: 'avatar',
      label: '',
      render: (user: User) => (
        <Avatar
          src={user.avatar}
          name={user.full_name}
          size="sm"
        />
      )
    },
    {
      key: 'full_name',
      label: 'Name',
      sortable: true,
      render: (user: User) => (
        <div>
          <div className="font-medium text-gray-900">{user.full_name}</div>
          <div className="text-sm text-gray-500">{user.username}</div>
        </div>
      )
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true
    },
    {
      key: 'roles',
      label: 'Roles',
      render: (user: User) => (
        <div className="flex flex-wrap gap-1">
          {user.roles?.map(role => (
            <Badge key={role.id} variant="secondary" size="sm">
              {role.display_name}
            </Badge>
          ))}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (user: User) => (
        <Badge
          variant={getStatusVariant(user.status)}
          text={user.status}
        />
      )
    },
    {
      key: 'last_login_at',
      label: 'Last Login',
      sortable: true,
      render: (user: User) => (
        user.last_login_at ? formatDate(user.last_login_at) : 'Never'
      )
    },
    {
      key: 'actions',
      label: '',
      render: (user: User) => (
        <DropdownMenu>
          {hasPermission('users.edit') && (
            <DropdownItem onClick={() => editUser(user)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </DropdownItem>
          )}
          {hasPermission('users.assign_role') && (
            <DropdownItem onClick={() => manageRoles(user)}>
              <Shield className="w-4 h-4 mr-2" />
              Manage Roles
            </DropdownItem>
          )}
          {hasPermission('users.delete') && (
            <DropdownItem
              variant="danger"
              onClick={() => handleDeleteUser(user)}
            >
              <Trash className="w-4 h-4 mr-2" />
              Delete
            </DropdownItem>
          )}
        </DropdownMenu>
      )
    }
  ];

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handleFilterChange = (key: keyof UserFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleDeleteUser = async (user: User) => {
    if (await confirm(`Are you sure you want to delete ${user.full_name}?`)) {
      deleteUserMutation.mutate(user.id);
    }
  };

  const editUser = (user: User) => {
    setSelectedUser(user);
    setShowCreateModal(true);
  };

  const handleUserSaved = () => {
    setShowCreateModal(false);
    setSelectedUser(null);
    queryClient.invalidateQueries(['users']);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users</h1>
          <p className="text-gray-600">Manage system users and permissions</p>
        </div>
        {hasPermission('users.create') && (
          <Button
            variant="primary"
            icon={<Plus className="w-4 h-4" />}
            onClick={() => setShowCreateModal(true)}
          >
            Add User
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            placeholder="Search users..."
            value={filters.search}
            onChange={(e) => handleSearch(e.target.value)}
            icon={<Search className="w-4 h-4" />}
          />
          <Select
            placeholder="Filter by role"
            value={filters.role}
            onChange={(value) => handleFilterChange('role', value)}
            options={rolesData?.map(role => ({
              value: role.id,
              label: role.display_name
            })) || []}
          />
          <Select
            placeholder="Filter by status"
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            options={[
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'suspended', label: 'Suspended' }
            ]}
          />
          <Button
            variant="outline"
            onClick={() => setFilters({ search: '', role: '', status: '', page: 1, per_page: 15 })}
          >
            Clear Filters
          </Button>
        </div>
      </Card>

      {/* Users Table */}
      <Card>
        <DataTable
          columns={columns}
          data={usersData?.data || []}
          loading={isLoading}
          pagination={{
            current: usersData?.meta?.current_page || 1,
            total: usersData?.meta?.total || 0,
            pageSize: usersData?.meta?.per_page || 15,
            onChange: (page) => handleFilterChange('page', page)
          }}
          onSort={(field, direction) => {
            handleFilterChange('sort', `${field}:${direction}`);
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        open={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setSelectedUser(null);
        }}
        title={selectedUser ? 'Edit User' : 'Create User'}
        size="lg"
      >
        <UserForm
          user={selectedUser}
          roles={rolesData || []}
          onSave={handleUserSaved}
          onCancel={() => {
            setShowCreateModal(false);
            setSelectedUser(null);
          }}
        />
      </Modal>
    </div>
  );
};
```

      <!-- Filters -->
      <Card class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <SearchInput
            v-model="filters.search"
            placeholder="Search users..."
            @input="search"
          />
          <Select
            v-model="filters.role"
            :options="roleOptions"
            placeholder="Filter by role"
            @change="search"
          />
          <Select
            v-model="filters.status"
            :options="statusOptions"
            placeholder="Filter by status"
            @change="search"
          />
        </div>
      </Card>

      <!-- Users Table -->
      <Card>
        <DataTable
          :columns="columns"
          :data="users.data"
          :loading="loading"
          @sort="handleSort"
        >
          <template #avatar="{ row }">
            <Avatar
              :src="row.avatar"
              :name="row.name"
              size="sm"
            />
          </template>
          
          <template #status="{ row }">
            <Badge
              :variant="getStatusVariant(row.status)"
              :text="row.status"
            />
          </template>
          
          <template #actions="{ row }">
            <DropdownMenu>
              <DropdownItem @click="editUser(row)">
                <PencilIcon class="w-4 h-4 mr-2" />
                Edit
              </DropdownItem>
              <DropdownItem @click="resetPassword(row)">
                <KeyIcon class="w-4 h-4 mr-2" />
                Reset Password
              </DropdownItem>
              <DropdownItem
                variant="danger"
                @click="deleteUser(row)"
              >
                <TrashIcon class="w-4 h-4 mr-2" />
                Delete
              </DropdownItem>
            </DropdownMenu>
          </template>
        </DataTable>
        
        <Pagination
          :links="users.links"
          :meta="users.meta"
        />
      </Card>
    </div>

    <!-- Create/Edit Modal -->
    <Modal
      :show="showCreateModal"
      @close="showCreateModal = false"
      max-width="2xl"
    >
      <UserForm
        :user="selectedUser"
        :roles="roles"
        @saved="handleUserSaved"
        @cancel="showCreateModal = false"
      />
    </Modal>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import { useToast } from '@/Composables/useToast'

interface Props {
  users: PaginatedData<User>
  filters: UserFilters
  roles: Role[]
}

const props = defineProps<Props>()
const { toast } = useToast()

const showCreateModal = ref(false)
const selectedUser = ref<User | null>(null)
const loading = ref(false)

const columns = [
  { key: 'avatar', label: '', sortable: false },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'roles', label: 'Roles', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'last_login_at', label: 'Last Login', sortable: true },
  { key: 'actions', label: '', sortable: false }
]

const search = debounce(() => {
  router.get(route('users.index'), props.filters, {
    preserveState: true,
    preserveScroll: true,
  })
}, 300)

const editUser = (user: User) => {
  selectedUser.value = user
  showCreateModal.value = true
}

const deleteUser = async (user: User) => {
  if (await confirm('Are you sure you want to delete this user?')) {
    router.delete(route('users.destroy', user.id), {
      onSuccess: () => toast.success('User deleted successfully'),
      onError: () => toast.error('Failed to delete user')
    })
  }
}
</script>
```

### **PHASE 3: INVENTORY MODULE (Week 4-5)**

#### **3.1 Advanced Inventory Features**
```php
// Modules/Inventory/Services/StockService.php
class StockService
{
    public function moveStock(
        Item $item,
        Warehouse $fromWarehouse,
        Warehouse $toWarehouse,
        float $quantity,
        string $reason = null
    ): StockMovement {
        DB::transaction(function () use ($item, $fromWarehouse, $toWarehouse, $quantity, $reason) {
            // Create outbound movement
            $outbound = StockMovement::create([
                'uuid' => Str::uuid(),
                'item_id' => $item->id,
                'warehouse_id' => $fromWarehouse->id,
                'movement_type' => 'out',
                'transaction_type' => 'transfer',
                'quantity' => $quantity,
                'unit_id' => $item->unit_id,
                'reason' => $reason,
                'movement_date' => now()->toDateString(),
                'created_by' => auth()->id(),
            ]);

            // Create inbound movement
            $inbound = StockMovement::create([
                'uuid' => Str::uuid(),
                'item_id' => $item->id,
                'warehouse_id' => $toWarehouse->id,
                'movement_type' => 'in',
                'transaction_type' => 'transfer',
                'quantity' => $quantity,
                'unit_id' => $item->unit_id,
                'reference_id' => $outbound->id,
                'reason' => $reason,
                'movement_date' => now()->toDateString(),
                'created_by' => auth()->id(),
            ]);

            // Fire events
            event(new StockMoved($item, $fromWarehouse, $toWarehouse, $quantity));
            
            return $inbound;
        });
    }
}
```

#### **3.2 Real-time Stock Dashboard**
```vue
<!-- resources/js/Pages/Inventory/Dashboard.vue -->
<template>
  <AppLayout title="Inventory Dashboard">
    <div class="space-y-6">
      <!-- KPI Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatsCard
          title="Total Items"
          :value="stats.total_items"
          icon="CubeIcon"
          trend="+12%"
          trend-direction="up"
        />
        <StatsCard
          title="Low Stock Items"
          :value="stats.low_stock_items"
          icon="ExclamationTriangleIcon"
          trend="-5%"
          trend-direction="down"
          variant="warning"
        />
        <StatsCard
          title="Total Value"
          :value="formatCurrency(stats.total_value)"
          icon="CurrencyDollarIcon"
          trend="+8%"
          trend-direction="up"
        />
        <StatsCard
          title="Warehouses"
          :value="stats.total_warehouses"
          icon="BuildingStorefrontIcon"
        />
      </div>

      <!-- Charts Row -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Stock Movement Chart -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">Stock Movements</h3>
          <LineChart
            :data="stockMovementData"
            :options="chartOptions"
            height="300"
          />
        </Card>

        <!-- Top Items Chart -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">Top Items by Value</h3>
          <DoughnutChart
            :data="topItemsData"
            :options="doughnutOptions"
            height="300"
          />
        </Card>
      </div>

      <!-- Recent Activities & Alerts -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Stock Movements -->
        <Card class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Recent Movements</h3>
            <Link
              :href="route('inventory.movements.index')"
              class="text-blue-600 hover:text-blue-800 text-sm"
            >
              View All
            </Link>
          </div>
          <div class="space-y-3">
            <div
              v-for="movement in recentMovements"
              :key="movement.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div
                  :class="[
                    'w-2 h-2 rounded-full',
                    movement.movement_type === 'in' ? 'bg-green-500' : 'bg-red-500'
                  ]"
                />
                <div>
                  <p class="font-medium">{{ movement.item.name }}</p>
                  <p class="text-sm text-gray-600">
                    {{ movement.warehouse.name }} • {{ movement.quantity }} {{ movement.unit.symbol }}
                  </p>
                </div>
              </div>
              <span class="text-sm text-gray-500">
                {{ formatDate(movement.created_at) }}
              </span>
            </div>
          </div>
        </Card>

        <!-- Stock Alerts -->
        <Card class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Stock Alerts</h3>
            <Badge :text="`${stockAlerts.length} alerts`" variant="warning" />
          </div>
          <div class="space-y-3">
            <div
              v-for="alert in stockAlerts"
              :key="alert.id"
              class="flex items-center justify-between p-3 border border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <ExclamationTriangleIcon class="w-5 h-5 text-yellow-600" />
                <div>
                  <p class="font-medium">{{ alert.item.name }}</p>
                  <p class="text-sm text-gray-600">
                    Current: {{ alert.current_stock }} • Min: {{ alert.min_stock }}
                  </p>
                </div>
              </div>
              <Button
                size="sm"
                variant="secondary"
                @click="createPurchaseOrder(alert.item)"
              >
                Reorder
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { LineChart, DoughnutChart } from 'vue-chartjs'

interface Props {
  stats: InventoryStats
  recentMovements: StockMovement[]
  stockAlerts: StockAlert[]
  chartData: ChartData
}

const props = defineProps<Props>()

const stockMovementData = computed(() => ({
  labels: props.chartData.movement_labels,
  datasets: [
    {
      label: 'Stock In',
      data: props.chartData.stock_in_data,
      borderColor: 'rgb(34, 197, 94)',
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
      tension: 0.4
    },
    {
      label: 'Stock Out',
      data: props.chartData.stock_out_data,
      borderColor: 'rgb(239, 68, 68)',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      tension: 0.4
    }
  ]
}))

const topItemsData = computed(() => ({
  labels: props.chartData.top_items_labels,
  datasets: [{
    data: props.chartData.top_items_values,
    backgroundColor: [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
    ]
  }]
}))
</script>
```

### **PHASE 4: POULTRY MANAGEMENT MODULE (Week 6-7)**

#### **4.1 Farm & House Management**
```php
// Modules/PoultryManagement/Controllers/HouseController.php
class HouseController extends Controller
{
    public function index(Request $request)
    {
        $houses = House::with(['farm', 'flocks.breed'])
            ->when($request->farm_id, fn($q, $farm) => $q->where('farm_id', $farm))
            ->when($request->status, fn($q, $status) => $q->where('status', $status))
            ->withCount(['flocks as active_flocks_count' => fn($q) => $q->where('status', 'active')])
            ->withSum('flocks as total_birds', 'current_count')
            ->paginate(12);

        return Inertia::render('PoultryManagement/Houses/Index', [
            'houses' => HouseResource::collection($houses),
            'farms' => Farm::select('id', 'name')->get(),
            'filters' => $request->only(['farm_id', 'status']),
            'stats' => $this->getHouseStats(),
        ]);
    }

    public function show(House $house)
    {
        $house->load([
            'farm',
            'flocks.breed',
            'flocks' => fn($q) => $q->where('status', 'active')
        ]);

        return Inertia::render('PoultryManagement/Houses/Show', [
            'house' => new HouseResource($house),
            'environmentalData' => $this->getEnvironmentalData($house),
            'productionSummary' => $this->getProductionSummary($house),
        ]);
    }
}
```

#### **4.2 Modern House Dashboard**
```vue
<!-- resources/js/Pages/PoultryManagement/Houses/Show.vue -->
<template>
  <AppLayout :title="`House ${house.code}`">
    <div class="space-y-6">
      <!-- House Header -->
      <div class="bg-gradient-to-r from-green-600 to-blue-600 rounded-xl p-6 text-white">
        <div class="flex justify-between items-start">
          <div>
            <h1 class="text-3xl font-bold">{{ house.name }}</h1>
            <p class="text-green-100 mt-1">{{ house.farm.name }}</p>
            <div class="flex items-center mt-4 space-x-6">
              <div class="flex items-center">
                <HomeIcon class="w-5 h-5 mr-2" />
                <span>{{ house.house_type }}</span>
              </div>
              <div class="flex items-center">
                <CubeIcon class="w-5 h-5 mr-2" />
                <span>{{ house.area }} m²</span>
              </div>
              <div class="flex items-center">
                <UserGroupIcon class="w-5 h-5 mr-2" />
                <span>{{ house.current_population }}/{{ house.capacity_birds }} birds</span>
              </div>
            </div>
          </div>
          <div class="text-right">
            <Badge
              :text="house.status"
              :variant="getStatusVariant(house.status)"
              size="lg"
            />
            <div class="mt-2">
              <Button
                variant="secondary"
                size="sm"
                @click="showEditModal = true"
              >
                Edit House
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Capacity & Utilization -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Capacity Utilization</p>
              <p class="text-2xl font-bold">{{ utilizationPercentage }}%</p>
            </div>
            <div class="w-16 h-16">
              <CircularProgress
                :percentage="utilizationPercentage"
                :color="getUtilizationColor(utilizationPercentage)"
              />
            </div>
          </div>
        </Card>

        <Card class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Active Flocks</p>
              <p class="text-2xl font-bold">{{ house.active_flocks_count }}</p>
            </div>
            <ChartBarIcon class="w-8 h-8 text-blue-500" />
          </div>
        </Card>

        <Card class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Average Age</p>
              <p class="text-2xl font-bold">{{ averageAge }} weeks</p>
            </div>
            <ClockIcon class="w-8 h-8 text-green-500" />
          </div>
        </Card>
      </div>

      <!-- Environmental Monitoring -->
      <Card class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold">Environmental Monitoring</h3>
          <div class="flex space-x-2">
            <Button
              size="sm"
              variant="ghost"
              :class="{ 'bg-blue-100': timeRange === '24h' }"
              @click="timeRange = '24h'"
            >
              24H
            </Button>
            <Button
              size="sm"
              variant="ghost"
              :class="{ 'bg-blue-100': timeRange === '7d' }"
              @click="timeRange = '7d'"
            >
              7D
            </Button>
            <Button
              size="sm"
              variant="ghost"
              :class="{ 'bg-blue-100': timeRange === '30d' }"
              @click="timeRange = '30d'"
            >
              30D
            </Button>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Temperature Chart -->
          <div class="lg:col-span-2">
            <LineChart
              :data="temperatureChartData"
              :options="chartOptions"
              height="300"
            />
          </div>

          <!-- Current Readings -->
          <div class="space-y-4">
            <EnvironmentalCard
              title="Temperature"
              :value="`${environmentalData.current_temperature}°C`"
              :status="getTemperatureStatus(environmentalData.current_temperature)"
              icon="ThermometerIcon"
            />
            <EnvironmentalCard
              title="Humidity"
              :value="`${environmentalData.current_humidity}%`"
              :status="getHumidityStatus(environmentalData.current_humidity)"
              icon="CloudIcon"
            />
            <EnvironmentalCard
              title="Air Quality"
              :value="environmentalData.air_quality_index"
              :status="getAirQualityStatus(environmentalData.air_quality_index)"
              icon="WindIcon"
            />
          </div>
        </div>
      </Card>

      <!-- Active Flocks -->
      <Card class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold">Active Flocks</h3>
          <Button
            variant="primary"
            icon="PlusIcon"
            @click="showAddFlockModal = true"
          >
            Add Flock
          </Button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <FlockCard
            v-for="flock in house.flocks"
            :key="flock.id"
            :flock="flock"
            @view="viewFlock"
            @edit="editFlock"
          />
        </div>
      </Card>

      <!-- Production Summary -->
      <Card class="p-6">
        <h3 class="text-lg font-semibold mb-6">Production Summary (Last 30 Days)</h3>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <StatsCard
            title="Total Eggs"
            :value="productionSummary.total_eggs.toLocaleString()"
            icon="EggIcon"
            :trend="productionSummary.eggs_trend"
          />
          <StatsCard
            title="Laying Rate"
            :value="`${productionSummary.laying_rate}%`"
            icon="TrendingUpIcon"
            :trend="productionSummary.laying_rate_trend"
          />
          <StatsCard
            title="Feed Consumed"
            :value="`${productionSummary.feed_consumed} kg`"
            icon="CubeIcon"
            :trend="productionSummary.feed_trend"
          />
          <StatsCard
            title="FCR"
            :value="productionSummary.fcr"
            icon="CalculatorIcon"
            :trend="productionSummary.fcr_trend"
          />
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium mb-4">Egg Production Trend</h4>
            <LineChart
              :data="eggProductionChartData"
              :options="productionChartOptions"
              height="250"
            />
          </div>
          <div>
            <h4 class="font-medium mb-4">Feed Consumption vs Production</h4>
            <BarChart
              :data="feedConsumptionChartData"
              :options="feedChartOptions"
              height="250"
            />
          </div>
        </div>
      </Card>
    </div>

    <!-- Modals -->
    <Modal :show="showEditModal" @close="showEditModal = false" max-width="4xl">
      <HouseForm
        :house="house"
        @saved="handleHouseSaved"
        @cancel="showEditModal = false"
      />
    </Modal>

    <Modal :show="showAddFlockModal" @close="showAddFlockModal = false" max-width="3xl">
      <FlockForm
        :house="house"
        @saved="handleFlockSaved"
        @cancel="showAddFlockModal = false"
      />
    </Modal>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { LineChart, BarChart } from 'vue-chartjs'

interface Props {
  house: House
  environmentalData: EnvironmentalData
  productionSummary: ProductionSummary
}

const props = defineProps<Props>()

const showEditModal = ref(false)
const showAddFlockModal = ref(false)
const timeRange = ref('24h')

const utilizationPercentage = computed(() =>
  Math.round((props.house.current_population / props.house.capacity_birds) * 100)
)

const averageAge = computed(() => {
  if (!props.house.flocks?.length) return 0
  const totalAge = props.house.flocks.reduce((sum, flock) => sum + flock.age_weeks, 0)
  return Math.round(totalAge / props.house.flocks.length)
})

const temperatureChartData = computed(() => ({
  labels: props.environmentalData.temperature_labels,
  datasets: [{
    label: 'Temperature (°C)',
    data: props.environmentalData.temperature_data,
    borderColor: 'rgb(239, 68, 68)',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    tension: 0.4
  }]
}))

// Watch for time range changes and fetch new data
watch(timeRange, (newRange) => {
  // Fetch new environmental data based on time range
  router.get(route('houses.show', props.house.id),
    { time_range: newRange },
    { preserveState: true, only: ['environmentalData'] }
  )
})
</script>
```

### **PHASE 5: EGG PRODUCTION MODULE (Week 8)**

#### **5.1 Daily Production Recording**
```php
// Modules/EggProduction/Controllers/ProductionController.php
class ProductionController extends Controller
{
    public function store(ProductionRecordRequest $request)
    {
        $record = DB::transaction(function () use ($request) {
            $record = EggProductionRecord::create([
                'uuid' => Str::uuid(),
                'flock_id' => $request->flock_id,
                'record_date' => $request->record_date,
                'age_weeks' => $request->age_weeks,
                'bird_count' => $request->bird_count,
                'eggs_collected' => $request->eggs_collected,
                'eggs_good' => $request->eggs_good,
                'eggs_cracked' => $request->eggs_cracked,
                'eggs_dirty' => $request->eggs_dirty,
                'total_weight_kg' => $request->total_weight_kg,
                'mortality_count' => $request->mortality_count,
                'feed_consumed_kg' => $request->feed_consumed_kg,
                'temperature_min' => $request->temperature_min,
                'temperature_max' => $request->temperature_max,
                'humidity_percentage' => $request->humidity_percentage,
                'recorded_by' => auth()->id(),
            ]);

            // Update flock population
            $record->flock->update([
                'current_count' => $record->flock->current_count - $request->mortality_count,
                'mortality_count' => $record->flock->mortality_count + $request->mortality_count,
            ]);

            // Create inventory transaction for eggs
            if ($request->eggs_good > 0) {
                $this->createEggInventoryTransaction($record);
            }

            // Fire events
            event(new EggProductionRecorded($record));

            return $record;
        });

        return redirect()->route('egg-production.index')
            ->with('success', 'Production record saved successfully');
    }

    private function createEggInventoryTransaction(EggProductionRecord $record): void
    {
        $eggItem = Item::where('code', 'EGG-FRESH')->first();

        if ($eggItem) {
            StockMovement::create([
                'uuid' => Str::uuid(),
                'item_id' => $eggItem->id,
                'warehouse_id' => $record->flock->house->farm->warehouse_id,
                'movement_type' => 'in',
                'transaction_type' => 'production',
                'reference_type' => EggProductionRecord::class,
                'reference_id' => $record->id,
                'quantity' => $record->eggs_good,
                'unit_id' => Unit::where('code', 'pcs')->first()->id,
                'movement_date' => $record->record_date,
                'created_by' => auth()->id(),
            ]);
        }
    }
}
```

#### **5.2 Production Analytics Dashboard**
```vue
<!-- resources/js/Pages/EggProduction/Analytics.vue -->
<template>
  <AppLayout title="Production Analytics">
    <div class="space-y-6">
      <!-- Date Range Selector -->
      <Card class="p-6">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-semibold">Production Analytics</h2>
          <div class="flex items-center space-x-4">
            <DateRangePicker
              v-model="dateRange"
              @change="fetchAnalytics"
            />
            <Select
              v-model="selectedFarm"
              :options="farmOptions"
              placeholder="All Farms"
              @change="fetchAnalytics"
            />
          </div>
        </div>
      </Card>

      <!-- KPI Overview -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
        <StatsCard
          title="Total Eggs"
          :value="analytics.total_eggs.toLocaleString()"
          icon="EggIcon"
          :trend="analytics.eggs_trend"
          trend-direction="up"
        />
        <StatsCard
          title="Avg Laying Rate"
          :value="`${analytics.avg_laying_rate}%`"
          icon="TrendingUpIcon"
          :trend="analytics.laying_rate_trend"
        />
        <StatsCard
          title="Total Birds"
          :value="analytics.total_birds.toLocaleString()"
          icon="UserGroupIcon"
          :trend="analytics.birds_trend"
        />
        <StatsCard
          title="Avg FCR"
          :value="analytics.avg_fcr"
          icon="CalculatorIcon"
          :trend="analytics.fcr_trend"
          trend-direction="down"
        />
        <StatsCard
          title="Mortality Rate"
          :value="`${analytics.mortality_rate}%`"
          icon="ExclamationTriangleIcon"
          :trend="analytics.mortality_trend"
          trend-direction="down"
          variant="warning"
        />
      </div>

      <!-- Charts Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Production Trend -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">Production Trend</h3>
          <LineChart
            :data="productionTrendData"
            :options="lineChartOptions"
            height="350"
          />
        </Card>

        <!-- Laying Rate by Age -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">Laying Rate by Age</h3>
          <AreaChart
            :data="layingRateByAgeData"
            :options="areaChartOptions"
            height="350"
          />
        </Card>

        <!-- Feed Conversion Ratio -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">Feed Conversion Ratio</h3>
          <BarChart
            :data="fcrData"
            :options="barChartOptions"
            height="350"
          />
        </Card>

        <!-- Egg Quality Distribution -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold mb-4">Egg Quality Distribution</h3>
          <DoughnutChart
            :data="eggQualityData"
            :options="doughnutOptions"
            height="350"
          />
        </Card>
      </div>

      <!-- Performance by House -->
      <Card class="p-6">
        <h3 class="text-lg font-semibold mb-6">Performance by House</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  House
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Birds
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Eggs/Day
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Laying Rate
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  FCR
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mortality
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="house in analytics.house_performance"
                :key="house.id"
                class="hover:bg-gray-50"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ house.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ house.farm_name }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ house.bird_count.toLocaleString() }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ house.avg_eggs_per_day.toLocaleString() }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-900">{{ house.laying_rate }}%</span>
                    <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-green-600 h-2 rounded-full"
                        :style="{ width: `${house.laying_rate}%` }"
                      />
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ house.fcr }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ house.mortality_rate }}%
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <PerformanceBadge :score="house.performance_score" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { LineChart, AreaChart, BarChart, DoughnutChart } from 'vue-chartjs'

interface Props {
  analytics: ProductionAnalytics
  farms: Farm[]
}

const props = defineProps<Props>()

const dateRange = ref({
  start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
  end: new Date()
})
const selectedFarm = ref(null)

const productionTrendData = computed(() => ({
  labels: props.analytics.trend_labels,
  datasets: [
    {
      label: 'Eggs Collected',
      data: props.analytics.eggs_trend_data,
      borderColor: 'rgb(34, 197, 94)',
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
      tension: 0.4
    },
    {
      label: 'Laying Rate %',
      data: props.analytics.laying_rate_trend_data,
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
      yAxisID: 'y1'
    }
  ]
}))

const fetchAnalytics = () => {
  router.get(route('egg-production.analytics'), {
    date_range: dateRange.value,
    farm_id: selectedFarm.value
  }, {
    preserveState: true,
    only: ['analytics']
  })
}
</script>
```

## 🚀 PRODUCTION-READY DEPLOYMENT

### **6.1 Multi-Stage Docker Configuration**
```dockerfile
# Multi-stage Dockerfile for production
FROM node:18-alpine as frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

# PHP Backend stage
FROM php:8.2-fpm-alpine as backend

# Install system dependencies
RUN apk add --no-cache \
    git curl libpng-dev libxml2-dev zip unzip \
    mysql-client nginx supervisor \
    freetype-dev libjpeg-turbo-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd

# Install PHP extensions
RUN docker-php-ext-install \
    pdo pdo_mysql mbstring exif pcntl bcmath \
    opcache intl

# Install Redis extension
RUN apk add --no-cache pcre-dev $PHPIZE_DEPS \
    && pecl install redis \
    && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configure PHP for production
COPY docker/php/php.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# Set working directory
WORKDIR /var/www

# Copy backend files
COPY backend/ .

# Install PHP dependencies
RUN composer install --optimize-autoloader --no-dev --no-scripts \
    && composer dump-autoload --optimize

# Copy frontend build
COPY --from=frontend-builder /app/frontend/dist ./public/dist

# Configure Nginx
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf

# Configure Supervisor
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www/storage \
    && chmod -R 755 /var/www/bootstrap/cache

# Health check
COPY docker/healthcheck.sh /usr/local/bin/healthcheck
RUN chmod +x /usr/local/bin/healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck

EXPOSE 80 443
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    container_name: erp-poultry-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - erp-network

  webserver:
    image: nginx:alpine
    container_name: erp-poultry-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - erp-network

  database:
    image: mysql:8.0
    container_name: erp-poultry-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: erp_poultry
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_PASSWORD: secret
      MYSQL_USER: erp_user
    volumes:
      - dbdata:/var/lib/mysql
      - ./erp_poultry_modular_database.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - erp-network

  redis:
    image: redis:alpine
    container_name: erp-poultry-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - erp-network

networks:
  erp-network:
    driver: bridge

volumes:
  dbdata:
    driver: local
```

### **6.2 CI/CD Pipeline**
```yaml
# .github/workflows/deploy.yml
name: Deploy ERP Poultry

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: secret
          MYSQL_DATABASE: erp_poultry_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql

    - name: Install Composer dependencies
      run: composer install --prefer-dist --no-progress

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install NPM dependencies
      run: npm install

    - name: Build assets
      run: npm run build

    - name: Copy environment file
      run: cp .env.example .env

    - name: Generate application key
      run: php artisan key:generate

    - name: Run database migrations
      run: php artisan migrate --force

    - name: Run tests
      run: php artisan test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /var/www/erp-poultry
          git pull origin main
          composer install --optimize-autoloader --no-dev
          npm install && npm run build
          php artisan migrate --force
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          sudo systemctl reload nginx
```

## 🧪 TESTING STRATEGY

### **7.1 Unit Testing**
```php
// tests/Unit/Modules/Inventory/StockServiceTest.php
class StockServiceTest extends TestCase
{
    use RefreshDatabase;

    protected StockService $stockService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->stockService = app(StockService::class);
    }

    /** @test */
    public function it_can_move_stock_between_warehouses()
    {
        // Arrange
        $item = Item::factory()->create();
        $fromWarehouse = Warehouse::factory()->create();
        $toWarehouse = Warehouse::factory()->create();

        // Create initial stock
        StockLevel::create([
            'item_id' => $item->id,
            'warehouse_id' => $fromWarehouse->id,
            'quantity_on_hand' => 100,
        ]);

        // Act
        $movement = $this->stockService->moveStock(
            $item,
            $fromWarehouse,
            $toWarehouse,
            50,
            'Transfer for testing'
        );

        // Assert
        $this->assertDatabaseHas('stock_movements', [
            'item_id' => $item->id,
            'warehouse_id' => $fromWarehouse->id,
            'movement_type' => 'out',
            'quantity' => 50,
        ]);

        $this->assertDatabaseHas('stock_movements', [
            'item_id' => $item->id,
            'warehouse_id' => $toWarehouse->id,
            'movement_type' => 'in',
            'quantity' => 50,
        ]);

        $fromStock = StockLevel::where('item_id', $item->id)
            ->where('warehouse_id', $fromWarehouse->id)
            ->first();
        $this->assertEquals(50, $fromStock->quantity_on_hand);

        $toStock = StockLevel::where('item_id', $item->id)
            ->where('warehouse_id', $toWarehouse->id)
            ->first();
        $this->assertEquals(50, $toStock->quantity_on_hand);
    }
}
```

### **7.2 Feature Testing**
```php
// tests/Feature/EggProductionTest.php
class EggProductionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_can_record_daily_egg_production()
    {
        // Arrange
        $user = User::factory()->create();
        $flock = Flock::factory()->create(['current_count' => 1000]);

        $productionData = [
            'flock_id' => $flock->id,
            'record_date' => now()->toDateString(),
            'age_weeks' => 25.5,
            'bird_count' => 1000,
            'eggs_collected' => 850,
            'eggs_good' => 800,
            'eggs_cracked' => 30,
            'eggs_dirty' => 20,
            'total_weight_kg' => 51.0,
            'mortality_count' => 2,
            'feed_consumed_kg' => 120.5,
            'temperature_min' => 22.5,
            'temperature_max' => 28.0,
            'humidity_percentage' => 65.0,
        ];

        // Act
        $response = $this->actingAs($user)
            ->post(route('egg-production.store'), $productionData);

        // Assert
        $response->assertRedirect(route('egg-production.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('egg_production_records', [
            'flock_id' => $flock->id,
            'eggs_collected' => 850,
            'laying_percentage' => 85.0, // Computed column
        ]);

        // Check flock population updated
        $flock->refresh();
        $this->assertEquals(998, $flock->current_count);
        $this->assertEquals(2, $flock->mortality_count);
    }
}
```

## 📱 MOBILE RESPONSIVENESS

### **8.1 Mobile-First Components**
```vue
<!-- resources/js/Components/Mobile/MobileNavigation.vue -->
<template>
  <nav class="lg:hidden">
    <!-- Mobile menu button -->
    <div class="flex items-center justify-between p-4 bg-white border-b">
      <h1 class="text-xl font-semibold">{{ title }}</h1>
      <button
        @click="showMenu = !showMenu"
        class="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
      >
        <Bars3Icon v-if="!showMenu" class="w-6 h-6" />
        <XMarkIcon v-else class="w-6 h-6" />
      </button>
    </div>

    <!-- Mobile menu -->
    <Transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition duration-75 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div v-show="showMenu" class="bg-white border-b shadow-lg">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <MobileNavItem
            v-for="item in navigation"
            :key="item.name"
            :item="item"
            @click="showMenu = false"
          />
        </div>
      </div>
    </Transition>
  </nav>
</template>
```

### **8.2 Touch-Optimized Data Entry**
```vue
<!-- resources/js/Components/Mobile/TouchDataEntry.vue -->
<template>
  <div class="space-y-4">
    <!-- Quick number input for mobile -->
    <div class="grid grid-cols-3 gap-2">
      <button
        v-for="number in [1, 2, 3, 4, 5, 6, 7, 8, 9, 0]"
        :key="number"
        @click="appendNumber(number)"
        class="h-12 text-lg font-semibold bg-gray-100 rounded-lg active:bg-gray-200 touch-manipulation"
      >
        {{ number }}
      </button>
      <button
        @click="clearInput"
        class="h-12 text-lg font-semibold bg-red-100 text-red-600 rounded-lg active:bg-red-200 touch-manipulation"
      >
        Clear
      </button>
    </div>

    <!-- Voice input for production data -->
    <button
      @click="startVoiceInput"
      :disabled="isListening"
      class="w-full h-12 bg-blue-600 text-white rounded-lg disabled:opacity-50 touch-manipulation"
    >
      <MicrophoneIcon class="w-5 h-5 mx-auto" />
      {{ isListening ? 'Listening...' : 'Voice Input' }}
    </button>

    <!-- Barcode scanner -->
    <button
      @click="openBarcodeScanner"
      class="w-full h-12 bg-green-600 text-white rounded-lg touch-manipulation"
    >
      <QrCodeIcon class="w-5 h-5 mx-auto" />
      Scan Barcode
    </button>
  </div>
</template>
```

## 📈 PERFORMANCE OPTIMIZATION

### **9.1 Database Optimization**
```php
// app/Console/Commands/OptimizeDatabase.php
class OptimizeDatabase extends Command
{
    protected $signature = 'db:optimize';
    protected $description = 'Optimize database performance';

    public function handle()
    {
        $this->info('Optimizing database...');

        // Analyze and optimize tables
        $tables = [
            'stock_movements',
            'egg_production_records',
            'feed_consumption_records',
            'journal_entries',
            'sales_orders',
        ];

        foreach ($tables as $table) {
            DB::statement("ANALYZE TABLE {$table}");
            DB::statement("OPTIMIZE TABLE {$table}");
            $this->line("Optimized table: {$table}");
        }

        // Update statistics
        DB::statement('ANALYZE TABLE stock_levels');

        $this->info('Database optimization completed!');
    }
}
```

### **9.2 Caching Strategy**
```php
// app/Services/CacheService.php
class CacheService
{
    public function getProductionSummary(int $flockId, string $period = '30d'): array
    {
        $cacheKey = "production_summary_{$flockId}_{$period}";

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($flockId, $period) {
            return EggProductionRecord::where('flock_id', $flockId)
                ->where('record_date', '>=', now()->sub($period))
                ->selectRaw('
                    SUM(eggs_collected) as total_eggs,
                    AVG(laying_percentage) as avg_laying_rate,
                    SUM(feed_consumed_kg) as total_feed,
                    AVG(feed_consumed_kg / eggs_collected * 1000) as avg_fcr
                ')
                ->first()
                ->toArray();
        });
    }

    public function invalidateProductionCache(int $flockId): void
    {
        $patterns = [
            "production_summary_{$flockId}_*",
            "house_analytics_*",
            "farm_dashboard_*"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }
}
```

## 🗓️ IMPLEMENTATION ROADMAP

### **Phase 1: Foundation (Week 1-2)**
- ✅ Laravel 10 setup dengan modular architecture
- ✅ Core authentication & authorization system
- ✅ Modern UI components dengan Tailwind CSS
- ✅ Database migration & seeding
- ✅ Basic module management system

### **Phase 2: Core Modules (Week 3-5)**
- ✅ User Management module
- ✅ Inventory Management module
- ✅ Basic accounting integration
- ✅ API endpoints untuk mobile
- ✅ Unit testing setup

### **Phase 3: Business Modules (Week 6-8)**
- ✅ Sales & Customer management
- ✅ Purchasing & Supplier management
- ✅ Poultry Management module
- ✅ Egg Production module
- ✅ Feed Management module

### **Phase 4: Advanced Features (Week 9-11)**
- 📋 Advanced analytics & reporting
- 📋 Mobile app development
- 📋 IoT sensor integration
- 📋 Automated alerts & notifications
- 📋 Performance optimization

### **Phase 5: Production & Scale (Week 12-14)**
- 📋 Production deployment
- 📋 Load testing & optimization
- 📋 User training & documentation
- 📋 Monitoring & maintenance setup
- 📋 Backup & disaster recovery

## 💡 BEST PRACTICES

### **Code Quality**
- PSR-12 coding standards
- 90%+ test coverage
- Type hints & strict typing
- Comprehensive documentation
- Code review process

### **Security**
- OWASP security guidelines
- Regular security audits
- Input validation & sanitization
- SQL injection prevention
- XSS protection

### **Performance**
- Database query optimization
- Caching strategy implementation
- Asset optimization
- CDN integration
- Monitoring & alerting

---

## 🎯 LEAN & REUSABLE CODING PRINCIPLES

### **Best Practices Implementation**

#### **1. Repository Pattern with Service Layer**
```php
// app/Shared/Contracts/RepositoryInterface.php
interface RepositoryInterface
{
    public function find(int $id);
    public function findOrFail(int $id);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id): bool;
    public function paginate(int $perPage = 15);
    public function filter(array $filters);
}

// app/Shared/Repositories/BaseRepository.php
abstract class BaseRepository implements RepositoryInterface
{
    protected Model $model;

    public function __construct()
    {
        $this->model = app($this->getModelClass());
    }

    public function find(int $id)
    {
        return $this->model->find($id);
    }

    public function findOrFail(int $id)
    {
        return $this->model->findOrFail($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data)
    {
        $model = $this->findOrFail($id);
        $model->update($data);
        return $model;
    }

    public function delete(int $id): bool
    {
        return $this->findOrFail($id)->delete();
    }

    public function paginate(int $perPage = 15)
    {
        return $this->model->paginate($perPage);
    }

    public function filter(array $filters)
    {
        $query = $this->model->newQuery();

        foreach ($filters as $field => $value) {
            if (method_exists($this, "filter{$field}")) {
                $this->{"filter{$field}"}($query, $value);
            }
        }

        return $query;
    }

    abstract protected function getModelClass(): string;
}
```

#### **2. React Custom Hooks Pattern**
```typescript
// frontend/src/hooks/useApi.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

interface UseApiOptions<T> {
  queryKey: string[];
  queryFn: () => Promise<T>;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  enabled?: boolean;
  staleTime?: number;
}

export function useApi<T>(options: UseApiOptions<T>) {
  return useQuery({
    queryKey: options.queryKey,
    queryFn: options.queryFn,
    onSuccess: options.onSuccess,
    onError: options.onError,
    enabled: options.enabled,
    staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes default
  });
}

export function useApiMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: {
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: Error, variables: TVariables) => void;
    invalidateQueries?: string[][];
  }
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    onSuccess: (data, variables) => {
      options?.onSuccess?.(data, variables);
      options?.invalidateQueries?.forEach(queryKey => {
        queryClient.invalidateQueries(queryKey);
      });
      toast.success('Operation completed successfully');
    },
    onError: (error, variables) => {
      options?.onError?.(error, variables);
      toast.error(error.message || 'Operation failed');
    },
  });
}

// Usage example
export function useUsers(filters: UserFilters) {
  return useApi({
    queryKey: ['users', filters],
    queryFn: () => userService.getUsers(filters),
  });
}

export function useCreateUser() {
  return useApiMutation(
    userService.createUser,
    {
      invalidateQueries: [['users']],
    }
  );
}
```

#### **3. Reusable Form Components**
```typescript
// frontend/src/components/forms/FormField.tsx
interface FormFieldProps {
  label: string;
  name: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  error,
  required,
  children
}) => {
  return (
    <div className="space-y-1">
      <label htmlFor={name} className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {children}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

// Usage with react-hook-form
export const UserForm: React.FC<UserFormProps> = ({ user, onSave }) => {
  const { register, handleSubmit, formState: { errors } } = useForm<UserFormData>({
    defaultValues: user,
    resolver: zodResolver(userSchema)
  });

  return (
    <form onSubmit={handleSubmit(onSave)} className="space-y-4">
      <FormField
        label="Username"
        name="username"
        required
        error={errors.username?.message}
      >
        <Input
          {...register('username')}
          placeholder="Enter username"
        />
      </FormField>

      <FormField
        label="Email"
        name="email"
        required
        error={errors.email?.message}
      >
        <Input
          type="email"
          {...register('email')}
          placeholder="Enter email"
        />
      </FormField>
    </form>
  );
};
```

## 📋 IMPLEMENTATION CHECKLIST

### **✅ Docker Ready**
- [x] Multi-stage Dockerfile for production
- [x] Docker Compose with all services
- [x] Health checks and monitoring
- [x] Volume management for data persistence
- [x] Environment-based configuration

### **✅ REST API Ready**
- [x] Laravel Sanctum authentication
- [x] OpenAPI/Swagger documentation
- [x] Consistent API response format
- [x] Rate limiting and throttling
- [x] CORS configuration
- [x] API versioning support

### **✅ React Starter Kit**
- [x] TypeScript configuration
- [x] Modern React 18 with hooks
- [x] Zustand for state management
- [x] React Query for server state
- [x] React Router for navigation
- [x] Tailwind CSS + Shadcn/ui components

### **✅ RBAC Authentication**
- [x] Role-based access control
- [x] Permission-based authorization
- [x] Module-level access control
- [x] API token management
- [x] Session management
- [x] Activity logging

### **✅ Social Login**
- [x] Google OAuth integration
- [x] Facebook login support
- [x] GitHub authentication
- [x] LinkedIn integration
- [x] Automatic user creation
- [x] Social account linking

### **✅ Lean & Reusable Coding**
- [x] Repository pattern implementation
- [x] Service layer architecture
- [x] Reusable React components
- [x] Custom hooks for API calls
- [x] Shared utilities and helpers
- [x] Consistent coding standards

## 🚀 QUICK START GUIDE

### **1. Clone & Setup**
```bash
git clone https://github.com/your-org/erp-poultry.git
cd erp-poultry
cp .env.example .env
```

### **2. Docker Development**
```bash
# Start all services
docker-compose up -d

# Install dependencies
docker-compose exec app composer install
docker-compose exec app npm install

# Run migrations
docker-compose exec app php artisan migrate --seed

# Generate API documentation
docker-compose exec app php artisan l5-swagger:generate
```

### **3. Frontend Development**
```bash
cd frontend
npm install
npm run dev
```

### **4. Access Application**
- **Frontend**: http://localhost:3000
- **API**: http://localhost:8000/api
- **API Docs**: http://localhost:8000/api/documentation
- **Database**: localhost:3306

---

**KESIMPULAN**: Implementasi ini memberikan foundation yang solid dan production-ready untuk sistem ERP Peternakan Ayam dengan:

🐳 **Docker-ready** - Containerized deployment dengan multi-stage builds
🔌 **REST API-ready** - Comprehensive API dengan authentication & documentation
⚛️ **React starter kit** - Modern frontend dengan TypeScript & best practices
🔐 **RBAC authentication** - Role-based access control dengan social login
🧩 **Lean & reusable** - Clean architecture dengan reusable components
📱 **Mobile-responsive** - Progressive Web App dengan offline capabilities
🚀 **Production-ready** - Optimized untuk performance dan scalability
