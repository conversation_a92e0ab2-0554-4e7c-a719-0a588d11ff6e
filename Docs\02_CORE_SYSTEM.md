# 02 - <PERSON><PERSON> SYSTEM IMPLEMENTATION

## 📋 OVERVIEW

File ini berisi implementasi core system yang menjadi foundation untuk semua modul ERP. Core system mencakup authentication, authorization (RBAC), module management, API base classes, dan shared components.

## 🎯 TUJUAN

- Implementasi authentication dengan Laravel Sanctum
- Setup RBAC (Role-Based Access Control) dengan Spatie Permission
- Membuat base classes untuk API controllers
- Setup module management system
- Implementasi social login (Google, Facebook)
- Membuat shared components untuk frontend

## ⏱️ ESTIMASI WAKTU

**Total**: 16-20 jam
- Backend core: 10-12 jam
- Frontend core: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Lead** (Lead)
- **Frontend Lead**
- **DevOps Engineer** (Support)

## 🗄️ DATABASE SCHEMA

Core system menggunakan tabel-tabel berikut dari `erp_poultry_modular_database.sql`:

```sql
-- Core tables
system_config
modules
module_permissions
event_logs
audit_trails

-- Authentication tables
users
roles
permissions
user_roles
role_permissions
user_sessions
password_reset_tokens
personal_access_tokens

-- Master data tables
companies
regions
units
```

## 🔧 STEP 1: BACKEND CORE IMPLEMENTATION

### **1.1 Create Core Directory Structure**

```bash
# Create core directory structure
mkdir -p app/Core/{Auth,RBAC,API,Services,Events,Middleware}
mkdir -p app/Core/Auth/{Controllers,Services,Traits}
mkdir -p app/Core/RBAC/{Models,Policies,Middleware}
mkdir -p app/Core/API/{Controllers,Resources,Middleware,Traits}
```

### **1.2 Base API Controller**

```php
<?php
// app/Core/API/Controllers/BaseApiController.php

namespace App\Core\API\Controllers;

use App\Http\Controllers\Controller;
use App\Core\API\Traits\ApiResponseTrait;
use App\Core\API\Traits\ValidationTrait;
use App\Core\API\Traits\FilterTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

abstract class BaseApiController extends Controller
{
    use ApiResponseTrait, ValidationTrait, FilterTrait;

    protected int $perPage = 15;
    protected array $allowedIncludes = [];
    protected array $allowedFilters = [];
    protected array $allowedSorts = [];

    public function index(Request $request)
    {
        $this->authorize('viewAny', $this->getModelClass());
        
        $query = $this->getModelClass()::query();
        
        // Apply filters
        $query = $this->applyFilters($query, $request);
        
        // Apply includes
        $query = $this->applyIncludes($query, $request);
        
        // Apply sorting
        $query = $this->applySorting($query, $request);
        
        $items = $query->paginate($request->get('per_page', $this->perPage));
        
        return $this->successResponse([
            'data' => $this->getResourceClass()::collection($items->items()),
            'meta' => $this->getPaginationMeta($items)
        ]);
    }

    public function show(Request $request, int $id)
    {
        $item = $this->findOrFail($id);
        $this->authorize('view', $item);
        
        $query = $this->getModelClass()::where('id', $id);
        $query = $this->applyIncludes($query, $request);
        $item = $query->first();
        
        return $this->successResponse([
            'data' => new ($this->getResourceClass())($item)
        ]);
    }

    public function store(Request $request)
    {
        $this->authorize('create', $this->getModelClass());
        
        $validated = $this->validateRequest($request, $this->getStoreRules());
        
        $item = DB::transaction(function () use ($validated) {
            return $this->performStore($validated);
        });
        
        return $this->successResponse([
            'data' => new ($this->getResourceClass())($item),
            'message' => 'Created successfully'
        ], 201);
    }

    public function update(Request $request, int $id)
    {
        $item = $this->findOrFail($id);
        $this->authorize('update', $item);
        
        $validated = $this->validateRequest($request, $this->getUpdateRules($item));
        
        $item = DB::transaction(function () use ($item, $validated) {
            return $this->performUpdate($item, $validated);
        });
        
        return $this->successResponse([
            'data' => new ($this->getResourceClass())($item),
            'message' => 'Updated successfully'
        ]);
    }

    public function destroy(int $id)
    {
        $item = $this->findOrFail($id);
        $this->authorize('delete', $item);
        
        DB::transaction(function () use ($item) {
            $this->performDestroy($item);
        });
        
        return $this->successResponse([
            'message' => 'Deleted successfully'
        ]);
    }

    // Abstract methods to be implemented by child controllers
    abstract protected function getModelClass(): string;
    abstract protected function getResourceClass(): string;
    abstract protected function getStoreRules(): array;
    abstract protected function getUpdateRules($item): array;
    abstract protected function performStore(array $data);
    abstract protected function performUpdate($item, array $data);
    abstract protected function performDestroy($item): void;

    protected function findOrFail(int $id)
    {
        return $this->getModelClass()::findOrFail($id);
    }
}
```

### **1.3 API Response Trait**

```php
<?php
// app/Core/API/Traits/ApiResponseTrait.php

namespace App\Core\API\Traits;

use Illuminate\Http\JsonResponse;

trait ApiResponseTrait
{
    protected function successResponse(array $data, int $status = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data['data'] ?? null,
            'message' => $data['message'] ?? null,
            'meta' => $data['meta'] ?? null
        ], $status);
    }

    protected function errorResponse(string $message, int $status = 400, array $errors = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $status);
    }

    protected function validationErrorResponse(array $errors): JsonResponse
    {
        return $this->errorResponse('Validation failed', 422, $errors);
    }

    protected function getPaginationMeta($paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem()
        ];
    }
}
```

### **1.4 Authentication Controller**

```php
<?php
// app/Core/Auth/Controllers/AuthController.php

namespace App\Core\Auth\Controllers;

use App\Http\Controllers\Controller;
use App\Core\Auth\Traits\SocialAuthTrait;
use App\Core\RBAC\Traits\RBACTrait;
use App\Core\API\Traits\ApiResponseTrait;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;

class AuthController extends Controller
{
    use ApiResponseTrait, SocialAuthTrait, RBACTrait;

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
            'remember' => 'boolean'
        ]);

        $credentials = $request->only('email', 'password');
        
        if (Auth::attempt($credentials, $request->remember)) {
            $user = Auth::user();
            
            // Check if user has required permissions
            if (!$this->hasModuleAccess($user, 'core')) {
                return $this->errorResponse('Access denied', 403);
            }
            
            $token = $user->createToken('auth-token', $this->getUserAbilities($user))->plainTextToken;
            
            // Log activity
            activity()
                ->causedBy($user)
                ->withProperties(['ip' => $request->ip()])
                ->log('User logged in via API');
            
            return $this->successResponse([
                'user' => $user->load('roles.permissions'),
                'token' => $token,
                'abilities' => $this->getUserAbilities($user),
                'modules' => $this->getAvailableModules($user)
            ]);
        }
        
        return $this->errorResponse('Invalid credentials', 401);
    }

    public function register(Request $request)
    {
        $request->validate([
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
        ]);

        $user = User::create([
            'uuid' => \Str::uuid(),
            'username' => $request->username,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
        ]);

        // Assign default role
        $user->assignRole('viewer');

        $token = $user->createToken('auth-token', $this->getUserAbilities($user))->plainTextToken;

        return $this->successResponse([
            'user' => $user->load('roles.permissions'),
            'token' => $token,
            'abilities' => $this->getUserAbilities($user),
            'modules' => $this->getAvailableModules($user)
        ], 201);
    }

    public function socialLogin(string $provider)
    {
        $validProviders = ['google', 'facebook', 'github'];
        
        if (!in_array($provider, $validProviders)) {
            return $this->errorResponse('Invalid provider', 400);
        }

        return Socialite::driver($provider)
            ->stateless()
            ->redirect();
    }

    public function socialCallback(string $provider, Request $request)
    {
        try {
            $socialUser = Socialite::driver($provider)->stateless()->user();
            
            $user = $this->findOrCreateSocialUser($socialUser, $provider);
            
            $token = $user->createToken('social-auth-token', $this->getUserAbilities($user))->plainTextToken;
            
            return $this->successResponse([
                'user' => $user->load('roles.permissions'),
                'token' => $token,
                'abilities' => $this->getUserAbilities($user),
                'modules' => $this->getAvailableModules($user)
            ]);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Social login failed: ' . $e->getMessage(), 400);
        }
    }

    public function me(Request $request)
    {
        $user = $request->user()->load('roles.permissions');
        
        return $this->successResponse([
            'user' => $user,
            'abilities' => $this->getUserAbilities($user),
            'modules' => $this->getAvailableModules($user)
        ]);
    }

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        
        activity()
            ->causedBy($request->user())
            ->log('User logged out');
            
        return $this->successResponse(['message' => 'Logged out successfully']);
    }

    public function refreshToken(Request $request)
    {
        $user = $request->user();
        
        // Delete current token
        $request->user()->currentAccessToken()->delete();
        
        // Create new token
        $token = $user->createToken('refresh-token', $this->getUserAbilities($user))->plainTextToken;
        
        return $this->successResponse([
            'token' => $token,
            'abilities' => $this->getUserAbilities($user)
        ]);
    }
}
```

### **1.5 RBAC Trait**

```php
<?php
// app/Core/RBAC/Traits/RBACTrait.php

namespace App\Core\RBAC\Traits;

use App\Models\User;
use Nwidart\Modules\Facades\Module;

trait RBACTrait
{
    public function hasModuleAccess(User $user, string $module): bool
    {
        return $user->hasPermissionTo("{$module}.access") || $user->hasRole('super_admin');
    }

    public function getUserAbilities(User $user): array
    {
        $abilities = $user->getAllPermissions()->pluck('name')->toArray();
        
        // Add role-based abilities
        foreach ($user->roles as $role) {
            $abilities[] = "role:{$role->name}";
        }
        
        return array_unique($abilities);
    }

    public function getAvailableModules(User $user): array
    {
        $modules = Module::allEnabled();
        
        return collect($modules)->filter(function ($module) use ($user) {
            return $this->hasModuleAccess($user, $module->getName());
        })->map(function ($module) {
            $config = $module->json();
            return [
                'name' => $module->getName(),
                'display_name' => $config->get('display_name', $module->getName()),
                'icon' => $config->get('icon', 'CubeIcon'),
                'route' => $config->get('route', "/{$module->getName()}")
            ];
        })->values()->toArray();
    }
}
```

## ⚛️ STEP 2: FRONTEND CORE IMPLEMENTATION

### **2.1 Create Frontend Core Structure**

```bash
# Create frontend core structure
mkdir -p frontend/src/core/{auth,api,components,hooks,stores,types,utils}
mkdir -p frontend/src/components/ui
```

### **2.2 API Client Setup**

```typescript
// frontend/src/core/api/apiClient.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useAuth } from '../stores/authStore';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const token = useAuth.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, logout user
          useAuth.getState().logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### **2.3 Authentication Store (Zustand)**

```typescript
// frontend/src/core/stores/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '../api/apiClient';

interface User {
  id: number;
  uuid: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar?: string;
  roles: Role[];
}

interface Role {
  id: number;
  name: string;
  display_name: string;
}

interface Module {
  name: string;
  display_name: string;
  icon: string;
  route: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  abilities: string[];
  modules: Module[];
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  socialLogin: (provider: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasModuleAccess: (module: string) => boolean;
}

interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirmation: string;
  first_name: string;
  last_name: string;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      abilities: [],
      modules: [],
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials) => {
        set({ isLoading: true });
        try {
          const response = await apiClient.post('/auth/login', credentials);
          set({
            user: response.data.user,
            token: response.data.token,
            abilities: response.data.abilities,
            modules: response.data.modules,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (userData) => {
        set({ isLoading: true });
        try {
          const response = await apiClient.post('/auth/register', userData);
          set({
            user: response.data.user,
            token: response.data.token,
            abilities: response.data.abilities,
            modules: response.data.modules,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      socialLogin: async (provider) => {
        set({ isLoading: true });
        try {
          // Redirect to social login
          window.location.href = `${process.env.REACT_APP_API_URL}/auth/social/${provider}`;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        try {
          await apiClient.post('/auth/logout');
        } finally {
          set({
            user: null,
            token: null,
            abilities: [],
            modules: [],
            isAuthenticated: false
          });
        }
      },

      checkAuth: async () => {
        const { token } = get();
        if (!token) return;

        try {
          const response = await apiClient.get('/auth/me');
          set({
            user: response.data.user,
            abilities: response.data.abilities,
            modules: response.data.modules,
            isAuthenticated: true
          });
        } catch (error) {
          set({
            user: null,
            token: null,
            abilities: [],
            modules: [],
            isAuthenticated: false
          });
        }
      },

      hasPermission: (permission) => {
        const { abilities } = get();
        return abilities.includes(permission) || abilities.includes('role:super_admin');
      },

      hasRole: (role) => {
        const { abilities } = get();
        return abilities.includes(`role:${role}`);
      },

      hasModuleAccess: (module) => {
        const { modules } = get();
        return modules.some(m => m.name === module);
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        abilities: state.abilities,
        modules: state.modules,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
```

## 🔗 STEP 3: API ROUTES SETUP

### **3.1 API Routes Configuration**

```php
<?php
// routes/api.php

use App\Core\Auth\Controllers\AuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('v1')->group(function () {
    // Public routes
    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
        Route::get('social/{provider}', [AuthController::class, 'socialLogin']);
        Route::get('social/{provider}/callback', [AuthController::class, 'socialCallback']);
    });

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::prefix('auth')->group(function () {
            Route::get('me', [AuthController::class, 'me']);
            Route::post('logout', [AuthController::class, 'logout']);
            Route::post('refresh', [AuthController::class, 'refreshToken']);
        });

        // Module routes will be added here
        // Each module will register its own routes
    });
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend Core**
- [ ] Base API controller created and working
- [ ] Authentication endpoints functional
- [ ] RBAC traits implemented
- [ ] Social login configured
- [ ] API responses consistent
- [ ] Error handling implemented

### **Frontend Core**
- [ ] API client configured
- [ ] Authentication store working
- [ ] Token management functional
- [ ] Permission checking working
- [ ] Social login integration

### **Integration**
- [ ] Frontend can authenticate with backend
- [ ] Token refresh working
- [ ] Permission-based UI rendering
- [ ] Social login flow complete

## 📞 NEXT STEPS

Setelah core system selesai:

1. **Test authentication flow** end-to-end
2. **Verify RBAC permissions** working correctly
3. **Test social login** dengan provider yang dikonfigurasi
4. **Commit core system** ke repository
5. **Lanjut ke** `03_USER_MANAGEMENT.md` untuk implementasi user management module

---

**IMPORTANT**: Core system adalah foundation untuk semua modul lainnya. Pastikan semua functionality bekerja dengan baik sebelum melanjutkan ke modul berikutnya.
