# 11 - HEALTH MANAGEMENT MODULE

## 📋 OVERVIEW

Modul Health Management mengelola seluruh aspek kesehatan ternak dari vaccination programs, health monitoring, disease management, hingga veterinary records. Modul ini terintegrasi dengan poultry management untuk flock health tracking dan production untuk health impact analysis.

## 🎯 TUJUAN

- Vaccination program management dengan scheduling
- Health monitoring dan disease surveillance
- Veterinary treatment records dan medication tracking
- Mortality analysis dan disease outbreak management
- Health performance correlation dengan production
- Compliance tracking untuk health regulations
- Integration dengan production untuk health impact analysis

## ⏱️ ESTIMASI WAKTU

**Total**: 18-22 jam
- Backend implementation: 12-14 jam
- Frontend implementation: 6-8 jam

## 👥 TIM YANG TERLIBAT

- **Backend Developer** (Lead)
- **Frontend Developer**
- **Veterinarian** (Consultant)

## 🗄️ DATABASE SCHEMA

Modul ini menggunakan tabel-tabel berikut:

```sql
-- Health programs
vaccination_programs
vaccination_schedules
health_protocols

-- Health records
health_records
vaccination_records
treatment_records
medication_records

-- Disease management
diseases
disease_outbreaks
mortality_records

-- Supporting tables
veterinarians
medications
vaccines
```

## 🔧 STEP 1: BACKEND IMPLEMENTATION

### **1.1 Create Module Structure**

```bash
# Create Health Management module
php artisan module:make HealthManagement

# Create module components
php artisan module:make-controller HealthManagement VaccinationController --api
php artisan module:make-controller HealthManagement HealthRecordController --api
php artisan module:make-controller HealthManagement TreatmentController --api
php artisan module:make-controller HealthManagement DiseaseController --api
php artisan module:make-controller HealthManagement HealthReportController --api
php artisan module:make-model HealthManagement VaccinationProgram
php artisan module:make-model HealthManagement HealthRecord
php artisan module:make-model HealthManagement TreatmentRecord
php artisan module:make-model HealthManagement DiseaseOutbreak
php artisan module:make-request HealthManagement VaccinationStoreRequest
php artisan module:make-request HealthManagement HealthRecordStoreRequest
php artisan module:make-resource HealthManagement VaccinationResource
php artisan module:make-resource HealthManagement HealthRecordResource
php artisan module:make-policy HealthManagement HealthManagementPolicy
php artisan module:make-seeder HealthManagement HealthManagementSeeder
```

### **1.2 Health Record Model**

```php
<?php
// Modules/HealthManagement/Entities/HealthRecord.php

namespace Modules\HealthManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class HealthRecord extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'record_date',
        'flock_id',
        'house_id',
        'inspection_type',
        'inspector_id',
        'bird_count_inspected',
        'healthy_birds',
        'sick_birds',
        'mortality_count',
        'culled_count',
        'symptoms_observed',
        'diseases_suspected',
        'body_condition_score',
        'behavior_assessment',
        'appetite_level',
        'water_consumption_normal',
        'egg_production_normal',
        'environmental_factors',
        'temperature',
        'humidity',
        'air_quality',
        'cleanliness_score',
        'biosecurity_compliance',
        'recommendations',
        'follow_up_required',
        'follow_up_date',
        'notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'record_date' => 'date',
        'bird_count_inspected' => 'integer',
        'healthy_birds' => 'integer',
        'sick_birds' => 'integer',
        'mortality_count' => 'integer',
        'culled_count' => 'integer',
        'symptoms_observed' => 'array',
        'diseases_suspected' => 'array',
        'body_condition_score' => 'decimal:1',
        'appetite_level' => 'decimal:1',
        'water_consumption_normal' => 'boolean',
        'egg_production_normal' => 'boolean',
        'environmental_factors' => 'array',
        'temperature' => 'decimal:1',
        'humidity' => 'decimal:1',
        'air_quality' => 'decimal:1',
        'cleanliness_score' => 'decimal:1',
        'biosecurity_compliance' => 'decimal:1',
        'follow_up_required' => 'boolean',
        'follow_up_date' => 'date',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['sick_birds', 'mortality_count', 'status', 'diseases_suspected'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function flock()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\Flock::class);
    }

    public function house()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\House::class);
    }

    public function inspector()
    {
        return $this->belongsTo(\App\Models\User::class, 'inspector_id');
    }

    public function treatmentRecords()
    {
        return $this->hasMany(TreatmentRecord::class, 'health_record_id');
    }

    public function vaccinationRecords()
    {
        return $this->hasMany(VaccinationRecord::class, 'health_record_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('record_date', [$startDate, $endDate]);
    }

    public function scopeByFlock($query, $flockId)
    {
        return $query->where('flock_id', $flockId);
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeWithIssues($query)
    {
        return $query->where(function ($q) {
            $q->where('sick_birds', '>', 0)
              ->orWhere('mortality_count', '>', 0)
              ->orWhereNotNull('diseases_suspected');
        });
    }

    public function scopeRequiringFollowUp($query)
    {
        return $query->where('follow_up_required', true)
            ->where('status', '!=', 'completed');
    }

    // Accessors
    public function getHealthPercentageAttribute(): float
    {
        return $this->bird_count_inspected > 0 
            ? ($this->healthy_birds / $this->bird_count_inspected) * 100 
            : 0;
    }

    public function getMortalityRateAttribute(): float
    {
        return $this->bird_count_inspected > 0 
            ? ($this->mortality_count / $this->bird_count_inspected) * 100 
            : 0;
    }

    public function getMorbidityRateAttribute(): float
    {
        return $this->bird_count_inspected > 0 
            ? ($this->sick_birds / $this->bird_count_inspected) * 100 
            : 0;
    }

    public function getOverallHealthScoreAttribute(): float
    {
        $scores = [
            $this->body_condition_score,
            $this->behavior_assessment,
            $this->appetite_level,
            $this->cleanliness_score,
            $this->biosecurity_compliance,
        ];

        $validScores = array_filter($scores, function ($score) {
            return !is_null($score);
        });

        return count($validScores) > 0 ? array_sum($validScores) / count($validScores) : 0;
    }

    public function getRiskLevelAttribute(): string
    {
        if ($this->mortality_rate > 5 || $this->morbidity_rate > 10) {
            return 'high';
        } elseif ($this->mortality_rate > 2 || $this->morbidity_rate > 5) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    // Methods
    public function updateFlockHealth(): void
    {
        if ($this->mortality_count > 0 || $this->culled_count > 0) {
            $this->flock->updatePopulation(
                $this->mortality_count,
                $this->culled_count,
                0
            );
        }
    }

    public function createTreatmentIfNeeded(): void
    {
        if ($this->sick_birds > 0 && !empty($this->diseases_suspected)) {
            foreach ($this->diseases_suspected as $disease) {
                TreatmentRecord::create([
                    'uuid' => \Str::uuid(),
                    'health_record_id' => $this->id,
                    'flock_id' => $this->flock_id,
                    'disease_id' => $disease['id'] ?? null,
                    'treatment_date' => $this->record_date,
                    'birds_affected' => $this->sick_birds,
                    'treatment_type' => 'medication',
                    'status' => 'pending',
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                ]);
            }
        }
    }

    public static function getHealthSummary(string $date = null): array
    {
        $date = $date ?: now()->toDateString();
        
        $records = static::with(['flock', 'house'])
            ->where('record_date', $date)
            ->get();

        return [
            'date' => $date,
            'total_inspections' => $records->count(),
            'total_birds_inspected' => $records->sum('bird_count_inspected'),
            'total_healthy_birds' => $records->sum('healthy_birds'),
            'total_sick_birds' => $records->sum('sick_birds'),
            'total_mortality' => $records->sum('mortality_count'),
            'average_health_percentage' => $records->avg('health_percentage'),
            'average_mortality_rate' => $records->avg('mortality_rate'),
            'average_morbidity_rate' => $records->avg('morbidity_rate'),
            'high_risk_flocks' => $records->where('risk_level', 'high')->count(),
            'follow_ups_required' => $records->where('follow_up_required', true)->count(),
        ];
    }
}
```

### **1.3 Vaccination Record Model**

```php
<?php
// Modules/HealthManagement/Entities/VaccinationRecord.php

namespace Modules\HealthManagement\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class VaccinationRecord extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'uuid',
        'vaccination_date',
        'flock_id',
        'house_id',
        'vaccination_program_id',
        'vaccine_id',
        'batch_number',
        'expiry_date',
        'birds_vaccinated',
        'dosage_per_bird',
        'total_dosage',
        'administration_method',
        'vaccination_age_weeks',
        'veterinarian_id',
        'vaccinator_id',
        'vaccine_temperature',
        'storage_conditions_met',
        'adverse_reactions',
        'reaction_count',
        'efficacy_expected',
        'next_vaccination_date',
        'notes',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'vaccination_date' => 'date',
        'expiry_date' => 'date',
        'birds_vaccinated' => 'integer',
        'dosage_per_bird' => 'decimal:3',
        'total_dosage' => 'decimal:3',
        'vaccination_age_weeks' => 'decimal:1',
        'vaccine_temperature' => 'decimal:1',
        'storage_conditions_met' => 'boolean',
        'adverse_reactions' => 'array',
        'reaction_count' => 'integer',
        'efficacy_expected' => 'decimal:1',
        'next_vaccination_date' => 'date',
    ];

    // Activity logging
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['birds_vaccinated', 'status', 'adverse_reactions'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    // Relationships
    public function flock()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\Flock::class);
    }

    public function house()
    {
        return $this->belongsTo(\Modules\PoultryManagement\Entities\House::class);
    }

    public function vaccinationProgram()
    {
        return $this->belongsTo(VaccinationProgram::class, 'vaccination_program_id');
    }

    public function vaccine()
    {
        return $this->belongsTo(Vaccine::class);
    }

    public function veterinarian()
    {
        return $this->belongsTo(Veterinarian::class);
    }

    public function vaccinator()
    {
        return $this->belongsTo(\App\Models\User::class, 'vaccinator_id');
    }

    public function healthRecord()
    {
        return $this->belongsTo(HealthRecord::class, 'health_record_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('vaccination_date', [$startDate, $endDate]);
    }

    public function scopeByFlock($query, $flockId)
    {
        return $query->where('flock_id', $flockId);
    }

    public function scopeByVaccine($query, $vaccineId)
    {
        return $query->where('vaccine_id', $vaccineId);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeDueForNext($query)
    {
        return $query->whereNotNull('next_vaccination_date')
            ->where('next_vaccination_date', '<=', now()->addDays(7));
    }

    // Accessors
    public function getVaccinationCoverageAttribute(): float
    {
        $totalBirds = $this->flock->current_count;
        return $totalBirds > 0 ? ($this->birds_vaccinated / $totalBirds) * 100 : 0;
    }

    public function getAdverseReactionRateAttribute(): float
    {
        return $this->birds_vaccinated > 0 
            ? ($this->reaction_count / $this->birds_vaccinated) * 100 
            : 0;
    }

    public function getCostPerBirdAttribute(): float
    {
        $vaccineCost = $this->vaccine->cost_per_dose ?? 0;
        return $vaccineCost * $this->dosage_per_bird;
    }

    public function getTotalCostAttribute(): float
    {
        return $this->cost_per_bird * $this->birds_vaccinated;
    }

    // Methods
    public function scheduleNextVaccination(): void
    {
        if ($this->vaccinationProgram && $this->vaccinationProgram->interval_weeks > 0) {
            $nextDate = $this->vaccination_date->addWeeks($this->vaccinationProgram->interval_weeks);
            $this->update(['next_vaccination_date' => $nextDate]);
        }
    }

    public function recordAdverseReaction(array $reactionData): void
    {
        $reactions = $this->adverse_reactions ?: [];
        $reactions[] = array_merge($reactionData, [
            'recorded_at' => now()->toISOString(),
            'recorded_by' => auth()->id(),
        ]);

        $this->update([
            'adverse_reactions' => $reactions,
            'reaction_count' => $this->reaction_count + ($reactionData['bird_count'] ?? 1),
        ]);
    }

    public static function getVaccinationSchedule(int $flockId, string $startDate, string $endDate): array
    {
        $flock = \Modules\PoultryManagement\Entities\Flock::findOrFail($flockId);
        $program = VaccinationProgram::where('target_species', $flock->breed->type)
            ->where('life_stage', $flock->production_stage)
            ->where('is_active', true)
            ->first();

        if (!$program) {
            return [];
        }

        $schedule = [];
        $currentDate = now()->parse($startDate);
        $endDate = now()->parse($endDate);

        while ($currentDate <= $endDate) {
            $flockAgeWeeks = $flock->placement_date->diffInWeeks($currentDate);
            
            $dueVaccinations = $program->schedules()
                ->where('age_weeks_min', '<=', $flockAgeWeeks)
                ->where('age_weeks_max', '>=', $flockAgeWeeks)
                ->whereDoesntHave('vaccinationRecords', function ($query) use ($flock, $currentDate) {
                    $query->where('flock_id', $flock->id)
                        ->where('vaccination_date', $currentDate->toDateString());
                })
                ->get();

            foreach ($dueVaccinations as $vaccination) {
                $schedule[] = [
                    'date' => $currentDate->toDateString(),
                    'flock_age_weeks' => $flockAgeWeeks,
                    'vaccine_name' => $vaccination->vaccine->name,
                    'vaccine_id' => $vaccination->vaccine_id,
                    'dosage_per_bird' => $vaccination->dosage_per_bird,
                    'administration_method' => $vaccination->administration_method,
                    'notes' => $vaccination->notes,
                ];
            }

            $currentDate->addDay();
        }

        return $schedule;
    }
}
```

### **1.4 Health Management Service**

```php
<?php
// Modules/HealthManagement/Services/HealthManagementService.php

namespace Modules\HealthManagement\Services;

use Modules\HealthManagement\Entities\HealthRecord;
use Modules\HealthManagement\Entities\VaccinationRecord;
use Modules\HealthManagement\Entities\TreatmentRecord;
use Modules\HealthManagement\Entities\DiseaseOutbreak;
use Modules\HealthManagement\Events\HealthIssueDetected;
use Modules\HealthManagement\Events\VaccinationCompleted;
use Modules\HealthManagement\Events\DiseaseOutbreakDeclared;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class HealthManagementService
{
    public function recordHealthInspection(array $data): HealthRecord
    {
        return DB::transaction(function () use ($data) {
            $record = HealthRecord::create([
                'uuid' => Str::uuid(),
                'record_date' => $data['record_date'],
                'flock_id' => $data['flock_id'],
                'house_id' => $data['house_id'],
                'inspection_type' => $data['inspection_type'],
                'inspector_id' => $data['inspector_id'] ?? auth()->id(),
                'bird_count_inspected' => $data['bird_count_inspected'],
                'healthy_birds' => $data['healthy_birds'],
                'sick_birds' => $data['sick_birds'] ?? 0,
                'mortality_count' => $data['mortality_count'] ?? 0,
                'culled_count' => $data['culled_count'] ?? 0,
                'symptoms_observed' => $data['symptoms_observed'] ?? null,
                'diseases_suspected' => $data['diseases_suspected'] ?? null,
                'body_condition_score' => $data['body_condition_score'] ?? null,
                'behavior_assessment' => $data['behavior_assessment'] ?? null,
                'appetite_level' => $data['appetite_level'] ?? null,
                'water_consumption_normal' => $data['water_consumption_normal'] ?? true,
                'egg_production_normal' => $data['egg_production_normal'] ?? true,
                'environmental_factors' => $data['environmental_factors'] ?? null,
                'temperature' => $data['temperature'] ?? null,
                'humidity' => $data['humidity'] ?? null,
                'air_quality' => $data['air_quality'] ?? null,
                'cleanliness_score' => $data['cleanliness_score'] ?? null,
                'biosecurity_compliance' => $data['biosecurity_compliance'] ?? null,
                'recommendations' => $data['recommendations'] ?? null,
                'follow_up_required' => $data['follow_up_required'] ?? false,
                'follow_up_date' => $data['follow_up_date'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => 'recorded',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Update flock population if there are mortalities or culls
            $record->updateFlockHealth();

            // Create treatment records if needed
            $record->createTreatmentIfNeeded();

            // Check for health issues and trigger alerts
            if ($record->risk_level === 'high') {
                event(new HealthIssueDetected($record));
            }

            return $record;
        });
    }

    public function recordVaccination(array $data): VaccinationRecord
    {
        return DB::transaction(function () use ($data) {
            $record = VaccinationRecord::create([
                'uuid' => Str::uuid(),
                'vaccination_date' => $data['vaccination_date'],
                'flock_id' => $data['flock_id'],
                'house_id' => $data['house_id'],
                'vaccination_program_id' => $data['vaccination_program_id'] ?? null,
                'vaccine_id' => $data['vaccine_id'],
                'batch_number' => $data['batch_number'],
                'expiry_date' => $data['expiry_date'],
                'birds_vaccinated' => $data['birds_vaccinated'],
                'dosage_per_bird' => $data['dosage_per_bird'],
                'total_dosage' => $data['birds_vaccinated'] * $data['dosage_per_bird'],
                'administration_method' => $data['administration_method'],
                'vaccination_age_weeks' => $data['vaccination_age_weeks'],
                'veterinarian_id' => $data['veterinarian_id'] ?? null,
                'vaccinator_id' => $data['vaccinator_id'] ?? auth()->id(),
                'vaccine_temperature' => $data['vaccine_temperature'] ?? null,
                'storage_conditions_met' => $data['storage_conditions_met'] ?? true,
                'efficacy_expected' => $data['efficacy_expected'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => 'completed',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // Schedule next vaccination if part of a program
            $record->scheduleNextVaccination();

            event(new VaccinationCompleted($record));

            return $record;
        });
    }

    public function recordTreatment(array $data): TreatmentRecord
    {
        return DB::transaction(function () use ($data) {
            $record = TreatmentRecord::create([
                'uuid' => Str::uuid(),
                'treatment_date' => $data['treatment_date'],
                'flock_id' => $data['flock_id'],
                'house_id' => $data['house_id'] ?? null,
                'health_record_id' => $data['health_record_id'] ?? null,
                'disease_id' => $data['disease_id'] ?? null,
                'symptoms' => $data['symptoms'] ?? null,
                'diagnosis' => $data['diagnosis'] ?? null,
                'treatment_type' => $data['treatment_type'],
                'medication_id' => $data['medication_id'] ?? null,
                'dosage' => $data['dosage'] ?? null,
                'administration_method' => $data['administration_method'] ?? null,
                'treatment_duration_days' => $data['treatment_duration_days'] ?? null,
                'birds_treated' => $data['birds_treated'],
                'veterinarian_id' => $data['veterinarian_id'] ?? null,
                'treatment_cost' => $data['treatment_cost'] ?? 0,
                'withdrawal_period_days' => $data['withdrawal_period_days'] ?? 0,
                'withdrawal_end_date' => $data['withdrawal_period_days'] 
                    ? now()->parse($data['treatment_date'])->addDays($data['withdrawal_period_days'])
                    : null,
                'treatment_outcome' => $data['treatment_outcome'] ?? null,
                'side_effects' => $data['side_effects'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => 'ongoing',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            return $record;
        });
    }

    public function declareOutbreak(array $data): DiseaseOutbreak
    {
        return DB::transaction(function () use ($data) {
            $outbreak = DiseaseOutbreak::create([
                'uuid' => Str::uuid(),
                'outbreak_number' => DiseaseOutbreak::generateOutbreakNumber(),
                'disease_id' => $data['disease_id'],
                'farm_id' => $data['farm_id'],
                'house_id' => $data['house_id'] ?? null,
                'flock_id' => $data['flock_id'] ?? null,
                'outbreak_date' => $data['outbreak_date'],
                'detection_date' => $data['detection_date'] ?? $data['outbreak_date'],
                'initial_cases' => $data['initial_cases'],
                'total_birds_at_risk' => $data['total_birds_at_risk'],
                'mortality_count' => $data['mortality_count'] ?? 0,
                'morbidity_count' => $data['morbidity_count'] ?? $data['initial_cases'],
                'suspected_source' => $data['suspected_source'] ?? null,
                'transmission_route' => $data['transmission_route'] ?? null,
                'control_measures' => $data['control_measures'] ?? null,
                'quarantine_imposed' => $data['quarantine_imposed'] ?? false,
                'quarantine_start_date' => $data['quarantine_start_date'] ?? null,
                'quarantine_end_date' => $data['quarantine_end_date'] ?? null,
                'veterinarian_id' => $data['veterinarian_id'] ?? null,
                'authorities_notified' => $data['authorities_notified'] ?? false,
                'notification_date' => $data['notification_date'] ?? null,
                'economic_impact' => $data['economic_impact'] ?? 0,
                'notes' => $data['notes'] ?? null,
                'status' => 'active',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            event(new DiseaseOutbreakDeclared($outbreak));

            return $outbreak;
        });
    }

    public function getHealthAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $filters['end_date'] ?? now()->endOfMonth()->toDateString();
        $flockId = $filters['flock_id'] ?? null;
        $houseId = $filters['house_id'] ?? null;

        $healthQuery = HealthRecord::byDateRange($startDate, $endDate);
        $vaccinationQuery = VaccinationRecord::byDateRange($startDate, $endDate);

        if ($flockId) {
            $healthQuery->where('flock_id', $flockId);
            $vaccinationQuery->where('flock_id', $flockId);
        }

        if ($houseId) {
            $healthQuery->where('house_id', $houseId);
            $vaccinationQuery->where('house_id', $houseId);
        }

        $healthRecords = $healthQuery->get();
        $vaccinationRecords = $vaccinationQuery->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'health_summary' => [
                'total_inspections' => $healthRecords->count(),
                'total_birds_inspected' => $healthRecords->sum('bird_count_inspected'),
                'total_mortality' => $healthRecords->sum('mortality_count'),
                'total_morbidity' => $healthRecords->sum('sick_birds'),
                'average_health_score' => $healthRecords->avg('overall_health_score'),
                'average_mortality_rate' => $healthRecords->avg('mortality_rate'),
                'average_morbidity_rate' => $healthRecords->avg('morbidity_rate'),
                'high_risk_inspections' => $healthRecords->where('risk_level', 'high')->count(),
            ],
            'vaccination_summary' => [
                'total_vaccinations' => $vaccinationRecords->count(),
                'total_birds_vaccinated' => $vaccinationRecords->sum('birds_vaccinated'),
                'total_vaccination_cost' => $vaccinationRecords->sum('total_cost'),
                'average_coverage' => $vaccinationRecords->avg('vaccination_coverage'),
                'adverse_reactions' => $vaccinationRecords->sum('reaction_count'),
            ],
            'health_trends' => $this->getHealthTrends($healthRecords),
            'disease_patterns' => $this->getDiseasePatterns($healthRecords),
            'vaccination_compliance' => $this->getVaccinationCompliance($vaccinationRecords),
        ];
    }

    private function getHealthTrends($records): array
    {
        return $records->groupBy('record_date')
            ->map(function ($dayRecords, $date) {
                return [
                    'date' => $date,
                    'mortality_rate' => $dayRecords->avg('mortality_rate'),
                    'morbidity_rate' => $dayRecords->avg('morbidity_rate'),
                    'health_score' => $dayRecords->avg('overall_health_score'),
                    'inspections' => $dayRecords->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    private function getDiseasePatterns($records): array
    {
        $diseaseData = [];
        
        foreach ($records as $record) {
            if ($record->diseases_suspected) {
                foreach ($record->diseases_suspected as $disease) {
                    $diseaseName = $disease['name'] ?? 'Unknown';
                    if (!isset($diseaseData[$diseaseName])) {
                        $diseaseData[$diseaseName] = [
                            'disease_name' => $diseaseName,
                            'occurrence_count' => 0,
                            'total_birds_affected' => 0,
                            'total_mortality' => 0,
                        ];
                    }
                    
                    $diseaseData[$diseaseName]['occurrence_count']++;
                    $diseaseData[$diseaseName]['total_birds_affected'] += $record->sick_birds;
                    $diseaseData[$diseaseName]['total_mortality'] += $record->mortality_count;
                }
            }
        }

        return array_values($diseaseData);
    }

    private function getVaccinationCompliance($records): array
    {
        return $records->groupBy('vaccine_id')
            ->map(function ($vaccineRecords) {
                $vaccine = $vaccineRecords->first()->vaccine;
                return [
                    'vaccine_name' => $vaccine->name,
                    'total_administrations' => $vaccineRecords->count(),
                    'total_birds_vaccinated' => $vaccineRecords->sum('birds_vaccinated'),
                    'average_coverage' => $vaccineRecords->avg('vaccination_coverage'),
                    'adverse_reaction_rate' => $vaccineRecords->avg('adverse_reaction_rate'),
                ];
            })
            ->values()
            ->toArray();
    }

    public function getUpcomingVaccinations(int $days = 7): array
    {
        return VaccinationRecord::dueForNext()
            ->with(['flock', 'house', 'vaccine'])
            ->get()
            ->map(function ($record) {
                return [
                    'flock_number' => $record->flock->flock_number,
                    'house_name' => $record->house->name,
                    'vaccine_name' => $record->vaccine->name,
                    'due_date' => $record->next_vaccination_date,
                    'days_until_due' => now()->diffInDays($record->next_vaccination_date),
                    'birds_to_vaccinate' => $record->flock->current_count,
                ];
            })
            ->toArray();
    }
}
```

## ⚛️ STEP 2: FRONTEND IMPLEMENTATION

### **2.1 Create Frontend Structure**

```bash
# Create health management structure
mkdir -p frontend/src/modules/health-management/{components,pages,hooks,services,types}
```

### **2.2 Health Management Service**

```typescript
// frontend/src/modules/health-management/services/healthManagementService.ts
import { apiClient } from '@/core/api/apiClient';

export interface HealthRecord {
  id: number;
  uuid: string;
  record_date: string;
  flock: any;
  house: any;
  inspection_type: 'routine' | 'targeted' | 'emergency';
  inspector?: any;
  bird_count_inspected: number;
  healthy_birds: number;
  sick_birds: number;
  mortality_count: number;
  culled_count: number;
  symptoms_observed?: any;
  diseases_suspected?: any;
  body_condition_score?: number;
  behavior_assessment?: number;
  appetite_level?: number;
  water_consumption_normal: boolean;
  egg_production_normal: boolean;
  environmental_factors?: any;
  temperature?: number;
  humidity?: number;
  air_quality?: number;
  cleanliness_score?: number;
  biosecurity_compliance?: number;
  health_percentage: number;
  mortality_rate: number;
  morbidity_rate: number;
  overall_health_score: number;
  risk_level: 'low' | 'medium' | 'high';
  recommendations?: string;
  follow_up_required: boolean;
  follow_up_date?: string;
  notes?: string;
  status: 'recorded' | 'reviewed' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface VaccinationRecord {
  id: number;
  uuid: string;
  vaccination_date: string;
  flock: any;
  house: any;
  vaccination_program?: any;
  vaccine: Vaccine;
  batch_number: string;
  expiry_date: string;
  birds_vaccinated: number;
  dosage_per_bird: number;
  total_dosage: number;
  administration_method: 'injection' | 'drinking_water' | 'spray' | 'eye_drop';
  vaccination_age_weeks: number;
  veterinarian?: any;
  vaccinator?: any;
  vaccine_temperature?: number;
  storage_conditions_met: boolean;
  vaccination_coverage: number;
  adverse_reactions?: any;
  reaction_count: number;
  adverse_reaction_rate: number;
  cost_per_bird: number;
  total_cost: number;
  efficacy_expected?: number;
  next_vaccination_date?: string;
  notes?: string;
  status: 'scheduled' | 'completed' | 'delayed';
  created_at: string;
  updated_at: string;
}

export interface Vaccine {
  id: number;
  name: string;
  manufacturer: string;
  type: 'live' | 'killed' | 'recombinant';
  target_diseases: string[];
  storage_temperature_min: number;
  storage_temperature_max: number;
  shelf_life_months: number;
  cost_per_dose: number;
}

export interface TreatmentRecord {
  id: number;
  uuid: string;
  treatment_date: string;
  flock: any;
  house?: any;
  health_record?: HealthRecord;
  disease?: any;
  symptoms?: any;
  diagnosis?: string;
  treatment_type: 'medication' | 'vaccination' | 'isolation' | 'supportive';
  medication?: any;
  dosage?: string;
  administration_method?: string;
  treatment_duration_days?: number;
  birds_treated: number;
  veterinarian?: any;
  treatment_cost: number;
  withdrawal_period_days: number;
  withdrawal_end_date?: string;
  treatment_outcome?: 'successful' | 'partial' | 'failed';
  side_effects?: any;
  notes?: string;
  status: 'planned' | 'ongoing' | 'completed' | 'discontinued';
  created_at: string;
  updated_at: string;
}

class HealthManagementService {
  // Health Records
  async getHealthRecords(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/health-records?${params.toString()}`);
  }

  async getHealthRecord(id: number, includes: string[] = []) {
    const params = includes.length > 0 ? `?include=${includes.join(',')}` : '';
    return apiClient.get(`/health-records/${id}${params}`);
  }

  async createHealthRecord(data: {
    record_date: string;
    flock_id: number;
    house_id: number;
    inspection_type: string;
    inspector_id?: number;
    bird_count_inspected: number;
    healthy_birds: number;
    sick_birds?: number;
    mortality_count?: number;
    culled_count?: number;
    symptoms_observed?: any;
    diseases_suspected?: any;
    body_condition_score?: number;
    behavior_assessment?: number;
    appetite_level?: number;
    water_consumption_normal?: boolean;
    egg_production_normal?: boolean;
    environmental_factors?: any;
    temperature?: number;
    humidity?: number;
    air_quality?: number;
    cleanliness_score?: number;
    biosecurity_compliance?: number;
    recommendations?: string;
    follow_up_required?: boolean;
    follow_up_date?: string;
    notes?: string;
  }) {
    return apiClient.post('/health-records', data);
  }

  async updateHealthRecord(id: number, data: any) {
    return apiClient.put(`/health-records/${id}`, data);
  }

  async deleteHealthRecord(id: number) {
    return apiClient.delete(`/health-records/${id}`);
  }

  async getHealthSummary(date?: string) {
    const params = date ? `?date=${date}` : '';
    return apiClient.get(`/health-records/summary${params}`);
  }

  // Vaccination Records
  async getVaccinationRecords(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/vaccination-records?${params.toString()}`);
  }

  async recordVaccination(data: {
    vaccination_date: string;
    flock_id: number;
    house_id: number;
    vaccination_program_id?: number;
    vaccine_id: number;
    batch_number: string;
    expiry_date: string;
    birds_vaccinated: number;
    dosage_per_bird: number;
    administration_method: string;
    vaccination_age_weeks: number;
    veterinarian_id?: number;
    vaccinator_id?: number;
    vaccine_temperature?: number;
    storage_conditions_met?: boolean;
    efficacy_expected?: number;
    notes?: string;
  }) {
    return apiClient.post('/vaccination-records', data);
  }

  async getVaccinationSchedule(flockId: number, startDate: string, endDate: string) {
    return apiClient.get(`/vaccination-schedule?flock_id=${flockId}&start_date=${startDate}&end_date=${endDate}`);
  }

  async getUpcomingVaccinations(days: number = 7) {
    return apiClient.get(`/vaccination-records/upcoming?days=${days}`);
  }

  // Treatment Records
  async getTreatmentRecords(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/treatment-records?${params.toString()}`);
  }

  async recordTreatment(data: {
    treatment_date: string;
    flock_id: number;
    house_id?: number;
    health_record_id?: number;
    disease_id?: number;
    symptoms?: any;
    diagnosis?: string;
    treatment_type: string;
    medication_id?: number;
    dosage?: string;
    administration_method?: string;
    treatment_duration_days?: number;
    birds_treated: number;
    veterinarian_id?: number;
    treatment_cost?: number;
    withdrawal_period_days?: number;
    notes?: string;
  }) {
    return apiClient.post('/treatment-records', data);
  }

  // Disease Management
  async getDiseaseOutbreaks(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/disease-outbreaks?${params.toString()}`);
  }

  async declareOutbreak(data: {
    disease_id: number;
    farm_id: number;
    house_id?: number;
    flock_id?: number;
    outbreak_date: string;
    detection_date?: string;
    initial_cases: number;
    total_birds_at_risk: number;
    mortality_count?: number;
    morbidity_count?: number;
    suspected_source?: string;
    transmission_route?: string;
    control_measures?: any;
    quarantine_imposed?: boolean;
    quarantine_start_date?: string;
    quarantine_end_date?: string;
    veterinarian_id?: number;
    authorities_notified?: boolean;
    notification_date?: string;
    economic_impact?: number;
    notes?: string;
  }) {
    return apiClient.post('/disease-outbreaks', data);
  }

  // Analytics & Reports
  async getHealthAnalytics(filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/health-management/analytics?${params.toString()}`);
  }

  async getHealthReport(reportType: string, filters: any = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get(`/health-management/reports/${reportType}?${params.toString()}`);
  }

  // Master Data
  async getVaccines() {
    return apiClient.get('/vaccines');
  }

  async getDiseases() {
    return apiClient.get('/diseases');
  }

  async getMedications() {
    return apiClient.get('/medications');
  }

  async getVeterinarians() {
    return apiClient.get('/veterinarians');
  }
}

export const healthManagementService = new HealthManagementService();
```

## 🔗 STEP 3: MODULE REGISTRATION

### **3.1 Register Routes**

```php
<?php
// Modules/HealthManagement/Routes/api.php

use Modules\HealthManagement\Http\Controllers\VaccinationController;
use Modules\HealthManagement\Http\Controllers\HealthRecordController;
use Modules\HealthManagement\Http\Controllers\TreatmentController;
use Modules\HealthManagement\Http\Controllers\DiseaseController;
use Modules\HealthManagement\Http\Controllers\HealthReportController;

Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // Health Records
    Route::apiResource('health-records', HealthRecordController::class);
    Route::get('health-records/summary', [HealthRecordController::class, 'summary']);
    
    // Vaccination Records
    Route::apiResource('vaccination-records', VaccinationController::class);
    Route::get('vaccination-schedule', [VaccinationController::class, 'schedule']);
    Route::get('vaccination-records/upcoming', [VaccinationController::class, 'upcoming']);
    Route::post('vaccination-records/{record}/adverse-reaction', [VaccinationController::class, 'recordAdverseReaction']);
    
    // Treatment Records
    Route::apiResource('treatment-records', TreatmentController::class);
    Route::post('treatment-records/{record}/outcome', [TreatmentController::class, 'recordOutcome']);
    
    // Disease Management
    Route::apiResource('disease-outbreaks', DiseaseController::class);
    Route::post('disease-outbreaks/{outbreak}/control-measure', [DiseaseController::class, 'addControlMeasure']);
    Route::post('disease-outbreaks/{outbreak}/close', [DiseaseController::class, 'closeOutbreak']);
    
    // Health Analytics & Reports
    Route::prefix('health-management')->group(function () {
        Route::get('analytics', [HealthReportController::class, 'analytics']);
        Route::get('reports/{type}', [HealthReportController::class, 'report']);
    });
    
    // Master Data
    Route::get('vaccines', [VaccinationController::class, 'vaccines']);
    Route::get('diseases', [DiseaseController::class, 'diseases']);
    Route::get('medications', [TreatmentController::class, 'medications']);
    Route::get('veterinarians', [HealthRecordController::class, 'veterinarians']);
});
```

## ✅ VERIFICATION CHECKLIST

### **Backend**
- [ ] Health record CRUD working
- [ ] Vaccination scheduling functional
- [ ] Treatment recording working
- [ ] Disease outbreak management
- [ ] Health analytics accurate
- [ ] Integration dengan poultry management

### **Frontend**
- [ ] Health inspection interface
- [ ] Vaccination management
- [ ] Treatment recording
- [ ] Disease tracking
- [ ] Health analytics dashboard
- [ ] Alert system working

### **Integration**
- [ ] Flock population updates
- [ ] Production correlation working
- [ ] Alert notifications functional
- [ ] Compliance tracking accurate
- [ ] Cost calculations correct

## 📞 NEXT STEPS

Setelah Health Management module selesai:

1. **Test health workflow** end-to-end
2. **Verify vaccination scheduling**
3. **Test disease outbreak** management
4. **Validate health analytics**
5. **Commit module** ke repository
6. **Lanjut ke** `12_FINANCIAL_REPORTING.md`

---

**IMPORTANT**: Health Management adalah critical module untuk animal welfare dan compliance. Pastikan semua tracking akurat dan alert system berfungsi dengan baik untuk early detection dan prevention.
